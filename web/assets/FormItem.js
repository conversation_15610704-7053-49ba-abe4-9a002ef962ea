import{$ as le,aG as Ue,w as Te,al as Je,P as Ze,Z as We,I as C,R as V,J as te,d as Ne,L as Be,V as we,r as U,W as Re,O as E,ae as _e,c0 as Ge,K as pe,b as S,bI as xe,bL as He,X as ve,bG as $e,c1 as Xe,e as Qe,Y as et,bl as tt,ab as nt,at as D,bt as Ae}from"./index.js";function rt(r,e,t){var n;const i=le(r,null);if(i===null)return;const o=(n=Ue())===null||n===void 0?void 0:n.proxy;Te(t,a),a(t.value),Je(()=>{a(void 0,t.value)});function a(d,l){if(!i)return;const m=i[e];l!==void 0&&s(m,l),d!==void 0&&f(m,d)}function s(d,l){d[l]||(d[l]=[]),d[l].splice(d[l].findIndex(m=>m===o),1)}function f(d,l){d[l]||(d[l]=[]),~d[l].findIndex(m=>m===o)||d[l].push(o)}}const it={feedbackPadding:"4px 0 0 2px",feedbackHeightSmall:"24px",feedbackHeightMedium:"24px",feedbackHeightLarge:"26px",feedbackFontSizeSmall:"13px",feedbackFontSizeMedium:"14px",feedbackFontSizeLarge:"14px",labelFontSizeLeftSmall:"14px",labelFontSizeLeftMedium:"14px",labelFontSizeLeftLarge:"15px",labelFontSizeTopSmall:"13px",labelFontSizeTopMedium:"14px",labelFontSizeTopLarge:"14px",labelHeightSmall:"24px",labelHeightMedium:"26px",labelHeightLarge:"28px",labelPaddingVertical:"0 0 6px 2px",labelPaddingHorizontal:"0 12px 0 0",labelTextAlignVertical:"left",labelTextAlignHorizontal:"right",labelFontWeight:"400"};function at(r){const{heightSmall:e,heightMedium:t,heightLarge:n,textColor1:i,errorColor:o,warningColor:a,lineHeight:s,textColor3:f}=r;return Object.assign(Object.assign({},it),{blankHeightSmall:e,blankHeightMedium:t,blankHeightLarge:n,lineHeight:s,labelTextColor:i,asteriskColor:o,feedbackTextColorError:o,feedbackTextColorWarning:a,feedbackTextColor:f})}const ot={name:"Form",common:Ze,self:at},De=ot,de=We("n-form"),Ye=We("n-form-item-insts"),st=C("form",[V("inline",`
 width: 100%;
 display: inline-flex;
 align-items: flex-start;
 align-content: space-around;
 `,[C("form-item",{width:"auto",marginRight:"18px"},[te("&:last-child",{marginRight:0})])])]);var ft=globalThis&&globalThis.__awaiter||function(r,e,t,n){function i(o){return o instanceof t?o:new t(function(a){a(o)})}return new(t||(t=Promise))(function(o,a){function s(l){try{d(n.next(l))}catch(m){a(m)}}function f(l){try{d(n.throw(l))}catch(m){a(m)}}function d(l){l.done?o(l.value):i(l.value).then(s,f)}d((n=n.apply(r,e||[])).next())})};const lt=Object.assign(Object.assign({},we.props),{inline:Boolean,labelWidth:[Number,String],labelAlign:String,labelPlacement:{type:String,default:"top"},model:{type:Object,default:()=>{}},rules:Object,disabled:Boolean,size:String,showRequireMark:{type:Boolean,default:void 0},requireMarkPlacement:String,showFeedback:{type:Boolean,default:!0},onSubmit:{type:Function,default:r=>{r.preventDefault()}},showLabel:{type:Boolean,default:void 0},validateMessages:Object}),Jt=Ne({name:"Form",props:lt,setup(r){const{mergedClsPrefixRef:e}=Be(r);we("Form","-form",st,De,r,e);const t={},n=U(void 0),i=f=>{const d=n.value;(d===void 0||f>=d)&&(n.value=f)};function o(f){return ft(this,arguments,void 0,function*(d,l=()=>!0){return yield new Promise((m,b)=>{const q=[];for(const c of _e(t)){const g=t[c];for(const v of g)v.path&&q.push(v.internalValidate(null,l))}Promise.all(q).then(c=>{const g=c.some(R=>!R.valid),v=[],u=[];c.forEach(R=>{var h,y;!((h=R.errors)===null||h===void 0)&&h.length&&v.push(R.errors),!((y=R.warnings)===null||y===void 0)&&y.length&&u.push(R.warnings)}),d&&d(v.length?v:void 0,{warnings:u.length?u:void 0}),g?b(v.length?v:void 0):m({warnings:u.length?u:void 0})})})})}function a(){for(const f of _e(t)){const d=t[f];for(const l of d)l.restoreValidation()}}return Re(de,{props:r,maxChildLabelWidthRef:n,deriveMaxChildLabelWidth:i}),Re(Ye,{formItems:t}),Object.assign({validate:o,restoreValidation:a},{mergedClsPrefix:e})},render(){const{mergedClsPrefix:r}=this;return E("form",{class:[`${r}-form`,this.inline&&`${r}-form--inline`],onSubmit:this.onSubmit},this.$slots)}});function J(){return J=Object.assign?Object.assign.bind():function(r){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(r[n]=t[n])}return r},J.apply(this,arguments)}function dt(r,e){r.prototype=Object.create(e.prototype),r.prototype.constructor=r,fe(r,e)}function qe(r){return qe=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},qe(r)}function fe(r,e){return fe=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},fe(r,e)}function ut(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function ye(r,e,t){return ut()?ye=Reflect.construct.bind():ye=function(i,o,a){var s=[null];s.push.apply(s,o);var f=Function.bind.apply(i,s),d=new f;return a&&fe(d,a.prototype),d},ye.apply(null,arguments)}function ct(r){return Function.toString.call(r).indexOf("[native code]")!==-1}function Fe(r){var e=typeof Map=="function"?new Map:void 0;return Fe=function(n){if(n===null||!ct(n))return n;if(typeof n!="function")throw new TypeError("Super expression must either be null or a function");if(typeof e<"u"){if(e.has(n))return e.get(n);e.set(n,i)}function i(){return ye(n,arguments,qe(this).constructor)}return i.prototype=Object.create(n.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),fe(i,n)},Fe(r)}var mt=/%[sdj%]/g,gt=function(){};typeof process<"u"&&process.env;function Se(r){if(!r||!r.length)return null;var e={};return r.forEach(function(t){var n=t.field;e[n]=e[n]||[],e[n].push(t)}),e}function M(r){for(var e=arguments.length,t=new Array(e>1?e-1:0),n=1;n<e;n++)t[n-1]=arguments[n];var i=0,o=t.length;if(typeof r=="function")return r.apply(null,t);if(typeof r=="string"){var a=r.replace(mt,function(s){if(s==="%%")return"%";if(i>=o)return s;switch(s){case"%s":return String(t[i++]);case"%d":return Number(t[i++]);case"%j":try{return JSON.stringify(t[i++])}catch{return"[Circular]"}break;default:return s}});return a}return r}function ht(r){return r==="string"||r==="url"||r==="hex"||r==="email"||r==="date"||r==="pattern"}function F(r,e){return!!(r==null||e==="array"&&Array.isArray(r)&&!r.length||ht(e)&&typeof r=="string"&&!r)}function pt(r,e,t){var n=[],i=0,o=r.length;function a(s){n.push.apply(n,s||[]),i++,i===o&&t(n)}r.forEach(function(s){e(s,a)})}function Ee(r,e,t){var n=0,i=r.length;function o(a){if(a&&a.length){t(a);return}var s=n;n=n+1,s<i?e(r[s],o):t([])}o([])}function vt(r){var e=[];return Object.keys(r).forEach(function(t){e.push.apply(e,r[t]||[])}),e}var je=function(r){dt(e,r);function e(t,n){var i;return i=r.call(this,"Async Validation Error")||this,i.errors=t,i.fields=n,i}return e}(Fe(Error));function bt(r,e,t,n,i){if(e.first){var o=new Promise(function(b,q){var c=function(u){return n(u),u.length?q(new je(u,Se(u))):b(i)},g=vt(r);Ee(g,t,c)});return o.catch(function(b){return b}),o}var a=e.firstFields===!0?Object.keys(r):e.firstFields||[],s=Object.keys(r),f=s.length,d=0,l=[],m=new Promise(function(b,q){var c=function(v){if(l.push.apply(l,v),d++,d===f)return n(l),l.length?q(new je(l,Se(l))):b(i)};s.length||(n(l),b(i)),s.forEach(function(g){var v=r[g];a.indexOf(g)!==-1?Ee(v,t,c):pt(v,t,c)})});return m.catch(function(b){return b}),m}function yt(r){return!!(r&&r.message!==void 0)}function wt(r,e){for(var t=r,n=0;n<e.length;n++){if(t==null)return t;t=t[e[n]]}return t}function Me(r,e){return function(t){var n;return r.fullFields?n=wt(e,r.fullFields):n=e[t.field||r.fullField],yt(t)?(t.field=t.field||r.fullField,t.fieldValue=n,t):{message:typeof t=="function"?t():t,fieldValue:n,field:t.field||r.fullField}}}function Le(r,e){if(e){for(var t in e)if(e.hasOwnProperty(t)){var n=e[t];typeof n=="object"&&typeof r[t]=="object"?r[t]=J({},r[t],n):r[t]=n}}return r}var Ke=function(e,t,n,i,o,a){e.required&&(!n.hasOwnProperty(e.field)||F(t,a||e.type))&&i.push(M(o.messages.required,e.fullField))},xt=function(e,t,n,i,o){(/^\s+$/.test(t)||t==="")&&i.push(M(o.messages.whitespace,e.fullField))},be,kt=function(){if(be)return be;var r="[a-fA-F\\d:]",e=function(y){return y&&y.includeBoundaries?"(?:(?<=\\s|^)(?="+r+")|(?<="+r+")(?=\\s|$))":""},t="(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}",n="[a-fA-F\\d]{1,4}",i=(`
(?:
(?:`+n+":){7}(?:"+n+`|:)|                                    // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8
(?:`+n+":){6}(?:"+t+"|:"+n+`|:)|                             // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::1.2.3.4
(?:`+n+":){5}(?::"+t+"|(?::"+n+`){1,2}|:)|                   // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::7:1.2.3.4
(?:`+n+":){4}(?:(?::"+n+"){0,1}:"+t+"|(?::"+n+`){1,3}|:)| // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::6:7:1.2.3.4
(?:`+n+":){3}(?:(?::"+n+"){0,2}:"+t+"|(?::"+n+`){1,4}|:)| // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::5:6:7:1.2.3.4
(?:`+n+":){2}(?:(?::"+n+"){0,3}:"+t+"|(?::"+n+`){1,5}|:)| // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::4:5:6:7:1.2.3.4
(?:`+n+":){1}(?:(?::"+n+"){0,4}:"+t+"|(?::"+n+`){1,6}|:)| // 1::              1::3:4:5:6:7:8   1::8            1::3:4:5:6:7:1.2.3.4
(?::(?:(?::`+n+"){0,5}:"+t+"|(?::"+n+`){1,7}|:))             // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::1.2.3.4
)(?:%[0-9a-zA-Z]{1,})?                                             // %eth0            %1
`).replace(/\s*\/\/.*$/gm,"").replace(/\n/g,"").trim(),o=new RegExp("(?:^"+t+"$)|(?:^"+i+"$)"),a=new RegExp("^"+t+"$"),s=new RegExp("^"+i+"$"),f=function(y){return y&&y.exact?o:new RegExp("(?:"+e(y)+t+e(y)+")|(?:"+e(y)+i+e(y)+")","g")};f.v4=function(h){return h&&h.exact?a:new RegExp(""+e(h)+t+e(h),"g")},f.v6=function(h){return h&&h.exact?s:new RegExp(""+e(h)+i+e(h),"g")};var d="(?:(?:[a-z]+:)?//)",l="(?:\\S+(?::\\S*)?@)?",m=f.v4().source,b=f.v6().source,q="(?:(?:[a-z\\u00a1-\\uffff0-9][-_]*)*[a-z\\u00a1-\\uffff0-9]+)",c="(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*",g="(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))",v="(?::\\d{2,5})?",u='(?:[/?#][^\\s"]*)?',R="(?:"+d+"|www\\.)"+l+"(?:localhost|"+m+"|"+b+"|"+q+c+g+")"+v+u;return be=new RegExp("(?:^"+R+"$)","i"),be},ze={email:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/,hex:/^#?([a-f0-9]{6}|[a-f0-9]{3})$/i},oe={integer:function(e){return oe.number(e)&&parseInt(e,10)===e},float:function(e){return oe.number(e)&&!oe.integer(e)},array:function(e){return Array.isArray(e)},regexp:function(e){if(e instanceof RegExp)return!0;try{return!!new RegExp(e)}catch{return!1}},date:function(e){return typeof e.getTime=="function"&&typeof e.getMonth=="function"&&typeof e.getYear=="function"&&!isNaN(e.getTime())},number:function(e){return isNaN(e)?!1:typeof e=="number"},object:function(e){return typeof e=="object"&&!oe.array(e)},method:function(e){return typeof e=="function"},email:function(e){return typeof e=="string"&&e.length<=320&&!!e.match(ze.email)},url:function(e){return typeof e=="string"&&e.length<=2048&&!!e.match(kt())},hex:function(e){return typeof e=="string"&&!!e.match(ze.hex)}},Rt=function(e,t,n,i,o){if(e.required&&t===void 0){Ke(e,t,n,i,o);return}var a=["integer","float","array","regexp","object","method","email","number","date","url","hex"],s=e.type;a.indexOf(s)>-1?oe[s](t)||i.push(M(o.messages.types[s],e.fullField,e.type)):s&&typeof t!==e.type&&i.push(M(o.messages.types[s],e.fullField,e.type))},qt=function(e,t,n,i,o){var a=typeof e.len=="number",s=typeof e.min=="number",f=typeof e.max=="number",d=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,l=t,m=null,b=typeof t=="number",q=typeof t=="string",c=Array.isArray(t);if(b?m="number":q?m="string":c&&(m="array"),!m)return!1;c&&(l=t.length),q&&(l=t.replace(d,"_").length),a?l!==e.len&&i.push(M(o.messages[m].len,e.fullField,e.len)):s&&!f&&l<e.min?i.push(M(o.messages[m].min,e.fullField,e.min)):f&&!s&&l>e.max?i.push(M(o.messages[m].max,e.fullField,e.max)):s&&f&&(l<e.min||l>e.max)&&i.push(M(o.messages[m].range,e.fullField,e.min,e.max))},ee="enum",Ft=function(e,t,n,i,o){e[ee]=Array.isArray(e[ee])?e[ee]:[],e[ee].indexOf(t)===-1&&i.push(M(o.messages[ee],e.fullField,e[ee].join(", ")))},St=function(e,t,n,i,o){if(e.pattern){if(e.pattern instanceof RegExp)e.pattern.lastIndex=0,e.pattern.test(t)||i.push(M(o.messages.pattern.mismatch,e.fullField,t,e.pattern));else if(typeof e.pattern=="string"){var a=new RegExp(e.pattern);a.test(t)||i.push(M(o.messages.pattern.mismatch,e.fullField,t,e.pattern))}}},p={required:Ke,whitespace:xt,type:Rt,range:qt,enum:Ft,pattern:St},Pt=function(e,t,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(F(t,"string")&&!e.required)return n();p.required(e,t,i,a,o,"string"),F(t,"string")||(p.type(e,t,i,a,o),p.range(e,t,i,a,o),p.pattern(e,t,i,a,o),e.whitespace===!0&&p.whitespace(e,t,i,a,o))}n(a)},Ot=function(e,t,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(F(t)&&!e.required)return n();p.required(e,t,i,a,o),t!==void 0&&p.type(e,t,i,a,o)}n(a)},_t=function(e,t,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(t===""&&(t=void 0),F(t)&&!e.required)return n();p.required(e,t,i,a,o),t!==void 0&&(p.type(e,t,i,a,o),p.range(e,t,i,a,o))}n(a)},$t=function(e,t,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(F(t)&&!e.required)return n();p.required(e,t,i,a,o),t!==void 0&&p.type(e,t,i,a,o)}n(a)},At=function(e,t,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(F(t)&&!e.required)return n();p.required(e,t,i,a,o),F(t)||p.type(e,t,i,a,o)}n(a)},Et=function(e,t,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(F(t)&&!e.required)return n();p.required(e,t,i,a,o),t!==void 0&&(p.type(e,t,i,a,o),p.range(e,t,i,a,o))}n(a)},jt=function(e,t,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(F(t)&&!e.required)return n();p.required(e,t,i,a,o),t!==void 0&&(p.type(e,t,i,a,o),p.range(e,t,i,a,o))}n(a)},Mt=function(e,t,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(t==null&&!e.required)return n();p.required(e,t,i,a,o,"array"),t!=null&&(p.type(e,t,i,a,o),p.range(e,t,i,a,o))}n(a)},Lt=function(e,t,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(F(t)&&!e.required)return n();p.required(e,t,i,a,o),t!==void 0&&p.type(e,t,i,a,o)}n(a)},zt="enum",It=function(e,t,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(F(t)&&!e.required)return n();p.required(e,t,i,a,o),t!==void 0&&p[zt](e,t,i,a,o)}n(a)},Vt=function(e,t,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(F(t,"string")&&!e.required)return n();p.required(e,t,i,a,o),F(t,"string")||p.pattern(e,t,i,a,o)}n(a)},Ct=function(e,t,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(F(t,"date")&&!e.required)return n();if(p.required(e,t,i,a,o),!F(t,"date")){var f;t instanceof Date?f=t:f=new Date(t),p.type(e,f,i,a,o),f&&p.range(e,f.getTime(),i,a,o)}}n(a)},Tt=function(e,t,n,i,o){var a=[],s=Array.isArray(t)?"array":typeof t;p.required(e,t,i,a,o,s),n(a)},ke=function(e,t,n,i,o){var a=e.type,s=[],f=e.required||!e.required&&i.hasOwnProperty(e.field);if(f){if(F(t,a)&&!e.required)return n();p.required(e,t,i,s,o,a),F(t,a)||p.type(e,t,i,s,o)}n(s)},Wt=function(e,t,n,i,o){var a=[],s=e.required||!e.required&&i.hasOwnProperty(e.field);if(s){if(F(t)&&!e.required)return n();p.required(e,t,i,a,o)}n(a)},se={string:Pt,method:Ot,number:_t,boolean:$t,regexp:At,integer:Et,float:jt,array:Mt,object:Lt,enum:It,pattern:Vt,date:Ct,url:ke,hex:ke,email:ke,required:Tt,any:Wt};function Pe(){return{default:"Validation error on field %s",required:"%s is required",enum:"%s must be one of %s",whitespace:"%s cannot be empty",date:{format:"%s date %s is invalid for format %s",parse:"%s date could not be parsed, %s is invalid ",invalid:"%s date %s is invalid"},types:{string:"%s is not a %s",method:"%s is not a %s (function)",array:"%s is not an %s",object:"%s is not an %s",number:"%s is not a %s",date:"%s is not a %s",boolean:"%s is not a %s",integer:"%s is not an %s",float:"%s is not a %s",regexp:"%s is not a valid %s",email:"%s is not a valid %s",url:"%s is not a valid %s",hex:"%s is not a valid %s"},string:{len:"%s must be exactly %s characters",min:"%s must be at least %s characters",max:"%s cannot be longer than %s characters",range:"%s must be between %s and %s characters"},number:{len:"%s must equal %s",min:"%s cannot be less than %s",max:"%s cannot be greater than %s",range:"%s must be between %s and %s"},array:{len:"%s must be exactly %s in length",min:"%s cannot be less than %s in length",max:"%s cannot be greater than %s in length",range:"%s must be between %s and %s in length"},pattern:{mismatch:"%s value %s does not match pattern %s"},clone:function(){var e=JSON.parse(JSON.stringify(this));return e.clone=this.clone,e}}}var Oe=Pe(),ne=function(){function r(t){this.rules=null,this._messages=Oe,this.define(t)}var e=r.prototype;return e.define=function(n){var i=this;if(!n)throw new Error("Cannot configure a schema with no rules");if(typeof n!="object"||Array.isArray(n))throw new Error("Rules must be an object");this.rules={},Object.keys(n).forEach(function(o){var a=n[o];i.rules[o]=Array.isArray(a)?a:[a]})},e.messages=function(n){return n&&(this._messages=Le(Pe(),n)),this._messages},e.validate=function(n,i,o){var a=this;i===void 0&&(i={}),o===void 0&&(o=function(){});var s=n,f=i,d=o;if(typeof f=="function"&&(d=f,f={}),!this.rules||Object.keys(this.rules).length===0)return d&&d(null,s),Promise.resolve(s);function l(g){var v=[],u={};function R(y){if(Array.isArray(y)){var O;v=(O=v).concat.apply(O,y)}else v.push(y)}for(var h=0;h<g.length;h++)R(g[h]);v.length?(u=Se(v),d(v,u)):d(null,s)}if(f.messages){var m=this.messages();m===Oe&&(m=Pe()),Le(m,f.messages),f.messages=m}else f.messages=this.messages();var b={},q=f.keys||Object.keys(this.rules);q.forEach(function(g){var v=a.rules[g],u=s[g];v.forEach(function(R){var h=R;typeof h.transform=="function"&&(s===n&&(s=J({},s)),u=s[g]=h.transform(u)),typeof h=="function"?h={validator:h}:h=J({},h),h.validator=a.getValidationMethod(h),h.validator&&(h.field=g,h.fullField=h.fullField||g,h.type=a.getType(h),b[g]=b[g]||[],b[g].push({rule:h,value:u,source:s,field:g}))})});var c={};return bt(b,f,function(g,v){var u=g.rule,R=(u.type==="object"||u.type==="array")&&(typeof u.fields=="object"||typeof u.defaultField=="object");R=R&&(u.required||!u.required&&g.value),u.field=g.field;function h(P,T){return J({},T,{fullField:u.fullField+"."+P,fullFields:u.fullFields?[].concat(u.fullFields,[P]):[P]})}function y(P){P===void 0&&(P=[]);var T=Array.isArray(P)?P:[P];!f.suppressWarning&&T.length&&r.warning("async-validator:",T),T.length&&u.message!==void 0&&(T=[].concat(u.message));var I=T.map(Me(u,s));if(f.first&&I.length)return c[u.field]=1,v(I);if(!R)v(I);else{if(u.required&&!g.value)return u.message!==void 0?I=[].concat(u.message).map(Me(u,s)):f.error&&(I=[f.error(u,M(f.messages.required,u.field))]),v(I);var Y={};u.defaultField&&Object.keys(g.value).map(function(_){Y[_]=u.defaultField}),Y=J({},Y,g.rule.fields);var re={};Object.keys(Y).forEach(function(_){var L=Y[_],w=Array.isArray(L)?L:[L];re[_]=w.map(h.bind(null,_))});var ie=new r(re);ie.messages(f.messages),g.rule.options&&(g.rule.options.messages=f.messages,g.rule.options.error=f.error),ie.validate(g.value,g.rule.options||f,function(_){var L=[];I&&I.length&&L.push.apply(L,I),_&&_.length&&L.push.apply(L,_),v(L.length?L:null)})}}var O;if(u.asyncValidator)O=u.asyncValidator(u,g.value,y,g.source,f);else if(u.validator){try{O=u.validator(u,g.value,y,g.source,f)}catch(P){console.error==null||console.error(P),f.suppressValidatorError||setTimeout(function(){throw P},0),y(P.message)}O===!0?y():O===!1?y(typeof u.message=="function"?u.message(u.fullField||u.field):u.message||(u.fullField||u.field)+" fails"):O instanceof Array?y(O):O instanceof Error&&y(O.message)}O&&O.then&&O.then(function(){return y()},function(P){return y(P)})},function(g){l(g)},s)},e.getType=function(n){if(n.type===void 0&&n.pattern instanceof RegExp&&(n.type="pattern"),typeof n.validator!="function"&&n.type&&!se.hasOwnProperty(n.type))throw new Error(M("Unknown rule type %s",n.type));return n.type||"string"},e.getValidationMethod=function(n){if(typeof n.validator=="function")return n.validator;var i=Object.keys(n),o=i.indexOf("message");return o!==-1&&i.splice(o,1),i.length===1&&i[0]==="required"?se.required:se[this.getType(n)]||void 0},r}();ne.register=function(e,t){if(typeof t!="function")throw new Error("Cannot register a validator by type, validator is not a function");se[e]=t};ne.warning=gt;ne.messages=Oe;ne.validators=se;const{cubicBezierEaseInOut:Ie}=Ge;function Nt({name:r="fade-down",fromOffset:e="-4px",enterDuration:t=".3s",leaveDuration:n=".3s",enterCubicBezier:i=Ie,leaveCubicBezier:o=Ie}={}){return[te(`&.${r}-transition-enter-from, &.${r}-transition-leave-to`,{opacity:0,transform:`translateY(${e})`}),te(`&.${r}-transition-enter-to, &.${r}-transition-leave-from`,{opacity:1,transform:"translateY(0)"}),te(`&.${r}-transition-leave-active`,{transition:`opacity ${n} ${o}, transform ${n} ${o}`}),te(`&.${r}-transition-enter-active`,{transition:`opacity ${t} ${i}, transform ${t} ${i}`})]}const Bt=C("form-item",`
 display: grid;
 line-height: var(--n-line-height);
`,[C("form-item-label",`
 grid-area: label;
 align-items: center;
 line-height: 1.25;
 text-align: var(--n-label-text-align);
 font-size: var(--n-label-font-size);
 min-height: var(--n-label-height);
 padding: var(--n-label-padding);
 color: var(--n-label-text-color);
 transition: color .3s var(--n-bezier);
 box-sizing: border-box;
 font-weight: var(--n-label-font-weight);
 `,[pe("asterisk",`
 white-space: nowrap;
 user-select: none;
 -webkit-user-select: none;
 color: var(--n-asterisk-color);
 transition: color .3s var(--n-bezier);
 `),pe("asterisk-placeholder",`
 grid-area: mark;
 user-select: none;
 -webkit-user-select: none;
 visibility: hidden; 
 `)]),C("form-item-blank",`
 grid-area: blank;
 min-height: var(--n-blank-height);
 `),V("auto-label-width",[C("form-item-label","white-space: nowrap;")]),V("left-labelled",`
 grid-template-areas:
 "label blank"
 "label feedback";
 grid-template-columns: auto minmax(0, 1fr);
 grid-template-rows: auto 1fr;
 align-items: flex-start;
 `,[C("form-item-label",`
 display: grid;
 grid-template-columns: 1fr auto;
 min-height: var(--n-blank-height);
 height: auto;
 box-sizing: border-box;
 flex-shrink: 0;
 flex-grow: 0;
 `,[V("reverse-columns-space",`
 grid-template-columns: auto 1fr;
 `),V("left-mark",`
 grid-template-areas:
 "mark text"
 ". text";
 `),V("right-mark",`
 grid-template-areas: 
 "text mark"
 "text .";
 `),V("right-hanging-mark",`
 grid-template-areas: 
 "text mark"
 "text .";
 `),pe("text",`
 grid-area: text; 
 `),pe("asterisk",`
 grid-area: mark; 
 align-self: end;
 `)])]),V("top-labelled",`
 grid-template-areas:
 "label"
 "blank"
 "feedback";
 grid-template-rows: minmax(var(--n-label-height), auto) 1fr;
 grid-template-columns: minmax(0, 100%);
 `,[V("no-label",`
 grid-template-areas:
 "blank"
 "feedback";
 grid-template-rows: 1fr;
 `),C("form-item-label",`
 display: flex;
 align-items: flex-start;
 justify-content: var(--n-label-text-align);
 `)]),C("form-item-blank",`
 box-sizing: border-box;
 display: flex;
 align-items: center;
 position: relative;
 `),C("form-item-feedback-wrapper",`
 grid-area: feedback;
 box-sizing: border-box;
 min-height: var(--n-feedback-height);
 font-size: var(--n-feedback-font-size);
 line-height: 1.25;
 transform-origin: top left;
 `,[te("&:not(:empty)",`
 padding: var(--n-feedback-padding);
 `),C("form-item-feedback",{transition:"color .3s var(--n-bezier)",color:"var(--n-feedback-text-color)"},[V("warning",{color:"var(--n-feedback-text-color-warning)"}),V("error",{color:"var(--n-feedback-text-color-error)"}),Nt({fromOffset:"-3px",enterDuration:".3s",leaveDuration:".2s"})])])]);function Ht(r){const e=le(de,null);return{mergedSize:S(()=>r.size!==void 0?r.size:(e==null?void 0:e.props.size)!==void 0?e.props.size:"medium")}}function Dt(r){const e=le(de,null),t=S(()=>{const{labelPlacement:c}=r;return c!==void 0?c:e!=null&&e.props.labelPlacement?e.props.labelPlacement:"top"}),n=S(()=>t.value==="left"&&(r.labelWidth==="auto"||(e==null?void 0:e.props.labelWidth)==="auto")),i=S(()=>{if(t.value==="top")return;const{labelWidth:c}=r;if(c!==void 0&&c!=="auto")return xe(c);if(n.value){const g=e==null?void 0:e.maxChildLabelWidthRef.value;return g!==void 0?xe(g):void 0}if((e==null?void 0:e.props.labelWidth)!==void 0)return xe(e.props.labelWidth)}),o=S(()=>{const{labelAlign:c}=r;if(c)return c;if(e!=null&&e.props.labelAlign)return e.props.labelAlign}),a=S(()=>{var c;return[(c=r.labelProps)===null||c===void 0?void 0:c.style,r.labelStyle,{width:i.value}]}),s=S(()=>{const{showRequireMark:c}=r;return c!==void 0?c:e==null?void 0:e.props.showRequireMark}),f=S(()=>{const{requireMarkPlacement:c}=r;return c!==void 0?c:(e==null?void 0:e.props.requireMarkPlacement)||"right"}),d=U(!1),l=U(!1),m=S(()=>{const{validationStatus:c}=r;if(c!==void 0)return c;if(d.value)return"error";if(l.value)return"warning"}),b=S(()=>{const{showFeedback:c}=r;return c!==void 0?c:(e==null?void 0:e.props.showFeedback)!==void 0?e.props.showFeedback:!0}),q=S(()=>{const{showLabel:c}=r;return c!==void 0?c:(e==null?void 0:e.props.showLabel)!==void 0?e.props.showLabel:!0});return{validationErrored:d,validationWarned:l,mergedLabelStyle:a,mergedLabelPlacement:t,mergedLabelAlign:o,mergedShowRequireMark:s,mergedRequireMarkPlacement:f,mergedValidationStatus:m,mergedShowFeedback:b,mergedShowLabel:q,isAutoLabelWidth:n}}function Yt(r){const e=le(de,null),t=S(()=>{const{rulePath:a}=r;if(a!==void 0)return a;const{path:s}=r;if(s!==void 0)return s}),n=S(()=>{const a=[],{rule:s}=r;if(s!==void 0&&(Array.isArray(s)?a.push(...s):a.push(s)),e){const{rules:f}=e.props,{value:d}=t;if(f!==void 0&&d!==void 0){const l=He(f,d);l!==void 0&&(Array.isArray(l)?a.push(...l):a.push(l))}}return a}),i=S(()=>n.value.some(a=>a.required)),o=S(()=>i.value||r.required);return{mergedRules:n,mergedRequired:o}}var Ve=globalThis&&globalThis.__awaiter||function(r,e,t,n){function i(o){return o instanceof t?o:new t(function(a){a(o)})}return new(t||(t=Promise))(function(o,a){function s(l){try{d(n.next(l))}catch(m){a(m)}}function f(l){try{d(n.throw(l))}catch(m){a(m)}}function d(l){l.done?o(l.value):i(l.value).then(s,f)}d((n=n.apply(r,e||[])).next())})};const Kt=Object.assign(Object.assign({},we.props),{label:String,labelWidth:[Number,String],labelStyle:[String,Object],labelAlign:String,labelPlacement:String,path:String,first:Boolean,rulePath:String,required:Boolean,showRequireMark:{type:Boolean,default:void 0},requireMarkPlacement:String,showFeedback:{type:Boolean,default:void 0},rule:[Object,Array],size:String,ignorePathChange:Boolean,validationStatus:String,feedback:String,feedbackClass:String,feedbackStyle:[String,Object],showLabel:{type:Boolean,default:void 0},labelProps:Object});function Ce(r,e){return(...t)=>{try{const n=r(...t);return!e&&(typeof n=="boolean"||n instanceof Error||Array.isArray(n))||n!=null&&n.then?n:(n===void 0||Ae("form-item/validate",`You return a ${typeof n} typed value in the validator method, which is not recommended. Please use ${e?"`Promise`":"`boolean`, `Error` or `Promise`"} typed value instead.`),!0)}catch(n){Ae("form-item/validate","An error is catched in the validation, so the validation won't be done. Your callback in `validate` method of `n-form` or `n-form-item` won't be called in this validation."),console.error(n);return}}}const Zt=Ne({name:"FormItem",props:Kt,setup(r){rt(Ye,"formItems",ve(r,"path"));const{mergedClsPrefixRef:e,inlineThemeDisabled:t}=Be(r),n=le(de,null),i=Ht(r),o=Dt(r),{validationErrored:a,validationWarned:s}=o,{mergedRequired:f,mergedRules:d}=Yt(r),{mergedSize:l}=i,{mergedLabelPlacement:m,mergedLabelAlign:b,mergedRequireMarkPlacement:q}=o,c=U([]),g=U($e()),v=n?ve(n.props,"disabled"):U(!1),u=we("Form","-form-item",Bt,De,r,e);Te(ve(r,"path"),()=>{r.ignorePathChange||R()});function R(){c.value=[],a.value=!1,s.value=!1,r.feedback&&(g.value=$e())}const h=(...w)=>Ve(this,[...w],void 0,function*(j=null,W=()=>!0,$={suppressWarning:!0}){const{path:N}=r;$?$.first||($.first=r.first):$={};const{value:K}=d,Z=n?He(n.props.model,N||""):void 0,G={},X={},B=(j?K.filter(x=>Array.isArray(x.trigger)?x.trigger.includes(j):x.trigger===j):K).filter(W).map((x,A)=>{const k=Object.assign({},x);if(k.validator&&(k.validator=Ce(k.validator,!1)),k.asyncValidator&&(k.asyncValidator=Ce(k.asyncValidator,!0)),k.renderMessage){const he=`__renderMessage__${A}`;X[he]=k.message,k.message=he,G[he]=k.renderMessage}return k}),H=B.filter(x=>x.level!=="warning"),ue=B.filter(x=>x.level==="warning"),z={valid:!0,errors:void 0,warnings:void 0};if(!B.length)return z;const Q=N??"__n_no_path__",ce=new ne({[Q]:H}),me=new ne({[Q]:ue}),{validateMessages:ae}=(n==null?void 0:n.props)||{};ae&&(ce.messages(ae),me.messages(ae));const ge=x=>{c.value=x.map(A=>{const k=(A==null?void 0:A.message)||"";return{key:k,render:()=>k.startsWith("__renderMessage__")?G[k]():k}}),x.forEach(A=>{var k;!((k=A.message)===null||k===void 0)&&k.startsWith("__renderMessage__")&&(A.message=X[A.message])})};if(H.length){const x=yield new Promise(A=>{ce.validate({[Q]:Z},$,A)});x!=null&&x.length&&(z.valid=!1,z.errors=x,ge(x))}if(ue.length&&!z.errors){const x=yield new Promise(A=>{me.validate({[Q]:Z},$,A)});x!=null&&x.length&&(ge(x),z.warnings=x)}return!z.errors&&!z.warnings?R():(a.value=!!z.errors,s.value=!!z.warnings),z});function y(){h("blur")}function O(){h("change")}function P(){h("focus")}function T(){h("input")}function I(w,j){return Ve(this,void 0,void 0,function*(){let W,$,N,K;return typeof w=="string"?(W=w,$=j):w!==null&&typeof w=="object"&&(W=w.trigger,$=w.callback,N=w.shouldRuleBeApplied,K=w.options),yield new Promise((Z,G)=>{h(W,N,K).then(({valid:X,errors:B,warnings:H})=>{X?($&&$(void 0,{warnings:H}),Z({warnings:H})):($&&$(B,{warnings:H}),G(B))})})})}Re(Xe,{path:ve(r,"path"),disabled:v,mergedSize:i.mergedSize,mergedValidationStatus:o.mergedValidationStatus,restoreValidation:R,handleContentBlur:y,handleContentChange:O,handleContentFocus:P,handleContentInput:T});const Y={validate:I,restoreValidation:R,internalValidate:h},re=U(null);Qe(()=>{if(!o.isAutoLabelWidth.value)return;const w=re.value;if(w!==null){const j=w.style.whiteSpace;w.style.whiteSpace="nowrap",w.style.width="",n==null||n.deriveMaxChildLabelWidth(Number(getComputedStyle(w).width.slice(0,-2))),w.style.whiteSpace=j}});const ie=S(()=>{var w;const{value:j}=l,{value:W}=m,$=W==="top"?"vertical":"horizontal",{common:{cubicBezierEaseInOut:N},self:{labelTextColor:K,asteriskColor:Z,lineHeight:G,feedbackTextColor:X,feedbackTextColorWarning:B,feedbackTextColorError:H,feedbackPadding:ue,labelFontWeight:z,[D("labelHeight",j)]:Q,[D("blankHeight",j)]:ce,[D("feedbackFontSize",j)]:me,[D("feedbackHeight",j)]:ae,[D("labelPadding",$)]:ge,[D("labelTextAlign",$)]:x,[D(D("labelFontSize",W),j)]:A}}=u.value;let k=(w=b.value)!==null&&w!==void 0?w:x;return W==="top"&&(k=k==="right"?"flex-end":"flex-start"),{"--n-bezier":N,"--n-line-height":G,"--n-blank-height":ce,"--n-label-font-size":A,"--n-label-text-align":k,"--n-label-height":Q,"--n-label-padding":ge,"--n-label-font-weight":z,"--n-asterisk-color":Z,"--n-label-text-color":K,"--n-feedback-padding":ue,"--n-feedback-font-size":me,"--n-feedback-height":ae,"--n-feedback-text-color":X,"--n-feedback-text-color-warning":B,"--n-feedback-text-color-error":H}}),_=t?et("form-item",S(()=>{var w;return`${l.value[0]}${m.value[0]}${((w=b.value)===null||w===void 0?void 0:w[0])||""}`}),ie,r):void 0,L=S(()=>m.value==="left"&&q.value==="left"&&b.value==="left");return Object.assign(Object.assign(Object.assign(Object.assign({labelElementRef:re,mergedClsPrefix:e,mergedRequired:f,feedbackId:g,renderExplains:c,reverseColSpace:L},o),i),Y),{cssVars:t?void 0:ie,themeClass:_==null?void 0:_.themeClass,onRender:_==null?void 0:_.onRender})},render(){const{$slots:r,mergedClsPrefix:e,mergedShowLabel:t,mergedShowRequireMark:n,mergedRequireMarkPlacement:i,onRender:o}=this,a=n!==void 0?n:this.mergedRequired;o==null||o();const s=()=>{const f=this.$slots.label?this.$slots.label():this.label;if(!f)return null;const d=E("span",{class:`${e}-form-item-label__text`},f),l=a?E("span",{class:`${e}-form-item-label__asterisk`},i!=="left"?" *":"* "):i==="right-hanging"&&E("span",{class:`${e}-form-item-label__asterisk-placeholder`}," *"),{labelProps:m}=this;return E("label",Object.assign({},m,{class:[m==null?void 0:m.class,`${e}-form-item-label`,`${e}-form-item-label--${i}-mark`,this.reverseColSpace&&`${e}-form-item-label--reverse-columns-space`],style:this.mergedLabelStyle,ref:"labelElementRef"}),i==="left"?[l,d]:[d,l])};return E("div",{class:[`${e}-form-item`,this.themeClass,`${e}-form-item--${this.mergedSize}-size`,`${e}-form-item--${this.mergedLabelPlacement}-labelled`,this.isAutoLabelWidth&&`${e}-form-item--auto-label-width`,!t&&`${e}-form-item--no-label`],style:this.cssVars},t&&s(),E("div",{class:[`${e}-form-item-blank`,this.mergedValidationStatus&&`${e}-form-item-blank--${this.mergedValidationStatus}`]},r),this.mergedShowFeedback?E("div",{key:this.feedbackId,style:this.feedbackStyle,class:[`${e}-form-item-feedback-wrapper`,this.feedbackClass]},E(tt,{name:"fade-down-transition",mode:"out-in"},{default:()=>{const{mergedValidationStatus:f}=this;return nt(r.feedback,d=>{var l;const{feedback:m}=this,b=d||m?E("div",{key:"__feedback__",class:`${e}-form-item-feedback__line`},d||m):this.renderExplains.length?(l=this.renderExplains)===null||l===void 0?void 0:l.map(({key:q,render:c})=>E("div",{key:q,class:`${e}-form-item-feedback__line`},c())):null;return b?f==="warning"?E("div",{key:"controlled-warning",class:`${e}-form-item-feedback ${e}-form-item-feedback--warning`},b):f==="error"?E("div",{key:"controlled-error",class:`${e}-form-item-feedback ${e}-form-item-feedback--error`},b):f==="success"?E("div",{key:"controlled-success",class:`${e}-form-item-feedback ${e}-form-item-feedback--success`},b):E("div",{key:"controlled-default",class:`${e}-form-item-feedback`},b):null})}})):null)}});export{Jt as N,Zt as a};
