import{a7 as S,P as _,a8 as w,a9 as B,Z as R,d as y,L as j,$ as I,b as f,Y as $,X as g,aa as h,O as a,N as P,ab as L,ac as V,ad as F,ae as K,I as C,K as m,J as x,V as z,r as U,W as E,af as W,ag as q,ah as M,ai as D,aj as b}from"./index.js";import{u as T}from"./use-locale.js";const H={iconSize:"22px"};function J(e){const{fontSize:i,warningColor:s}=e;return Object.assign(Object.assign({},H),{fontSize:i,iconColor:s})}const X=S({name:"Popconfirm",common:_,peers:{Button:w,Popover:B},self:J}),Y=X,N=R("n-popconfirm"),O={positiveText:String,negativeText:String,showIcon:{type:Boolean,default:!0},onPositiveClick:{type:Function,required:!0},onNegativeClick:{type:Function,required:!0}},k=K(O),Z=y({name:"NPopconfirmPanel",props:O,setup(e){const{localeRef:i}=T("Popconfirm"),{inlineThemeDisabled:s}=j(),{mergedClsPrefixRef:n,mergedThemeRef:v,props:r}=I(N),d=f(()=>{const{common:{cubicBezierEaseInOut:o},self:{fontSize:l,iconSize:c,iconColor:u}}=v.value;return{"--n-bezier":o,"--n-font-size":l,"--n-icon-size":c,"--n-icon-color":u}}),t=s?$("popconfirm-panel",void 0,d,r):void 0;return Object.assign(Object.assign({},T("Popconfirm")),{mergedClsPrefix:n,cssVars:s?void 0:d,localizedPositiveText:f(()=>e.positiveText||i.value.positiveText),localizedNegativeText:f(()=>e.negativeText||i.value.negativeText),positiveButtonProps:g(r,"positiveButtonProps"),negativeButtonProps:g(r,"negativeButtonProps"),handlePositiveClick(o){e.onPositiveClick(o)},handleNegativeClick(o){e.onNegativeClick(o)},themeClass:t==null?void 0:t.themeClass,onRender:t==null?void 0:t.onRender})},render(){var e;const{mergedClsPrefix:i,showIcon:s,$slots:n}=this,v=h(n.action,()=>this.negativeText===null&&this.positiveText===null?[]:[this.negativeText!==null&&a(P,Object.assign({size:"small",onClick:this.handleNegativeClick},this.negativeButtonProps),{default:()=>this.localizedNegativeText}),this.positiveText!==null&&a(P,Object.assign({size:"small",type:"primary",onClick:this.handlePositiveClick},this.positiveButtonProps),{default:()=>this.localizedPositiveText})]);return(e=this.onRender)===null||e===void 0||e.call(this),a("div",{class:[`${i}-popconfirm__panel`,this.themeClass],style:this.cssVars},L(n.default,r=>s||r?a("div",{class:`${i}-popconfirm__body`},s?a("div",{class:`${i}-popconfirm__icon`},h(n.icon,()=>[a(V,{clsPrefix:i},{default:()=>a(F,null)})])):null,r):null),v?a("div",{class:[`${i}-popconfirm__action`]},v):null)}}),A=C("popconfirm",[m("body",`
 font-size: var(--n-font-size);
 display: flex;
 align-items: center;
 flex-wrap: nowrap;
 position: relative;
 `,[m("icon",`
 display: flex;
 font-size: var(--n-icon-size);
 color: var(--n-icon-color);
 transition: color .3s var(--n-bezier);
 margin: 0 8px 0 0;
 `)]),m("action",`
 display: flex;
 justify-content: flex-end;
 `,[x("&:not(:first-child)","margin-top: 8px"),C("button",[x("&:not(:last-child)","margin-right: 8px;")])])]),G=Object.assign(Object.assign(Object.assign({},z.props),D),{positiveText:String,negativeText:String,showIcon:{type:Boolean,default:!0},trigger:{type:String,default:"click"},positiveButtonProps:Object,negativeButtonProps:Object,onPositiveClick:Function,onNegativeClick:Function}),oe=y({name:"Popconfirm",props:G,slots:Object,__popover__:!0,setup(e){const{mergedClsPrefixRef:i}=j(),s=z("Popconfirm","-popconfirm",A,Y,e,i),n=U(null);function v(t){var o;if(!(!((o=n.value)===null||o===void 0)&&o.getMergedShow()))return;const{onPositiveClick:l,"onUpdate:show":c}=e;Promise.resolve(l?l(t):!0).then(u=>{var p;u!==!1&&((p=n.value)===null||p===void 0||p.setShow(!1),c&&b(c,!1))})}function r(t){var o;if(!(!((o=n.value)===null||o===void 0)&&o.getMergedShow()))return;const{onNegativeClick:l,"onUpdate:show":c}=e;Promise.resolve(l?l(t):!0).then(u=>{var p;u!==!1&&((p=n.value)===null||p===void 0||p.setShow(!1),c&&b(c,!1))})}return E(N,{mergedThemeRef:s,mergedClsPrefixRef:i,props:e}),{setShow(t){var o;(o=n.value)===null||o===void 0||o.setShow(t)},syncPosition(){var t;(t=n.value)===null||t===void 0||t.syncPosition()},mergedTheme:s,popoverInstRef:n,handlePositiveClick:v,handleNegativeClick:r}},render(){const{$slots:e,$props:i,mergedTheme:s}=this;return a(M,q(i,k,{theme:s.peers.Popover,themeOverrides:s.peerOverrides.Popover,internalExtraClass:["popconfirm"],ref:"popoverInstRef"}),{trigger:e.trigger,default:()=>{const n=W(i,k);return a(Z,Object.assign(Object.assign({},n),{onPositiveClick:this.handlePositiveClick,onNegativeClick:this.handleNegativeClick}),e)}})}});export{oe as N};
