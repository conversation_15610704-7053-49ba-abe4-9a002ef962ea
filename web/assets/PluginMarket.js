import{d as K,o as i,c as b,a as r,h as P,u as Z,r as g,e as ee,g as c,i as a,j as s,k as n,s as A,an as te,l as U,N as p,m as o,q as F,F as E,C as y,a6 as ae,t as d,ao as se,ap as le,A as N,p as B,x as ne,aq as re,_ as oe}from"./index.js";import{S as ie}from"./SearchOutline.js";import{N as ue}from"./Pagination.js";import{H as de}from"./HelpCircleOutline.js";import{u as ce}from"./use-loading-bar.js";import{N as pe}from"./Input.js";import{N as fe}from"./Empty.js";import{N as x}from"./Skeleton.js";import"./cryptojs.js";import"./use-locale.js";import"./en-US.js";import"./Select.js";import"./Checkmark.js";const ge={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},ve=r("path",{d:"M416 221.25V416a48 48 0 0 1-48 48H144a48 48 0 0 1-48-48V96a48 48 0 0 1 48-48h98.75a32 32 0 0 1 22.62 9.37l141.26 141.26a32 32 0 0 1 9.37 22.62z",fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"32"},null,-1),ye=r("path",{d:"M256 56v120a32 32 0 0 0 32 32h120",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),me=r("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M176 288h160"},null,-1),ke=r("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M176 368h160"},null,-1),_e=[ve,ye,me,ke],he=K({name:"DocumentTextOutline",render:function(u,v){return i(),b("svg",ge,_e)}}),$={searchPlugins(f="",u=1,v=10){const C=new URLSearchParams({query:f,page:u.toString(),pageSize:v.toString()});return P.get(`/plugin/v1/search?${C.toString()}`)},getMarketPluginInfo(f){return P.get(`/plugin/v1/info/${f}`)},getInstalledPlugins(){return P.get("/plugin/plugins")},installPlugin(f,u){return P.post("/plugin/plugins",{package_name:f,version:u})},uninstallPlugin(f){return P.delete(`/plugin/plugins/${f}`)},togglePlugin(f,u){return P.post(`/plugin/plugins/${f}/${u?"enable":"disable"}`)},updatePlugin(f){return P.put(`/plugin/plugins/${f}`)}},Pe={class:"plugin-market"},we={class:"search-container"},xe={class:"plugins-grid"},be={class:"plugin-header"},Ce={class:"plugin-tags"},ze={class:"plugin-description"},Ie={class:"plugin-meta"},Ne={class:"plugin-actions"},$e={class:"plugin-header"},Me={class:"plugin-tags"},Se={class:"plugin-meta"},Ue={class:"plugin-actions"},Ee={class:"action-buttons"},Be={key:0,class:"pagination-container"},Oe={class:"error-message"},Ve=K({__name:"PluginMarket",setup(f){const u=ce(),v=Z(),C=g([]),O=g(""),z=g(1),V=g(10),T=g(0),j=g(!1),q=g(!0),D=g(!0),L=g({}),M=g({}),_=g(!1),I=g(""),m=l=>{var e;return((e=L.value[l.pypiPackage])==null?void 0:e.loading)||!1},S=l=>{var e;return((e=L.value[l.pypiPackage])==null?void 0:e.operation)||null},k=(l,e,t)=>{L.value[l.pypiPackage]={loading:t,operation:e}},Q=Array(V.value).fill({name:"加载中...",description:"插件描述加载中，请稍候...",author:"作者加载中",pypiPackage:"package-name",pypiInfo:{version:"0.0.0",homePage:"#"}}),h=async()=>{j.value=!0,q.value=D.value,u.start();try{const l=await $.searchPlugins(O.value,z.value,V.value);C.value=l.plugins,T.value=l.totalCount}catch(l){console.error("获取插件列表失败:",l),v.error("获取插件列表失败")}finally{j.value=!1,D.value&&(q.value=!1,D.value=!1),u.finish()}},R=l=>{z.value=l,h()},H=()=>{z.value=1,h()},G=async l=>{if(!m(l)){k(l,"install",!0),u.start();try{await $.installPlugin(l.pypiPackage),v.success("插件安装成功"),await h()}catch(e){console.error("安装插件失败:",e),I.value=e.message||"未知错误",_.value=!0}finally{k(l,null,!1),u.finish()}}},J=async l=>{if(!m(l)){k(l,"update",!0),u.start();try{await $.updatePlugin(l.name),v.success("插件更新成功"),await h()}catch(e){console.error("更新插件失败:",e),I.value=e.message||"未知错误",_.value=!0}finally{k(l,null,!1),u.finish()}}},W=async l=>{if(!m(l)){k(l,"uninstall",!0),u.start();try{await $.uninstallPlugin(l.name),v.success("插件卸载成功"),await h()}catch(e){console.error("卸载插件失败:",e),I.value=e.message||"未知错误",_.value=!0}finally{k(l,null,!1),u.finish()}}},X=async l=>{if(!m(l)){k(l,"toggle",!0),u.start();try{await $.togglePlugin(l.name,!l.isEnabled),v.success(`插件${l.isEnabled?"禁用":"启用"}成功`),await h()}catch(e){console.error("切换插件状态失败:",e),v.error("切换插件状态失败"),I.value=e.message||"未知错误",_.value=!0}finally{k(l,null,!1),u.finish()}}},Y=l=>{M.value[l.pypiPackage]=!M.value[l.pypiPackage]};return ee(()=>{h()}),(l,e)=>(i(),c(s(le),null,{default:a(()=>[n(s(se),null,{default:a(()=>[r("div",Pe,[n(s(A),{title:"插件市场",class:"market-card"},{"header-extra":a(()=>[r("div",we,[n(s(pe),{value:O.value,"onUpdate:value":e[0]||(e[0]=t=>O.value=t),placeholder:"搜索插件...",class:"search-input",onKeyup:te(H,["enter"])},{prefix:a(()=>[n(s(U),null,{default:a(()=>[n(s(ie))]),_:1})]),_:1},8,["value"]),n(s(p),{type:"primary",onClick:H,disabled:j.value},{default:a(()=>e[4]||(e[4]=[o(" 搜索 ")])),_:1},8,["disabled"])])]),default:a(()=>[e[17]||(e[17]=r("div",{class:"market-description"}," 在这里浏览和安装可用的插件，扩展 Kirara AI 的功能。 ",-1)),r("div",xe,[q.value?(i(!0),b(E,{key:0},F(s(Q),(t,w)=>(i(),c(s(A),{key:w,size:"small",class:"plugin-card",bordered:!0},{default:a(()=>[r("div",be,[n(s(x),{text:"",style:{width:"60%"},sharp:!1},{default:a(()=>[r("h3",null,d(t.name),1)]),_:2},1024),r("div",Ce,[n(s(x),{text:"",style:{width:"80px"},sharp:!1},{default:a(()=>[n(s(N),{size:"small",type:"success",class:"version-tag"},{default:a(()=>[o(" v"+d(t.pypiInfo.version),1)]),_:2},1024)]),_:2},1024)])]),n(s(x),{text:"",repeat:2,sharp:!1,style:{margin:"16px 0"}},{default:a(()=>[r("div",ze,d(t.description),1)]),_:2},1024),r("div",Ie,[n(s(x),{text:"",style:{width:"60%"},sharp:!1},{default:a(()=>[n(s(B),{align:"center",size:12},{default:a(()=>[r("span",null,"作者: "+d(t.author),1),e[5]||(e[5]=r("span",{class:"separator"},"·",-1)),r("span",null,"PyPI: "+d(t.pypiPackage),1)]),_:2},1024)]),_:2},1024)]),r("div",Ne,[n(s(x),{text:"",style:{width:"30%"},sharp:!1},{default:a(()=>[n(s(B),{align:"center",size:12},{default:a(()=>[n(s(p),{secondary:"",text:""},{default:a(()=>e[6]||(e[6]=[o("主页")])),_:1}),n(s(p),{secondary:"",text:""},{default:a(()=>e[7]||(e[7]=[o("问题反馈")])),_:1}),n(s(p),{secondary:"",text:""},{default:a(()=>e[8]||(e[8]=[o("插件文档")])),_:1})]),_:1})]),_:1}),n(s(x),{text:"",style:{width:"80px"},sharp:!1},{default:a(()=>[n(s(p),{type:"primary"},{default:a(()=>e[9]||(e[9]=[o("安装")])),_:1})]),_:1})])]),_:2},1024))),128)):C.value.length>0?(i(!0),b(E,{key:1},F(C.value,t=>(i(),c(s(A),{key:t.pypiPackage,size:"small",class:"plugin-card",bordered:!0},{header:a(()=>[r("div",$e,[r("h3",null,d(t.name),1)])]),"header-extra":a(()=>[r("div",Me,[t.isInstalled?(i(),b(E,{key:0},[t.isInstalled?(i(),c(s(N),{key:0,size:"small",type:"info",class:"status-tag"},{default:a(()=>[o(" 已安装 v"+d(t.installedVersion),1)]),_:2},1024)):y("",!0),t.isUpgradable?(i(),c(s(N),{key:1,size:"small",type:"warning",class:"update-tag"},{default:a(()=>[o(" 可更新至 v"+d(t.pypiInfo.version),1)]),_:2},1024)):y("",!0),t.isEnabled!==void 0?(i(),c(s(N),{key:2,size:"small",type:t.isEnabled?"success":"error",class:"status-tag"},{default:a(()=>[o(d(t.isEnabled?"已启用":"已禁用"),1)]),_:2},1032,["type"])):y("",!0)],64)):(i(),c(s(N),{key:1,size:"small",type:"success",class:"version-tag"},{default:a(()=>[o(" v"+d(t.pypiInfo.version),1)]),_:2},1024))])]),default:a(()=>[r("div",{class:ne(["plugin-description",{expanded:M.value[t.pypiPackage]}])},d(t.description),3),t.description&&t.description.length>100?(i(),c(s(p),{key:0,text:"",size:"small",onClick:w=>Y(t),class:"toggle-description"},{default:a(()=>[o(d(M.value[t.pypiPackage]?"收起":"展开"),1)]),_:2},1032,["onClick"])):y("",!0),r("div",Se,[n(s(B),{align:"center",size:12},{default:a(()=>[r("span",null,"作者: "+d(t.author),1),e[10]||(e[10]=r("span",{class:"separator"},"·",-1)),r("span",null,"PyPI: "+d(t.pypiPackage),1)]),_:2},1024)]),r("div",Ue,[n(s(B),{align:"center",size:12},{default:a(()=>[n(s(p),{quaternary:"",size:"small",tag:"a",href:t.pypiInfo.homePage,target:"_blank"},{icon:a(()=>[n(s(U),null,{default:a(()=>[n(s(re))]),_:1})]),default:a(()=>[e[11]||(e[11]=o(" 主页 "))]),_:2},1032,["href"]),t.pypiInfo.bugTrackerUrl?(i(),c(s(p),{key:0,quaternary:"",size:"small",tag:"a",href:t.pypiInfo.bugTrackerUrl,target:"_blank"},{icon:a(()=>[n(s(U),null,{default:a(()=>[n(s(de))]),_:1})]),default:a(()=>[e[12]||(e[12]=o(" 问题反馈 "))]),_:2},1032,["href"])):y("",!0),t.pypiInfo.documentUrl?(i(),c(s(p),{key:1,quaternary:"",size:"small",tag:"a",href:t.pypiInfo.documentUrl,target:"_blank"},{icon:a(()=>[n(s(U),null,{default:a(()=>[n(s(he))]),_:1})]),default:a(()=>[e[13]||(e[13]=o(" 插件文档 "))]),_:2},1032,["href"])):y("",!0)]),_:2},1024),r("div",Ee,[t.isInstalled?(i(),b(E,{key:0},[t.isUpgradable?(i(),c(s(p),{key:0,type:"warning",size:"small",loading:m(t)&&S(t)==="update",onClick:w=>J(t),class:"action-button"},{default:a(()=>e[14]||(e[14]=[o(" 更新 ")])),_:2},1032,["loading","onClick"])):y("",!0),t.isEnabled!==void 0?(i(),c(s(p),{key:1,type:t.isEnabled?"error":"success",size:"small",loading:m(t)&&S(t)==="toggle",onClick:w=>X(t),class:"action-button"},{default:a(()=>[o(d(t.isEnabled?"禁用":"启用"),1)]),_:2},1032,["type","loading","onClick"])):y("",!0),n(s(p),{type:"error",size:"small",loading:m(t)&&S(t)==="uninstall",onClick:w=>W(t),class:"action-button"},{default:a(()=>e[15]||(e[15]=[o(" 卸载 ")])),_:2},1032,["loading","onClick"])],64)):(i(),c(s(p),{key:1,type:"primary",size:"small",loading:m(t)&&S(t)==="install",onClick:w=>G(t),class:"action-button"},{default:a(()=>e[16]||(e[16]=[o(" 安装 ")])),_:2},1032,["loading","onClick"]))])])]),_:2},1024))),128)):(i(),c(s(fe),{key:2,description:"没有找到插件"}))]),T.value>0?(i(),b("div",Be,[n(s(ue),{page:z.value,"onUpdate:page":[e[1]||(e[1]=t=>z.value=t),R],"page-count":Math.ceil(T.value/V.value)},null,8,["page","page-count"])])):y("",!0)]),_:1}),n(s(ae),{show:_.value,"onUpdate:show":e[3]||(e[3]=t=>_.value=t),title:"操作失败",preset:"dialog",type:"error",style:{width:"600px"}},{action:a(()=>[n(s(p),{onClick:e[2]||(e[2]=t=>_.value=!1)},{default:a(()=>e[18]||(e[18]=[o("关闭")])),_:1})]),default:a(()=>[e[19]||(e[19]=o(" 错误信息： ")),r("div",Oe,d(I.value),1)]),_:1},8,["show"])])]),_:1})]),_:1}))}});const We=oe(Ve,[["__scopeId","data-v-170d4faa"]]);export{We as default};
