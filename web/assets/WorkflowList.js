import{d as L,o as y,c as $,a as z,r as d,g as F,i as l,k as o,j as a,u as R,b as U,e as V,y as A,N as v,l as C,m as I,s as S,a6 as j,O as n,a5 as P,A as G,p as H,_ as J}from"./index.js";import{l as K,d as Q,g as X,c as Y}from"./workflow.js";import{N as D}from"./Input.js";import{a as W,N as Z}from"./FormItem.js";import{A as ee}from"./AddOutline.js";import{N as te}from"./Skeleton.js";import{N as ae}from"./DataTable.js";import{C as oe}from"./CopyOutline.js";import{T as le}from"./TrashOutline.js";import{N as re}from"./Empty.js";import{N as ne}from"./Popconfirm.js";import"./cryptojs.js";import"./use-locale.js";import"./en-US.js";import"./Select.js";import"./Checkmark.js";import"./Pagination.js";const se={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},ie=z("path",{d:"M384 224v184a40 40 0 0 1-40 40H104a40 40 0 0 1-40-40V168a40 40 0 0 1 40-40h167.48",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),ue=z("path",{d:"M459.94 53.25a16.06 16.06 0 0 0-23.22-.56L424.35 65a8 8 0 0 0 0 11.31l11.34 11.32a8 8 0 0 0 11.34 0l12.06-12c6.1-6.09 6.67-16.01.85-22.38z",fill:"currentColor"},null,-1),de=z("path",{d:"M399.34 90L218.82 270.2a9 9 0 0 0-2.31 3.93L208.16 299a3.91 3.91 0 0 0 4.86 4.86l24.85-8.35a9 9 0 0 0 3.93-2.31L422 112.66a9 9 0 0 0 0-12.66l-9.95-10a9 9 0 0 0-12.71 0z",fill:"currentColor"},null,-1),ce=[ie,ue,de],fe=L({name:"CreateOutline",render:function(c,p){return y(),$("svg",se,ce)}}),pe=L({__name:"WorkflowForm",props:{initialData:{}},setup(x,{expose:c}){var k,N,_;const p=x,w=d(null),i=d({workflowId:((k=p.initialData)==null?void 0:k.workflowId)||"",name:((N=p.initialData)==null?void 0:N.name)||"",description:((_=p.initialData)==null?void 0:_.description)||""}),m={workflowId:{required:!0,message:"请输入工作流ID",trigger:"blur",validator:(g,s)=>{if(!s)return!1;const r=s.split(":");if(r.length!==2)throw new Error("工作流ID格式必须为 group:workflow");if(!r[0]||!r[1])throw new Error("group 和 workflow 都不能为空");return!0}},name:{required:!0,message:"请输入工作流名称",trigger:"blur"}};return c({validate:async(g,s)=>{var r;return await((r=w.value)==null?void 0:r.validate(g,s))??{warnings:[]}},getFormData:()=>i.value}),(g,s)=>(y(),F(a(Z),{ref_key:"formRef",ref:w,model:i.value,rules:m,"label-placement":"left","label-width":"80","require-mark-placement":"right-hanging"},{default:l(()=>[o(a(W),{label:"工作流ID",path:"workflowId"},{default:l(()=>[o(a(D),{value:i.value.workflowId,"onUpdate:value":s[0]||(s[0]=r=>i.value.workflowId=r),placeholder:"请输入工作流ID，格式为 group:workflow"},null,8,["value"])]),_:1}),o(a(W),{label:"名称",path:"name"},{default:l(()=>[o(a(D),{value:i.value.name,"onUpdate:value":s[1]||(s[1]=r=>i.value.name=r),placeholder:"请输入工作流名称"},null,8,["value"])]),_:1}),o(a(W),{label:"描述",path:"description"},{default:l(()=>[o(a(D),{value:i.value.description,"onUpdate:value":s[2]||(s[2]=r=>i.value.description=r),type:"textarea",placeholder:"请输入工作流描述"},null,8,["value"])]),_:1})]),_:1},8,["model"]))}}),we={class:"workflow-list"},me={key:0,class:"loading-skeleton"},ke=L({__name:"WorkflowList",setup(x){A();const c=R(),p=d([]),w=d(!1),i=d(""),m=d(!1),h=d(!1),u=d(null),k=d(null),N=[{title:"ID",key:"id",width:200,ellipsis:{tooltip:!0}},{title:"名称",key:"name",width:200,render(e){return n(P,null,{trigger:()=>n("span",{class:"workflow-name"},e.name),default:()=>e.name})}},{title:"描述",key:"description",ellipsis:{tooltip:!0}},{title:"区块数量",key:"block_count",width:100,render(e){return n(G,{type:"info",round:!0,size:"small"},{default:()=>e.block_count})}},{title:"操作",key:"actions",width:220,render(e){const t=`${e.group_id}:${e.workflow_id}`;return n(H,null,{default:()=>[n(v,{quaternary:!0,size:"small",onClick:()=>s(e),class:"action-button"},{icon:()=>n(C,null,{default:()=>n(fe)}),default:()=>"编辑"}),n(v,{quaternary:!0,size:"small",onClick:()=>O(e),class:"action-button"},{icon:()=>n(C,null,{default:()=>n(oe)}),default:()=>"复制"}),n(ne,{onPositiveClick:()=>r(e),class:i.value===t?"deleting":""},{trigger:()=>n(v,{quaternary:!0,size:"small",type:"error",class:"action-button"},{icon:()=>n(C,null,{default:()=>n(le)}),default:()=>"删除"}),default:()=>"确认删除该工作流？"})]})}}],_=async()=>{w.value=!0;try{const{workflows:e}=await K();p.value=e}catch{c.error("获取工作流列表失败")}finally{w.value=!1}},g=()=>{window.open("/workflow/editor","_blank")},s=e=>{window.open(`/workflow/editor/${e.group_id}:${e.workflow_id}`,"_blank")},r=async e=>{const t=`${e.group_id}:${e.workflow_id}`;i.value=t;try{await Q(e.group_id,e.workflow_id),c.success("删除成功"),await _()}catch{c.error("删除失败")}finally{i.value=""}},O=e=>{u.value=e,m.value=!0},T=async()=>{var B;if(!k.value||(B=(await k.value.validate()).warnings)!=null&&B.length||!u.value)return;const t=k.value.getFormData(),[f,q]=t.workflowId.split(":");h.value=!0;try{const{workflow:b}=await X(u.value.group_id,u.value.workflow_id);await Y(f,q,{group_id:f,workflow_id:q,name:t.name,description:t.description,blocks:b.blocks,wires:b.wires}),c.success("复制成功"),m.value=!1,_()}catch(b){c.error(`复制失败: ${b.message||"未知错误"}`)}finally{h.value=!1}},E=()=>{m.value=!1},M=U(()=>p.value.map(e=>({...e,id:e.group_id+":"+e.workflow_id})));return V(()=>{_()}),(e,t)=>(y(),$("div",we,[o(a(S),{title:"工作流管理",class:"workflow-card"},{"header-extra":l(()=>[o(a(v),{type:"primary",onClick:g,class:"create-button"},{icon:l(()=>[o(a(C),null,{default:l(()=>[o(a(ee))]),_:1})]),default:l(()=>[t[1]||(t[1]=I(" 创建工作流 "))]),_:1})]),default:l(()=>[w.value?(y(),$("div",me,[o(a(te),{text:"",repeat:3})])):M.value.length===0?(y(),F(a(re),{key:1,description:"暂无工作流"},{extra:l(()=>[o(a(v),{type:"primary",onClick:g,class:"create-button"},{default:l(()=>t[2]||(t[2]=[I(" 创建工作流 ")])),_:1})]),_:1})):(y(),F(a(ae),{key:2,columns:N,data:M.value,loading:w.value,class:"workflow-table","row-class-name":()=>"workflow-row",pagination:{pageSize:10}},null,8,["data","loading"]))]),_:1}),o(a(j),{show:m.value,"onUpdate:show":t[0]||(t[0]=f=>m.value=f),preset:"dialog",title:"复制工作流"},{action:l(()=>[o(a(v),{onClick:E},{default:l(()=>t[3]||(t[3]=[I("取消")])),_:1}),o(a(v),{type:"primary",loading:h.value,onClick:T},{default:l(()=>t[4]||(t[4]=[I(" 确认复制 ")])),_:1},8,["loading"])]),default:l(()=>{var f;return[o(pe,{ref_key:"copyFormRef",ref:k,"initial-data":{workflowId:u.value?`${u.value.group_id}:${u.value.workflow_id}_copy`:"",name:u.value?`${u.value.name} (复制)`:"",description:((f=u.value)==null?void 0:f.description)||""}},null,8,["initial-data"])]}),_:1},8,["show"])]))}});const qe=J(ke,[["__scopeId","data-v-4f212fcb"]]);export{qe as default};
