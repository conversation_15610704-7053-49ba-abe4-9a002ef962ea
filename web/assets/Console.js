import{d as $,o as p,c as x,a as r,u as E,r as S,b as A,h as B,w as z,e as P,f as V,g as T,i as o,j as e,k as s,N as D,l as b,m as U,n as j,p as F,t as C,F as H,q as G,s as J,v as q,x as K,_ as Q}from"./index.js";import{u as X}from"./composables.js";import{N as Y}from"./Input.js";import{S as Z}from"./SearchOutline.js";import{T as ee}from"./TrashOutline.js";import{R as te}from"./RefreshOutline.js";import{N as oe}from"./Empty.js";import"./cryptojs.js";import"./use-locale.js";import"./en-US.js";const le={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},ne=r("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"48",d:"M112 268l144 144l144-144"},null,-1),se=r("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"48",d:"M256 392V100"},null,-1),re=[ne,se],ae=$({name:"ArrowDownOutline",render:function(_,t){return p(),x("svg",le,re)}}),ie={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},ce=r("path",{d:"M448 256c0-106-86-192-192-192S64 150 64 256s86 192 192 192s192-86 192-192z",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),ue=r("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-miterlimit":"10","stroke-width":"32",d:"M208 192v128"},null,-1),de=r("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-miterlimit":"10","stroke-width":"32",d:"M304 192v128"},null,-1),pe=[ce,ue,de],ve=$({name:"PauseCircleOutline",render:function(_,t){return p(),x("svg",ie,pe)}}),ge={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},fe=r("path",{d:"M448 256c0-106-86-192-192-192S64 150 64 256s86 192 192 192s192-86 192-192z",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),me=r("path",{d:"M216.32 334.44l114.45-69.14a10.89 10.89 0 0 0 0-18.6l-114.45-69.14a10.78 10.78 0 0 0-16.32 9.31v138.26a10.78 10.78 0 0 0 16.32 9.31z",fill:"currentColor"},null,-1),we=[fe,me],he=$({name:"PlayCircleOutline",render:function(_,t){return p(),x("svg",ge,we)}});function _e(){const d=E(),_=X(),t=S([]),w=S(!1),c=S(!1),v=S("");let a=null,g=null,h=0;const I=5,f=3e3,m=A(()=>v.value?t.value.filter(l=>l.content.toLowerCase().includes(v.value.toLowerCase())||l.tag.toLowerCase().includes(v.value.toLowerCase())||l.level.toLowerCase().includes(v.value.toLowerCase())):t.value),R=l=>{v.value=l},k=l=>{if(l.type==="log"){switch(l.level.toUpperCase()){}t.value.push(l),t.value.length>1e3&&(t.value=t.value.slice(-1e3))}},O=()=>{a&&N(),c.value=!0;try{const l=window.location.protocol==="https:"?"wss:":"ws:",W=window.location.host,u=localStorage.getItem("token")||"",i=B.url("/system/logs");a=new WebSocket(i),a.onopen=()=>{a.send(JSON.stringify({type:"auth",token:u})),w.value=!0,c.value=!1,d.success("已连接到服务器日志"),t.value=[{type:"log",level:"INFO",content:"已连接到服务器日志，正在加载历史日志...",timestamp:new Date().toISOString(),tag:"WebUI"}],h=0,g!==null&&(clearTimeout(g),g=null)},a.onmessage=n=>{try{const y=JSON.parse(n.data);Array.isArray(y)?y.forEach(M=>{k(M)}):k(y)}catch{t.value.push(n.data)}},a.onclose=n=>{w.value=!1,c.value=!1,!n.wasClean&&h<I?(t.value.push({type:"log",level:"INFO",content:`连接断开，${f/1e3}秒后尝试重新连接...`,timestamp:new Date().toISOString(),tag:"WebUI"}),g=window.setTimeout(()=>{h++,t.value.push({type:"log",level:"INFO",content:`正在尝试重新连接 (${h}/${I})...`,timestamp:new Date().toISOString(),tag:"WebUI"}),O()},f)):h>=I?(t.value.push({type:"log",level:"ERROR",content:"重连次数已达上限，请手动刷新页面重试",timestamp:new Date().toISOString(),tag:"WebUI"}),d.error("重连次数已达上限，请手动刷新页面重试")):t.value.push({type:"log",level:"ERROR",content:"与服务器的连接已断开",timestamp:new Date().toISOString(),tag:"WebUI"})},a.onerror=n=>{w.value=!1,c.value=!1,d.error("连接服务器日志失败"),t.value.push({type:"log",level:"ERROR",content:"连接服务器日志失败",timestamp:new Date().toISOString(),tag:"WebUI"}),console.error("WebSocket错误:",n)}}catch(l){c.value=!1,d.error("连接服务器日志失败"),console.error("WebSocket连接错误:",l)}},N=()=>{g!==null&&(clearTimeout(g),g=null),a&&(a.onclose=null,a.close(),a=null,w.value=!1)};return{logs:t,isConnected:w,isLoading:c,filterText:v,filteredLogs:m,connect:O,disconnect:N,restartServer:async()=>{if(!w.value){d.warning("未连接到服务器");return}_.warning({title:"确认重启",content:"确定要重启服务器吗？重启过程中服务将暂时不可用。",positiveText:"确认重启",negativeText:"取消",onPositiveClick:async()=>{try{c.value=!0,await B.post("/system/restart"),d.success("重启命令已发送"),t.value.push({type:"log",level:"INFO",content:"重启命令已发送",timestamp:new Date().toISOString(),tag:"WebUI"}),N(),t.value.push({type:"log",level:"INFO",content:"等待服务器重启，5秒后尝试重新连接...",timestamp:new Date().toISOString(),tag:"WebUI"}),setTimeout(()=>{h=0,O()},5e3)}catch(l){c.value=!1,d.error("重启服务器失败"),console.error("重启服务器错误:",l),t.value.push({type:"log",level:"ERROR",content:"重启服务器失败",timestamp:new Date().toISOString(),tag:"WebUI"})}}})},updateFilterText:R}}const ke={key:0,class:"console-logs"},ye={class:"log-index"},Se={class:"log-line-content"},Ce={class:"log-timestamp"},xe={class:"log-level"},Ie={class:"log-tag"},Oe={class:"log-content"},Ne=$({__name:"Console",setup(d){const{logs:_,isConnected:t,isLoading:w,filterText:c,filteredLogs:v,connect:a,disconnect:g,restartServer:h,updateFilterText:I}=_e(),f=S(null),m=S(!0),R=S(!1),k=async()=>{await q(),f.value&&f.value.scrollTo({top:f.value.scrollHeight,behavior:"auto"})},O=()=>{if(f.value){const{scrollTop:u,scrollHeight:i,clientHeight:n}=f.value;R.value=i-u>n+20}};function N(u){return u.level==="ERROR"?"log-error":u.level==="WARNING"?"log-warning":u.level==="DEBUG"?"log-debug":u.level==="INFO"?"log-info":u.level==="SUCCESS"?"log-success":""}function L(u){return new Date(u).toLocaleString("zh-CN",{hour12:!1})}const l=()=>{m.value=!m.value,m.value&&k()},W=()=>{_.value=[{type:"log",level:"INFO",content:"日志已清空",timestamp:new Date().toISOString(),tag:"WebUI"}],k()};return z(()=>_.value.length,()=>{m.value&&k()}),P(()=>{a()}),V(()=>{g()}),(u,i)=>(p(),T(e(J),{title:"服务器控制台",class:"console-card"},{"header-extra":o(()=>[s(e(F),{justify:"space-between",align:"center"},{default:o(()=>[s(e(D),{type:"primary",tertiary:"",onClick:k,disabled:!R.value},{icon:o(()=>[s(e(b),null,{default:o(()=>[s(e(ae))]),_:1})]),default:o(()=>[i[1]||(i[1]=U(" 回到底部 "))]),_:1},8,["disabled"]),s(e(Y),{value:e(c),"onUpdate:value":[i[0]||(i[0]=n=>j(c)?c.value=n:null),e(I)],placeholder:"输入关键词过滤日志",clearable:"",style:{width:"300px"}},{prefix:o(()=>[s(e(b),null,{default:o(()=>[s(e(Z))]),_:1})]),_:1},8,["value","onUpdate:value"]),s(e(F),{align:"center",size:8},{default:o(()=>[s(e(D),{type:"default",onClick:W},{icon:o(()=>[s(e(b),null,{default:o(()=>[s(e(ee))]),_:1})]),default:o(()=>[i[2]||(i[2]=U(" 清空日志 "))]),_:1}),s(e(D),{type:m.value?"primary":"default",onClick:l},{icon:o(()=>[s(e(b),null,{default:o(()=>[m.value?(p(),T(e(ve),{key:0})):(p(),T(e(he),{key:1}))]),_:1})]),default:o(()=>[U(" "+C(m.value?"自动滚动：开启":"自动滚动：关闭"),1)]),_:1},8,["type"]),s(e(D),{type:"error",onClick:e(h),loading:e(w),disabled:!e(t)},{icon:o(()=>[s(e(b),null,{default:o(()=>[s(e(te))]),_:1})]),default:o(()=>[i[3]||(i[3]=U(" 重启服务器 "))]),_:1},8,["onClick","loading","disabled"])]),_:1})]),_:1})]),default:o(()=>[r("div",{class:"console-container",ref_key:"consoleContainer",ref:f,onScroll:O},[e(v).length>0?(p(),x("div",ke,[(p(!0),x(H,null,G(e(v),(n,y)=>(p(),x("div",{key:y,class:K(["log-line",N(n)])},[r("span",ye,C(y+1),1),r("div",Se,[r("span",Ce,C(L(n.timestamp)),1),r("span",xe,C(n.level),1),r("span",Ie,C(n.tag),1),r("span",Oe,C(n.content),1)])],2))),128))])):(p(),T(e(oe),{key:1,description:"暂无日志"}))],544)]),_:1}))}});const Me=Q(Ne,[["__scopeId","data-v-8749eae9"]]);export{Me as default};
