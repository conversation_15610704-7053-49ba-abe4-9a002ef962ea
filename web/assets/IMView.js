import{i as h}from"./im.js";import{d as x,u as C,r as d,e as I,c as N,k as o,i as e,j as s,y as V,o as p,N as z,m as i,a as n,F as B,q as M,s as k,g as T,p as S,z as F,t as u,A as G,_ as $}from"./index.js";import{N as j}from"./Spin.js";import{N as q,a as D}from"./Grid.js";import"./cryptojs.js";const E={class:"im-view"},K={class:"adapter-card-header"},L={class:"adapter-type"},R={class:"adapter-card-content"},H=x({__name:"IMView",setup(J){const _=C(),m=d([]),f=d([]),y=d(!1),w=V(),l=d({}),v=async()=>{try{const{types:a,adapters:t}=await h.getAdapterTypes();m.value=a,l.value=t??{}}catch(a){_.error("获取适配器类型失败: "+a),console.error("获取适配器类型失败:",a)}},A=async()=>{try{const a=await h.getAdapters();f.value=a.adapters||[]}catch(a){_.error("获取适配器列表失败: "+a),console.error("获取适配器列表失败:",a)}},g=a=>f.value.some(t=>t.adapter===a),b=async a=>{w.push(`/im/adapters/${a}`)};return I(()=>{v(),A()}),(a,t)=>(p(),N("div",E,[o(s(k),{title:"聊天平台管理",class:"im-card"},{"header-extra":e(()=>[o(s(z),{type:"primary",onClick:v,class:"refresh-button"},{default:e(()=>t[0]||(t[0]=[i(" 刷新 ")])),_:1})]),default:e(()=>[t[1]||(t[1]=n("div",{class:"im-view-description"},[i(" 在这里配置 Kirara AI 与聊天平台的连接方式，更多介绍请阅读"),n("a",{href:"https://kirara-docs.app.lss233.com/guide/configuration/im.html",target:"_blank"},"官方文档"),i("。 ")],-1)),o(s(j),{show:y.value},{default:e(()=>[o(s(q),{cols:3,"x-gap":16,"y-gap":16,responsive:"screen"},{default:e(()=>[(p(!0),N(B,null,M(m.value,r=>(p(),T(s(D),{key:r,span:1},{default:e(()=>[o(s(k),{class:"adapter-card",hoverable:"",onClick:c=>b(r)},{header:e(()=>[n("div",K,[o(s(S),null,{default:e(()=>{var c;return[o(s(F),{size:30,round:"",src:"/assets/icons/im/"+r+".png","fallback-src":"/assets/icons/im/fallback-im.svg"},null,8,["src"]),n("span",L,u(((c=l.value[r])==null?void 0:c.localized_name)||r),1)]}),_:2},1024),o(s(G),{type:g(r)?"success":"default",size:"small",round:""},{default:e(()=>[i(u(g(r)?"已配置":"未配置"),1)]),_:2},1032,["type"])])]),default:e(()=>{var c;return[n("div",R,u((c=l.value[r])==null?void 0:c.localized_description),1)]}),_:2},1032,["onClick"])]),_:2},1024))),128))]),_:1})]),_:1},8,["show"])]),_:1})]))}});const X=$(H,[["__scopeId","data-v-b8ab23cc"]]);export{X as default};
