import{u as V}from"./llm-tracing.vm.js";import{R as q}from"./RefreshOutline.js";import{S as w}from"./SearchOutline.js";import{d as G,e as R,f as j,c as N,k as t,i as a,j as e,o as p,p as b,bF as E,D as k,m as n,t as d,N as f,l as C,a as o,F as W,q as $,s as c,g as A,x as H,_ as J}from"./index.js";import{N as L,a as r}from"./Grid.js";import{N as m}from"./Select.js";import{N as K}from"./Input.js";import{N as Q}from"./DataTable.js";import"./composables.js";import"./format.js";import"./en-US.js";import"./cryptojs.js";import"./use-locale.js";import"./Checkmark.js";import"./Empty.js";import"./Pagination.js";const X={class:"trace-list"},Y={class:"statistics-section"},Z={class:"stat-content"},ee={class:"stat-value"},te={class:"stat-label"},ae={class:"filter-section"},se={class:"filter-actions"},le={class:"trace-list-section"},oe={class:"list-header"},ie=G({__name:"LLMTraceList",setup(ne){const{traces:x,formattedStatistics:y,isConnected:u,isLoading:_,totalTraces:g,currentPage:S,pageSize:h,totalPages:z,filterParams:i,filterOptions:v,statusOptions:T,columns:P,fetchTraces:re,resetFilter:I,applyFilter:M,handlePageChange:U,handlePageSizeChange:B,refreshData:F,initialize:D,disconnectWebSocket:O}=V();return R(()=>{D()}),j(()=>{O()}),(de,s)=>(p(),N("div",X,[t(e(c),{title:"LLM 请求追踪",class:"trace-card"},{"header-extra":a(()=>[t(e(b),{align:"center",size:12},{default:a(()=>[t(e(E),{dot:e(u),color:e(u)?"success":"error",class:"connection-status"},{default:a(()=>[t(e(k),null,{default:a(()=>[n(d(e(u)?"实时连接":"未连接"),1)]),_:1})]),_:1},8,["dot","color"]),t(e(f),{type:"primary",onClick:e(F),loading:e(_),class:"refresh-button"},{icon:a(()=>[t(e(C),null,{default:a(()=>[t(e(q))]),_:1})]),default:a(()=>[s[4]||(s[4]=n(" 刷新 "))]),_:1},8,["onClick","loading"])]),_:1})]),default:a(()=>[o("div",Y,[t(e(L),{cols:5,"x-gap":16,"y-gap":16},{default:a(()=>[(p(!0),N(W,null,$(e(y),l=>(p(),A(e(r),{key:l.label},{default:a(()=>[t(e(c),{class:H(["stat-card",l.type])},{default:a(()=>[o("div",Z,[o("div",ee,d(l.value),1),o("div",te,d(l.label),1)])]),_:2},1032,["class"])]),_:2},1024))),128))]),_:1})]),o("div",ae,[t(e(c),{class:"filter-card"},{default:a(()=>[t(e(L),{cols:4,"x-gap":16},{default:a(()=>[t(e(r),null,{default:a(()=>[t(e(m),{value:e(i).modelId,"onUpdate:value":s[0]||(s[0]=l=>e(i).modelId=l),placeholder:"选择模型",clearable:"",options:e(v).modelId,class:"filter-select"},null,8,["value","options"])]),_:1}),t(e(r),null,{default:a(()=>[t(e(m),{value:e(i).backendName,"onUpdate:value":s[1]||(s[1]=l=>e(i).backendName=l),placeholder:"选择后端",clearable:"",options:e(v).backendName,class:"filter-select"},null,8,["value","options"])]),_:1}),t(e(r),null,{default:a(()=>[t(e(m),{value:e(i).status,"onUpdate:value":s[2]||(s[2]=l=>e(i).status=l),placeholder:"请求状态",clearable:"",options:e(T),class:"filter-select"},null,8,["value","options"])]),_:1}),t(e(r),null,{default:a(()=>[t(e(K),{value:e(i).query,"onUpdate:value":s[3]||(s[3]=l=>e(i).query=l),placeholder:"搜索关键词",clearable:"",class:"filter-input"},{prefix:a(()=>[t(e(C),null,{default:a(()=>[t(e(w))]),_:1})]),_:1},8,["value"])]),_:1})]),_:1}),o("div",se,[t(e(b),null,{default:a(()=>[t(e(f),{onClick:e(I),class:"filter-button"},{default:a(()=>s[5]||(s[5]=[n("重置")])),_:1},8,["onClick"]),t(e(f),{onClick:e(M),type:"primary",class:"filter-button"},{default:a(()=>s[6]||(s[6]=[n("应用")])),_:1},8,["onClick"])]),_:1})])]),_:1})]),o("div",le,[t(e(c),{class:"trace-list-card"},{header:a(()=>[o("div",oe,[s[7]||(s[7]=o("div",{class:"list-title"},"追踪记录",-1)),t(e(k),{class:"list-count"},{default:a(()=>[n("共 "+d(e(g))+" 条记录",1)]),_:1})])]),default:a(()=>[t(e(Q),{columns:e(P),data:e(x),loading:e(_),pagination:{page:e(S),pageSize:e(h),itemCount:e(g),pageCount:e(z),showSizePicker:!0,pageSizes:[10,20,50,100],onUpdatePage:e(U),onUpdatePageSize:e(B),prefix:({itemCount:l})=>`共 ${l} 条记录`},bordered:!1,class:"trace-table"},null,8,["columns","data","loading","pagination"])]),_:1})])]),_:1})]))}});const he=J(ie,[["__scopeId","data-v-73921880"]]);export{he as default};
