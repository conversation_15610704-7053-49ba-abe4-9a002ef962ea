import{d as U0,u as Z0,b as H0,r as Q,e as V0,c as su,k as S,i as E,j as D,B as G0,y as W0,o as M,s as Wu,a as L,N as ru,l as mu,t as Ju,g as P,C as eu,p as wu,m as $,F as J0,q as Q0,D as X0,E as Y0,x as K0,z as Qu,A as Su,G as ue,_ as ee}from"./index.js";import{i as tu}from"./im.js";import{N as te,D as re}from"./DynamicConfigForm.js";import{A as ne}from"./ArrowBackOutline.js";import{N as ce}from"./Alert.js";import{A as Xu}from"./AddOutline.js";import{S as ie}from"./SaveOutline.js";import{N as oe,a as Yu}from"./FormItem.js";import{N as ae}from"./Divider.js";import{N as se}from"./Thing.js";import{N as le}from"./Popconfirm.js";import{N as Ku}from"./Empty.js";import{N as fe}from"./Input.js";import{N as de}from"./Switch.js";import{N as he}from"./Spin.js";import"./cryptojs.js";import"./Select.js";import"./use-locale.js";import"./en-US.js";import"./Checkmark.js";const u0={};function be(u){let e=u0[u];if(e)return e;e=u0[u]=[];for(let t=0;t<128;t++){const n=String.fromCharCode(t);e.push(n)}for(let t=0;t<u.length;t++){const n=u.charCodeAt(t);e[n]="%"+("0"+n.toString(16).toUpperCase()).slice(-2)}return e}function iu(u,e){typeof e!="string"&&(e=iu.defaultChars);const t=be(e);return u.replace(/(%[a-f0-9]{2})+/gi,function(n){let r="";for(let c=0,o=n.length;c<o;c+=3){const i=parseInt(n.slice(c+1,c+3),16);if(i<128){r+=t[i];continue}if((i&224)===192&&c+3<o){const a=parseInt(n.slice(c+4,c+6),16);if((a&192)===128){const s=i<<6&1984|a&63;s<128?r+="��":r+=String.fromCharCode(s),c+=3;continue}}if((i&240)===224&&c+6<o){const a=parseInt(n.slice(c+4,c+6),16),s=parseInt(n.slice(c+7,c+9),16);if((a&192)===128&&(s&192)===128){const l=i<<12&61440|a<<6&4032|s&63;l<2048||l>=55296&&l<=57343?r+="���":r+=String.fromCharCode(l),c+=6;continue}}if((i&248)===240&&c+9<o){const a=parseInt(n.slice(c+4,c+6),16),s=parseInt(n.slice(c+7,c+9),16),l=parseInt(n.slice(c+10,c+12),16);if((a&192)===128&&(s&192)===128&&(l&192)===128){let d=i<<18&1835008|a<<12&258048|s<<6&4032|l&63;d<65536||d>1114111?r+="����":(d-=65536,r+=String.fromCharCode(55296+(d>>10),56320+(d&1023))),c+=9;continue}}r+="�"}return r})}iu.defaultChars=";/?:@&=+$,#";iu.componentChars="";const e0={};function pe(u){let e=e0[u];if(e)return e;e=e0[u]=[];for(let t=0;t<128;t++){const n=String.fromCharCode(t);/^[0-9a-z]$/i.test(n)?e.push(n):e.push("%"+("0"+t.toString(16).toUpperCase()).slice(-2))}for(let t=0;t<u.length;t++)e[u.charCodeAt(t)]=u[t];return e}function bu(u,e,t){typeof e!="string"&&(t=e,e=bu.defaultChars),typeof t>"u"&&(t=!0);const n=pe(e);let r="";for(let c=0,o=u.length;c<o;c++){const i=u.charCodeAt(c);if(t&&i===37&&c+2<o&&/^[0-9a-f]{2}$/i.test(u.slice(c+1,c+3))){r+=u.slice(c,c+3),c+=2;continue}if(i<128){r+=n[i];continue}if(i>=55296&&i<=57343){if(i>=55296&&i<=56319&&c+1<o){const a=u.charCodeAt(c+1);if(a>=56320&&a<=57343){r+=encodeURIComponent(u[c]+u[c+1]),c++;continue}}r+="%EF%BF%BD";continue}r+=encodeURIComponent(u[c])}return r}bu.defaultChars=";/?:@&=+$,-_.!~*'()#";bu.componentChars="-_.!~*'()";function Ou(u){let e="";return e+=u.protocol||"",e+=u.slashes?"//":"",e+=u.auth?u.auth+"@":"",u.hostname&&u.hostname.indexOf(":")!==-1?e+="["+u.hostname+"]":e+=u.hostname||"",e+=u.port?":"+u.port:"",e+=u.pathname||"",e+=u.search||"",e+=u.hash||"",e}function Du(){this.protocol=null,this.slashes=null,this.auth=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.pathname=null}const xe=/^([a-z0-9.+-]+:)/i,_e=/:[0-9]*$/,me=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,ke=["<",">",'"',"`"," ","\r",`
`,"	"],ge=["{","}","|","\\","^","`"].concat(ke),De=["'"].concat(ge),t0=["%","/","?",";","#"].concat(De),r0=["/","?","#"],Ce=255,n0=/^[+a-z0-9A-Z_-]{0,63}$/,Ee=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,c0={javascript:!0,"javascript:":!0},i0={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0};function ju(u,e){if(u&&u instanceof Du)return u;const t=new Du;return t.parse(u,e),t}Du.prototype.parse=function(u,e){let t,n,r,c=u;if(c=c.trim(),!e&&u.split("#").length===1){const s=me.exec(c);if(s)return this.pathname=s[1],s[2]&&(this.search=s[2]),this}let o=xe.exec(c);if(o&&(o=o[0],t=o.toLowerCase(),this.protocol=o,c=c.substr(o.length)),(e||o||c.match(/^\/\/[^@\/]+@[^@\/]+/))&&(r=c.substr(0,2)==="//",r&&!(o&&c0[o])&&(c=c.substr(2),this.slashes=!0)),!c0[o]&&(r||o&&!i0[o])){let s=-1;for(let f=0;f<r0.length;f++)n=c.indexOf(r0[f]),n!==-1&&(s===-1||n<s)&&(s=n);let l,d;s===-1?d=c.lastIndexOf("@"):d=c.lastIndexOf("@",s),d!==-1&&(l=c.slice(0,d),c=c.slice(d+1),this.auth=l),s=-1;for(let f=0;f<t0.length;f++)n=c.indexOf(t0[f]),n!==-1&&(s===-1||n<s)&&(s=n);s===-1&&(s=c.length),c[s-1]===":"&&s--;const b=c.slice(0,s);c=c.slice(s),this.parseHost(b),this.hostname=this.hostname||"";const h=this.hostname[0]==="["&&this.hostname[this.hostname.length-1]==="]";if(!h){const f=this.hostname.split(/\./);for(let k=0,C=f.length;k<C;k++){const v=f[k];if(v&&!v.match(n0)){let x="";for(let _=0,m=v.length;_<m;_++)v.charCodeAt(_)>127?x+="x":x+=v[_];if(!x.match(n0)){const _=f.slice(0,k),m=f.slice(k+1),g=v.match(Ee);g&&(_.push(g[1]),m.unshift(g[2])),m.length&&(c=m.join(".")+c),this.hostname=_.join(".");break}}}}this.hostname.length>Ce&&(this.hostname=""),h&&(this.hostname=this.hostname.substr(1,this.hostname.length-2))}const i=c.indexOf("#");i!==-1&&(this.hash=c.substr(i),c=c.slice(0,i));const a=c.indexOf("?");return a!==-1&&(this.search=c.substr(a),c=c.slice(0,a)),c&&(this.pathname=c),i0[t]&&this.hostname&&!this.pathname&&(this.pathname=""),this};Du.prototype.parseHost=function(u){let e=_e.exec(u);e&&(e=e[0],e!==":"&&(this.port=e.substr(1)),u=u.substr(0,u.length-e.length)),u&&(this.hostname=u)};const Ae=Object.freeze(Object.defineProperty({__proto__:null,decode:iu,encode:bu,format:Ou,parse:ju},Symbol.toStringTag,{value:"Module"})),m0=/[\0-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,k0=/[\0-\x1F\x7F-\x9F]/,ye=/[\xAD\u0600-\u0605\u061C\u06DD\u070F\u0890\u0891\u08E2\u180E\u200B-\u200F\u202A-\u202E\u2060-\u2064\u2066-\u206F\uFEFF\uFFF9-\uFFFB]|\uD804[\uDCBD\uDCCD]|\uD80D[\uDC30-\uDC3F]|\uD82F[\uDCA0-\uDCA3]|\uD834[\uDD73-\uDD7A]|\uDB40[\uDC01\uDC20-\uDC7F]/,$u=/[!-#%-\*,-\/:;\?@\[-\]_\{\}\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD803[\uDEAD\uDF55-\uDF59\uDF86-\uDF89]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDC4B-\uDC4F\uDC5A\uDC5B\uDC5D\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDE60-\uDE6C\uDEB9\uDF3C-\uDF3E]|\uD806[\uDC3B\uDD44-\uDD46\uDDE2\uDE3F-\uDE46\uDE9A-\uDE9C\uDE9E-\uDEA2\uDF00-\uDF09]|\uD807[\uDC41-\uDC45\uDC70\uDC71\uDEF7\uDEF8\uDF43-\uDF4F\uDFFF]|\uD809[\uDC70-\uDC74]|\uD80B[\uDFF1\uDFF2]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD81B[\uDE97-\uDE9A\uDFE2]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]|\uD83A[\uDD5E\uDD5F]/,g0=/[\$\+<->\^`\|~\xA2-\xA6\xA8\xA9\xAC\xAE-\xB1\xB4\xB8\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0384\u0385\u03F6\u0482\u058D-\u058F\u0606-\u0608\u060B\u060E\u060F\u06DE\u06E9\u06FD\u06FE\u07F6\u07FE\u07FF\u0888\u09F2\u09F3\u09FA\u09FB\u0AF1\u0B70\u0BF3-\u0BFA\u0C7F\u0D4F\u0D79\u0E3F\u0F01-\u0F03\u0F13\u0F15-\u0F17\u0F1A-\u0F1F\u0F34\u0F36\u0F38\u0FBE-\u0FC5\u0FC7-\u0FCC\u0FCE\u0FCF\u0FD5-\u0FD8\u109E\u109F\u1390-\u1399\u166D\u17DB\u1940\u19DE-\u19FF\u1B61-\u1B6A\u1B74-\u1B7C\u1FBD\u1FBF-\u1FC1\u1FCD-\u1FCF\u1FDD-\u1FDF\u1FED-\u1FEF\u1FFD\u1FFE\u2044\u2052\u207A-\u207C\u208A-\u208C\u20A0-\u20C0\u2100\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F\u218A\u218B\u2190-\u2307\u230C-\u2328\u232B-\u2426\u2440-\u244A\u249C-\u24E9\u2500-\u2767\u2794-\u27C4\u27C7-\u27E5\u27F0-\u2982\u2999-\u29D7\u29DC-\u29FB\u29FE-\u2B73\u2B76-\u2B95\u2B97-\u2BFF\u2CE5-\u2CEA\u2E50\u2E51\u2E80-\u2E99\u2E9B-\u2EF3\u2F00-\u2FD5\u2FF0-\u2FFF\u3004\u3012\u3013\u3020\u3036\u3037\u303E\u303F\u309B\u309C\u3190\u3191\u3196-\u319F\u31C0-\u31E3\u31EF\u3200-\u321E\u322A-\u3247\u3250\u3260-\u327F\u328A-\u32B0\u32C0-\u33FF\u4DC0-\u4DFF\uA490-\uA4C6\uA700-\uA716\uA720\uA721\uA789\uA78A\uA828-\uA82B\uA836-\uA839\uAA77-\uAA79\uAB5B\uAB6A\uAB6B\uFB29\uFBB2-\uFBC2\uFD40-\uFD4F\uFDCF\uFDFC-\uFDFF\uFE62\uFE64-\uFE66\uFE69\uFF04\uFF0B\uFF1C-\uFF1E\uFF3E\uFF40\uFF5C\uFF5E\uFFE0-\uFFE6\uFFE8-\uFFEE\uFFFC\uFFFD]|\uD800[\uDD37-\uDD3F\uDD79-\uDD89\uDD8C-\uDD8E\uDD90-\uDD9C\uDDA0\uDDD0-\uDDFC]|\uD802[\uDC77\uDC78\uDEC8]|\uD805\uDF3F|\uD807[\uDFD5-\uDFF1]|\uD81A[\uDF3C-\uDF3F\uDF45]|\uD82F\uDC9C|\uD833[\uDF50-\uDFC3]|\uD834[\uDC00-\uDCF5\uDD00-\uDD26\uDD29-\uDD64\uDD6A-\uDD6C\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDDEA\uDE00-\uDE41\uDE45\uDF00-\uDF56]|\uD835[\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85\uDE86]|\uD838[\uDD4F\uDEFF]|\uD83B[\uDCAC\uDCB0\uDD2E\uDEF0\uDEF1]|\uD83C[\uDC00-\uDC2B\uDC30-\uDC93\uDCA0-\uDCAE\uDCB1-\uDCBF\uDCC1-\uDCCF\uDCD1-\uDCF5\uDD0D-\uDDAD\uDDE6-\uDE02\uDE10-\uDE3B\uDE40-\uDE48\uDE50\uDE51\uDE60-\uDE65\uDF00-\uDFFF]|\uD83D[\uDC00-\uDED7\uDEDC-\uDEEC\uDEF0-\uDEFC\uDF00-\uDF76\uDF7B-\uDFD9\uDFE0-\uDFEB\uDFF0]|\uD83E[\uDC00-\uDC0B\uDC10-\uDC47\uDC50-\uDC59\uDC60-\uDC87\uDC90-\uDCAD\uDCB0\uDCB1\uDD00-\uDE53\uDE60-\uDE6D\uDE70-\uDE7C\uDE80-\uDE88\uDE90-\uDEBD\uDEBF-\uDEC5\uDECE-\uDEDB\uDEE0-\uDEE8\uDEF0-\uDEF8\uDF00-\uDF92\uDF94-\uDFCA]/,D0=/[ \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/,Fe=Object.freeze(Object.defineProperty({__proto__:null,Any:m0,Cc:k0,Cf:ye,P:$u,S:g0,Z:D0},Symbol.toStringTag,{value:"Module"})),ve=new Uint16Array('ᵁ<Õıʊҝջאٵ۞ޢߖࠏ੊ઑඡ๭༉༦჊ረዡᐕᒝᓃᓟᔥ\0\0\0\0\0\0ᕫᛍᦍᰒᷝ὾⁠↰⊍⏀⏻⑂⠤⤒ⴈ⹈⿎〖㊺㘹㞬㣾㨨㩱㫠㬮ࠀEMabcfglmnoprstu\\bfms¦³¹ÈÏlig耻Æ䃆P耻&䀦cute耻Á䃁reve;䄂Āiyx}rc耻Â䃂;䐐r;쀀𝔄rave耻À䃀pha;䎑acr;䄀d;橓Āgp¡on;䄄f;쀀𝔸plyFunction;恡ing耻Å䃅Ācs¾Ãr;쀀𝒜ign;扔ilde耻Ã䃃ml耻Ä䃄ЀaceforsuåûþėĜĢħĪĀcrêòkslash;或Ŷöø;櫧ed;挆y;䐑ƀcrtąċĔause;戵noullis;愬a;䎒r;쀀𝔅pf;쀀𝔹eve;䋘còēmpeq;扎܀HOacdefhilorsuōőŖƀƞƢƵƷƺǜȕɳɸɾcy;䐧PY耻©䂩ƀcpyŝŢźute;䄆Ā;iŧŨ拒talDifferentialD;慅leys;愭ȀaeioƉƎƔƘron;䄌dil耻Ç䃇rc;䄈nint;戰ot;䄊ĀdnƧƭilla;䂸terDot;䂷òſi;䎧rcleȀDMPTǇǋǑǖot;抙inus;抖lus;投imes;抗oĀcsǢǸkwiseContourIntegral;戲eCurlyĀDQȃȏoubleQuote;思uote;怙ȀlnpuȞȨɇɕonĀ;eȥȦ户;橴ƀgitȯȶȺruent;扡nt;戯ourIntegral;戮ĀfrɌɎ;愂oduct;成nterClockwiseContourIntegral;戳oss;樯cr;쀀𝒞pĀ;Cʄʅ拓ap;才րDJSZacefiosʠʬʰʴʸˋ˗ˡ˦̳ҍĀ;oŹʥtrahd;椑cy;䐂cy;䐅cy;䐏ƀgrsʿ˄ˇger;怡r;憡hv;櫤Āayː˕ron;䄎;䐔lĀ;t˝˞戇a;䎔r;쀀𝔇Āaf˫̧Ācm˰̢riticalȀADGT̖̜̀̆cute;䂴oŴ̋̍;䋙bleAcute;䋝rave;䁠ilde;䋜ond;拄ferentialD;慆Ѱ̽\0\0\0͔͂\0Ѕf;쀀𝔻ƀ;DE͈͉͍䂨ot;惜qual;扐blèCDLRUVͣͲ΂ϏϢϸontourIntegraìȹoɴ͹\0\0ͻ»͉nArrow;懓Āeo·ΤftƀARTΐΖΡrrow;懐ightArrow;懔eåˊngĀLRΫτeftĀARγιrrow;柸ightArrow;柺ightArrow;柹ightĀATϘϞrrow;懒ee;抨pɁϩ\0\0ϯrrow;懑ownArrow;懕erticalBar;戥ǹABLRTaВЪаўѿͼrrowƀ;BUНОТ憓ar;椓pArrow;懵reve;䌑eft˒к\0ц\0ѐightVector;楐eeVector;楞ectorĀ;Bљњ憽ar;楖ightǔѧ\0ѱeeVector;楟ectorĀ;BѺѻ懁ar;楗eeĀ;A҆҇护rrow;憧ĀctҒҗr;쀀𝒟rok;䄐ࠀNTacdfglmopqstuxҽӀӄӋӞӢӧӮӵԡԯԶՒ՝ՠեG;䅊H耻Ð䃐cute耻É䃉ƀaiyӒӗӜron;䄚rc耻Ê䃊;䐭ot;䄖r;쀀𝔈rave耻È䃈ement;戈ĀapӺӾcr;䄒tyɓԆ\0\0ԒmallSquare;旻erySmallSquare;斫ĀgpԦԪon;䄘f;쀀𝔼silon;䎕uĀaiԼՉlĀ;TՂՃ橵ilde;扂librium;懌Āci՗՚r;愰m;橳a;䎗ml耻Ë䃋Āipժկsts;戃onentialE;慇ʀcfiosօֈ֍ֲ׌y;䐤r;쀀𝔉lledɓ֗\0\0֣mallSquare;旼erySmallSquare;斪Ͱֺ\0ֿ\0\0ׄf;쀀𝔽All;戀riertrf;愱cò׋؀JTabcdfgorstר׬ׯ׺؀ؒؖ؛؝أ٬ٲcy;䐃耻>䀾mmaĀ;d׷׸䎓;䏜reve;䄞ƀeiy؇،ؐdil;䄢rc;䄜;䐓ot;䄠r;쀀𝔊;拙pf;쀀𝔾eater̀EFGLSTصلَٖٛ٦qualĀ;Lؾؿ扥ess;招ullEqual;执reater;檢ess;扷lantEqual;橾ilde;扳cr;쀀𝒢;扫ЀAacfiosuڅڋږڛڞڪھۊRDcy;䐪Āctڐڔek;䋇;䁞irc;䄤r;愌lbertSpace;愋ǰگ\0ڲf;愍izontalLine;攀Āctۃۅòکrok;䄦mpńېۘownHumðįqual;扏܀EJOacdfgmnostuۺ۾܃܇܎ܚܞܡܨ݄ݸދޏޕcy;䐕lig;䄲cy;䐁cute耻Í䃍Āiyܓܘrc耻Î䃎;䐘ot;䄰r;愑rave耻Ì䃌ƀ;apܠܯܿĀcgܴܷr;䄪inaryI;慈lieóϝǴ݉\0ݢĀ;eݍݎ戬Āgrݓݘral;戫section;拂isibleĀCTݬݲomma;恣imes;恢ƀgptݿރވon;䄮f;쀀𝕀a;䎙cr;愐ilde;䄨ǫޚ\0ޞcy;䐆l耻Ï䃏ʀcfosuެ޷޼߂ߐĀiyޱ޵rc;䄴;䐙r;쀀𝔍pf;쀀𝕁ǣ߇\0ߌr;쀀𝒥rcy;䐈kcy;䐄΀HJacfosߤߨ߽߬߱ࠂࠈcy;䐥cy;䐌ppa;䎚Āey߶߻dil;䄶;䐚r;쀀𝔎pf;쀀𝕂cr;쀀𝒦րJTaceflmostࠥࠩࠬࡐࡣ঳সে্਷ੇcy;䐉耻<䀼ʀcmnpr࠷࠼ࡁࡄࡍute;䄹bda;䎛g;柪lacetrf;愒r;憞ƀaeyࡗ࡜ࡡron;䄽dil;䄻;䐛Āfsࡨ॰tԀACDFRTUVarࡾࢩࢱࣦ࣠ࣼयज़ΐ४Ānrࢃ࢏gleBracket;柨rowƀ;BR࢙࢚࢞憐ar;懤ightArrow;懆eiling;挈oǵࢷ\0ࣃbleBracket;柦nǔࣈ\0࣒eeVector;楡ectorĀ;Bࣛࣜ懃ar;楙loor;挊ightĀAV࣯ࣵrrow;憔ector;楎Āerँगeƀ;AVउऊऐ抣rrow;憤ector;楚iangleƀ;BEतथऩ抲ar;槏qual;抴pƀDTVषूौownVector;楑eeVector;楠ectorĀ;Bॖॗ憿ar;楘ectorĀ;B॥०憼ar;楒ightáΜs̀EFGLSTॾঋকঝঢভqualGreater;拚ullEqual;扦reater;扶ess;檡lantEqual;橽ilde;扲r;쀀𝔏Ā;eঽা拘ftarrow;懚idot;䄿ƀnpw৔ਖਛgȀLRlr৞৷ਂਐeftĀAR০৬rrow;柵ightArrow;柷ightArrow;柶eftĀarγਊightáοightáϊf;쀀𝕃erĀLRਢਬeftArrow;憙ightArrow;憘ƀchtਾੀੂòࡌ;憰rok;䅁;扪Ѐacefiosuਗ਼੝੠੷੼અઋ઎p;椅y;䐜Ādl੥੯iumSpace;恟lintrf;愳r;쀀𝔐nusPlus;戓pf;쀀𝕄cò੶;䎜ҀJacefostuણધભીଔଙඑ඗ඞcy;䐊cute;䅃ƀaey઴હાron;䅇dil;䅅;䐝ƀgswે૰଎ativeƀMTV૓૟૨ediumSpace;怋hiĀcn૦૘ë૙eryThiî૙tedĀGL૸ଆreaterGreateòٳessLesóੈLine;䀊r;쀀𝔑ȀBnptଢନଷ଺reak;恠BreakingSpace;䂠f;愕ڀ;CDEGHLNPRSTV୕ୖ୪୼஡௫ఄ౞಄ದ೘ൡඅ櫬Āou୛୤ngruent;扢pCap;扭oubleVerticalBar;戦ƀlqxஃஊ஛ement;戉ualĀ;Tஒஓ扠ilde;쀀≂̸ists;戄reater΀;EFGLSTஶஷ஽௉௓௘௥扯qual;扱ullEqual;쀀≧̸reater;쀀≫̸ess;批lantEqual;쀀⩾̸ilde;扵umpń௲௽ownHump;쀀≎̸qual;쀀≏̸eĀfsఊధtTriangleƀ;BEచఛడ拪ar;쀀⧏̸qual;括s̀;EGLSTవశ఼ౄోౘ扮qual;扰reater;扸ess;쀀≪̸lantEqual;쀀⩽̸ilde;扴estedĀGL౨౹reaterGreater;쀀⪢̸essLess;쀀⪡̸recedesƀ;ESಒಓಛ技qual;쀀⪯̸lantEqual;拠ĀeiಫಹverseElement;戌ghtTriangleƀ;BEೋೌ೒拫ar;쀀⧐̸qual;拭ĀquೝഌuareSuĀbp೨೹setĀ;E೰ೳ쀀⊏̸qual;拢ersetĀ;Eഃആ쀀⊐̸qual;拣ƀbcpഓതൎsetĀ;Eഛഞ쀀⊂⃒qual;抈ceedsȀ;ESTലള഻െ抁qual;쀀⪰̸lantEqual;拡ilde;쀀≿̸ersetĀ;E൘൛쀀⊃⃒qual;抉ildeȀ;EFT൮൯൵ൿ扁qual;扄ullEqual;扇ilde;扉erticalBar;戤cr;쀀𝒩ilde耻Ñ䃑;䎝܀Eacdfgmoprstuvලෂ෉෕ෛ෠෧෼ขภยา฿ไlig;䅒cute耻Ó䃓Āiy෎ීrc耻Ô䃔;䐞blac;䅐r;쀀𝔒rave耻Ò䃒ƀaei෮ෲ෶cr;䅌ga;䎩cron;䎟pf;쀀𝕆enCurlyĀDQฎบoubleQuote;怜uote;怘;橔Āclวฬr;쀀𝒪ash耻Ø䃘iŬื฼de耻Õ䃕es;樷ml耻Ö䃖erĀBP๋๠Āar๐๓r;怾acĀek๚๜;揞et;掴arenthesis;揜Ҁacfhilors๿ງຊຏຒດຝະ໼rtialD;戂y;䐟r;쀀𝔓i;䎦;䎠usMinus;䂱Āipຢອncareplanåڝf;愙Ȁ;eio຺ູ໠໤檻cedesȀ;EST່້໏໚扺qual;檯lantEqual;扼ilde;找me;怳Ādp໩໮uct;戏ortionĀ;aȥ໹l;戝Āci༁༆r;쀀𝒫;䎨ȀUfos༑༖༛༟OT耻"䀢r;쀀𝔔pf;愚cr;쀀𝒬؀BEacefhiorsu༾གྷཇའཱིྦྷྪྭ႖ႩႴႾarr;椐G耻®䂮ƀcnrཎནབute;䅔g;柫rĀ;tཛྷཝ憠l;椖ƀaeyཧཬཱron;䅘dil;䅖;䐠Ā;vླྀཹ愜erseĀEUྂྙĀlq྇ྎement;戋uilibrium;懋pEquilibrium;楯r»ཹo;䎡ghtЀACDFTUVa࿁࿫࿳ဢဨၛႇϘĀnr࿆࿒gleBracket;柩rowƀ;BL࿜࿝࿡憒ar;懥eftArrow;懄eiling;按oǵ࿹\0စbleBracket;柧nǔည\0နeeVector;楝ectorĀ;Bဝသ懂ar;楕loor;挋Āerိ၃eƀ;AVဵံြ抢rrow;憦ector;楛iangleƀ;BEၐၑၕ抳ar;槐qual;抵pƀDTVၣၮၸownVector;楏eeVector;楜ectorĀ;Bႂႃ憾ar;楔ectorĀ;B႑႒懀ar;楓Āpuႛ႞f;愝ndImplies;楰ightarrow;懛ĀchႹႼr;愛;憱leDelayed;槴ڀHOacfhimoqstuფჱჷჽᄙᄞᅑᅖᅡᅧᆵᆻᆿĀCcჩხHcy;䐩y;䐨FTcy;䐬cute;䅚ʀ;aeiyᄈᄉᄎᄓᄗ檼ron;䅠dil;䅞rc;䅜;䐡r;쀀𝔖ortȀDLRUᄪᄴᄾᅉownArrow»ОeftArrow»࢚ightArrow»࿝pArrow;憑gma;䎣allCircle;战pf;쀀𝕊ɲᅭ\0\0ᅰt;戚areȀ;ISUᅻᅼᆉᆯ斡ntersection;抓uĀbpᆏᆞsetĀ;Eᆗᆘ抏qual;抑ersetĀ;Eᆨᆩ抐qual;抒nion;抔cr;쀀𝒮ar;拆ȀbcmpᇈᇛሉላĀ;sᇍᇎ拐etĀ;Eᇍᇕqual;抆ĀchᇠህeedsȀ;ESTᇭᇮᇴᇿ扻qual;檰lantEqual;扽ilde;承Tháྌ;我ƀ;esሒሓሣ拑rsetĀ;Eሜም抃qual;抇et»ሓրHRSacfhiorsሾቄ቉ቕ቞ቱቶኟዂወዑORN耻Þ䃞ADE;愢ĀHc቎ቒcy;䐋y;䐦Ābuቚቜ;䀉;䎤ƀaeyብቪቯron;䅤dil;䅢;䐢r;쀀𝔗Āeiቻ኉ǲኀ\0ኇefore;戴a;䎘Ācn኎ኘkSpace;쀀  Space;怉ldeȀ;EFTካኬኲኼ戼qual;扃ullEqual;扅ilde;扈pf;쀀𝕋ipleDot;惛Āctዖዛr;쀀𝒯rok;䅦ૡዷጎጚጦ\0ጬጱ\0\0\0\0\0ጸጽ፷ᎅ\0᏿ᐄᐊᐐĀcrዻጁute耻Ú䃚rĀ;oጇገ憟cir;楉rǣጓ\0጖y;䐎ve;䅬Āiyጞጣrc耻Û䃛;䐣blac;䅰r;쀀𝔘rave耻Ù䃙acr;䅪Ādiፁ፩erĀBPፈ፝Āarፍፐr;䁟acĀekፗፙ;揟et;掵arenthesis;揝onĀ;P፰፱拃lus;抎Āgp፻፿on;䅲f;쀀𝕌ЀADETadps᎕ᎮᎸᏄϨᏒᏗᏳrrowƀ;BDᅐᎠᎤar;椒ownArrow;懅ownArrow;憕quilibrium;楮eeĀ;AᏋᏌ报rrow;憥ownáϳerĀLRᏞᏨeftArrow;憖ightArrow;憗iĀ;lᏹᏺ䏒on;䎥ing;䅮cr;쀀𝒰ilde;䅨ml耻Ü䃜ҀDbcdefosvᐧᐬᐰᐳᐾᒅᒊᒐᒖash;披ar;櫫y;䐒ashĀ;lᐻᐼ抩;櫦Āerᑃᑅ;拁ƀbtyᑌᑐᑺar;怖Ā;iᑏᑕcalȀBLSTᑡᑥᑪᑴar;戣ine;䁼eparator;杘ilde;所ThinSpace;怊r;쀀𝔙pf;쀀𝕍cr;쀀𝒱dash;抪ʀcefosᒧᒬᒱᒶᒼirc;䅴dge;拀r;쀀𝔚pf;쀀𝕎cr;쀀𝒲Ȁfiosᓋᓐᓒᓘr;쀀𝔛;䎞pf;쀀𝕏cr;쀀𝒳ҀAIUacfosuᓱᓵᓹᓽᔄᔏᔔᔚᔠcy;䐯cy;䐇cy;䐮cute耻Ý䃝Āiyᔉᔍrc;䅶;䐫r;쀀𝔜pf;쀀𝕐cr;쀀𝒴ml;䅸ЀHacdefosᔵᔹᔿᕋᕏᕝᕠᕤcy;䐖cute;䅹Āayᕄᕉron;䅽;䐗ot;䅻ǲᕔ\0ᕛoWidtè૙a;䎖r;愨pf;愤cr;쀀𝒵௡ᖃᖊᖐ\0ᖰᖶᖿ\0\0\0\0ᗆᗛᗫᙟ᙭\0ᚕ᚛ᚲᚹ\0ᚾcute耻á䃡reve;䄃̀;Ediuyᖜᖝᖡᖣᖨᖭ戾;쀀∾̳;房rc耻â䃢te肻´̆;䐰lig耻æ䃦Ā;r²ᖺ;쀀𝔞rave耻à䃠ĀepᗊᗖĀfpᗏᗔsym;愵èᗓha;䎱ĀapᗟcĀclᗤᗧr;䄁g;樿ɤᗰ\0\0ᘊʀ;adsvᗺᗻᗿᘁᘇ戧nd;橕;橜lope;橘;橚΀;elmrszᘘᘙᘛᘞᘿᙏᙙ戠;榤e»ᘙsdĀ;aᘥᘦ戡ѡᘰᘲᘴᘶᘸᘺᘼᘾ;榨;榩;榪;榫;榬;榭;榮;榯tĀ;vᙅᙆ戟bĀ;dᙌᙍ抾;榝Āptᙔᙗh;戢»¹arr;捼Āgpᙣᙧon;䄅f;쀀𝕒΀;Eaeiop዁ᙻᙽᚂᚄᚇᚊ;橰cir;橯;扊d;手s;䀧roxĀ;e዁ᚒñᚃing耻å䃥ƀctyᚡᚦᚨr;쀀𝒶;䀪mpĀ;e዁ᚯñʈilde耻ã䃣ml耻ä䃤Āciᛂᛈoninôɲnt;樑ࠀNabcdefiklnoprsu᛭ᛱᜰ᜼ᝃᝈ᝸᝽០៦ᠹᡐᜍ᤽᥈ᥰot;櫭Ācrᛶ᜞kȀcepsᜀᜅᜍᜓong;扌psilon;䏶rime;怵imĀ;e᜚᜛戽q;拍Ŷᜢᜦee;抽edĀ;gᜬᜭ挅e»ᜭrkĀ;t፜᜷brk;掶Āoyᜁᝁ;䐱quo;怞ʀcmprtᝓ᝛ᝡᝤᝨausĀ;eĊĉptyv;榰séᜌnoõēƀahwᝯ᝱ᝳ;䎲;愶een;扬r;쀀𝔟g΀costuvwឍឝឳេ៕៛៞ƀaiuបពរðݠrc;旯p»፱ƀdptឤឨឭot;樀lus;樁imes;樂ɱឹ\0\0ើcup;樆ar;昅riangleĀdu៍្own;施p;斳plus;樄eåᑄåᒭarow;植ƀako៭ᠦᠵĀcn៲ᠣkƀlst៺֫᠂ozenge;槫riangleȀ;dlr᠒᠓᠘᠝斴own;斾eft;旂ight;斸k;搣Ʊᠫ\0ᠳƲᠯ\0ᠱ;斒;斑4;斓ck;斈ĀeoᠾᡍĀ;qᡃᡆ쀀=⃥uiv;쀀≡⃥t;挐Ȁptwxᡙᡞᡧᡬf;쀀𝕓Ā;tᏋᡣom»Ꮜtie;拈؀DHUVbdhmptuvᢅᢖᢪᢻᣗᣛᣬ᣿ᤅᤊᤐᤡȀLRlrᢎᢐᢒᢔ;敗;敔;敖;敓ʀ;DUduᢡᢢᢤᢦᢨ敐;敦;敩;敤;敧ȀLRlrᢳᢵᢷᢹ;敝;敚;敜;教΀;HLRhlrᣊᣋᣍᣏᣑᣓᣕ救;敬;散;敠;敫;敢;敟ox;槉ȀLRlrᣤᣦᣨᣪ;敕;敒;攐;攌ʀ;DUduڽ᣷᣹᣻᣽;敥;敨;攬;攴inus;抟lus;択imes;抠ȀLRlrᤙᤛᤝ᤟;敛;敘;攘;攔΀;HLRhlrᤰᤱᤳᤵᤷ᤻᤹攂;敪;敡;敞;攼;攤;攜Āevģ᥂bar耻¦䂦Ȁceioᥑᥖᥚᥠr;쀀𝒷mi;恏mĀ;e᜚᜜lƀ;bhᥨᥩᥫ䁜;槅sub;柈Ŭᥴ᥾lĀ;e᥹᥺怢t»᥺pƀ;Eeįᦅᦇ;檮Ā;qۜۛೡᦧ\0᧨ᨑᨕᨲ\0ᨷᩐ\0\0᪴\0\0᫁\0\0ᬡᬮ᭍᭒\0᯽\0ᰌƀcpr᦭ᦲ᧝ute;䄇̀;abcdsᦿᧀᧄ᧊᧕᧙戩nd;橄rcup;橉Āau᧏᧒p;橋p;橇ot;橀;쀀∩︀Āeo᧢᧥t;恁îړȀaeiu᧰᧻ᨁᨅǰ᧵\0᧸s;橍on;䄍dil耻ç䃧rc;䄉psĀ;sᨌᨍ橌m;橐ot;䄋ƀdmnᨛᨠᨦil肻¸ƭptyv;榲t脀¢;eᨭᨮ䂢räƲr;쀀𝔠ƀceiᨽᩀᩍy;䑇ckĀ;mᩇᩈ朓ark»ᩈ;䏇r΀;Ecefms᩟᩠ᩢᩫ᪤᪪᪮旋;槃ƀ;elᩩᩪᩭ䋆q;扗eɡᩴ\0\0᪈rrowĀlr᩼᪁eft;憺ight;憻ʀRSacd᪒᪔᪖᪚᪟»ཇ;擈st;抛irc;抚ash;抝nint;樐id;櫯cir;槂ubsĀ;u᪻᪼晣it»᪼ˬ᫇᫔᫺\0ᬊonĀ;eᫍᫎ䀺Ā;qÇÆɭ᫙\0\0᫢aĀ;t᫞᫟䀬;䁀ƀ;fl᫨᫩᫫戁îᅠeĀmx᫱᫶ent»᫩eóɍǧ᫾\0ᬇĀ;dኻᬂot;橭nôɆƀfryᬐᬔᬗ;쀀𝕔oäɔ脀©;sŕᬝr;愗Āaoᬥᬩrr;憵ss;朗Ācuᬲᬷr;쀀𝒸Ābpᬼ᭄Ā;eᭁᭂ櫏;櫑Ā;eᭉᭊ櫐;櫒dot;拯΀delprvw᭠᭬᭷ᮂᮬᯔ᯹arrĀlr᭨᭪;椸;椵ɰ᭲\0\0᭵r;拞c;拟arrĀ;p᭿ᮀ憶;椽̀;bcdosᮏᮐᮖᮡᮥᮨ截rcap;橈Āauᮛᮞp;橆p;橊ot;抍r;橅;쀀∪︀Ȁalrv᮵ᮿᯞᯣrrĀ;mᮼᮽ憷;椼yƀevwᯇᯔᯘqɰᯎ\0\0ᯒreã᭳uã᭵ee;拎edge;拏en耻¤䂤earrowĀlrᯮ᯳eft»ᮀight»ᮽeäᯝĀciᰁᰇoninôǷnt;戱lcty;挭ঀAHabcdefhijlorstuwz᰸᰻᰿ᱝᱩᱵᲊᲞᲬᲷ᳻᳿ᴍᵻᶑᶫᶻ᷆᷍rò΁ar;楥Ȁglrs᱈ᱍ᱒᱔ger;怠eth;愸òᄳhĀ;vᱚᱛ怐»ऊūᱡᱧarow;椏aã̕Āayᱮᱳron;䄏;䐴ƀ;ao̲ᱼᲄĀgrʿᲁr;懊tseq;橷ƀglmᲑᲔᲘ耻°䂰ta;䎴ptyv;榱ĀirᲣᲨsht;楿;쀀𝔡arĀlrᲳᲵ»ࣜ»သʀaegsv᳂͸᳖᳜᳠mƀ;oș᳊᳔ndĀ;ș᳑uit;晦amma;䏝in;拲ƀ;io᳧᳨᳸䃷de脀÷;o᳧ᳰntimes;拇nø᳷cy;䑒cɯᴆ\0\0ᴊrn;挞op;挍ʀlptuwᴘᴝᴢᵉᵕlar;䀤f;쀀𝕕ʀ;emps̋ᴭᴷᴽᵂqĀ;d͒ᴳot;扑inus;戸lus;戔quare;抡blebarwedgåúnƀadhᄮᵝᵧownarrowóᲃarpoonĀlrᵲᵶefôᲴighôᲶŢᵿᶅkaro÷གɯᶊ\0\0ᶎrn;挟op;挌ƀcotᶘᶣᶦĀryᶝᶡ;쀀𝒹;䑕l;槶rok;䄑Ādrᶰᶴot;拱iĀ;fᶺ᠖斿Āah᷀᷃ròЩaòྦangle;榦Āci᷒ᷕy;䑟grarr;柿ऀDacdefglmnopqrstuxḁḉḙḸոḼṉṡṾấắẽỡἪἷὄ὎὚ĀDoḆᴴoôᲉĀcsḎḔute耻é䃩ter;橮ȀaioyḢḧḱḶron;䄛rĀ;cḭḮ扖耻ê䃪lon;払;䑍ot;䄗ĀDrṁṅot;扒;쀀𝔢ƀ;rsṐṑṗ檚ave耻è䃨Ā;dṜṝ檖ot;檘Ȁ;ilsṪṫṲṴ檙nters;揧;愓Ā;dṹṺ檕ot;檗ƀapsẅẉẗcr;䄓tyƀ;svẒẓẕ戅et»ẓpĀ1;ẝẤĳạả;怄;怅怃ĀgsẪẬ;䅋p;怂ĀgpẴẸon;䄙f;쀀𝕖ƀalsỄỎỒrĀ;sỊị拕l;槣us;橱iƀ;lvỚớở䎵on»ớ;䏵ȀcsuvỪỳἋἣĀioữḱrc»Ḯɩỹ\0\0ỻíՈantĀglἂἆtr»ṝess»Ṻƀaeiἒ἖Ἒls;䀽st;扟vĀ;DȵἠD;橸parsl;槥ĀDaἯἳot;打rr;楱ƀcdiἾὁỸr;愯oô͒ĀahὉὋ;䎷耻ð䃰Āmrὓὗl耻ë䃫o;悬ƀcipὡὤὧl;䀡sôծĀeoὬὴctatioîՙnentialåչৡᾒ\0ᾞ\0ᾡᾧ\0\0ῆῌ\0ΐ\0ῦῪ \0 ⁚llingdotseñṄy;䑄male;晀ƀilrᾭᾳ῁lig;耀ﬃɩᾹ\0\0᾽g;耀ﬀig;耀ﬄ;쀀𝔣lig;耀ﬁlig;쀀fjƀaltῙ῜ῡt;晭ig;耀ﬂns;斱of;䆒ǰ΅\0ῳf;쀀𝕗ĀakֿῷĀ;vῼ´拔;櫙artint;樍Āao‌⁕Ācs‑⁒α‚‰‸⁅⁈\0⁐β•‥‧‪‬\0‮耻½䂽;慓耻¼䂼;慕;慙;慛Ƴ‴\0‶;慔;慖ʴ‾⁁\0\0⁃耻¾䂾;慗;慜5;慘ƶ⁌\0⁎;慚;慝8;慞l;恄wn;挢cr;쀀𝒻ࢀEabcdefgijlnorstv₂₉₟₥₰₴⃰⃵⃺⃿℃ℒℸ̗ℾ⅒↞Ā;lٍ₇;檌ƀcmpₐₕ₝ute;䇵maĀ;dₜ᳚䎳;檆reve;䄟Āiy₪₮rc;䄝;䐳ot;䄡Ȁ;lqsؾق₽⃉ƀ;qsؾٌ⃄lanô٥Ȁ;cdl٥⃒⃥⃕c;檩otĀ;o⃜⃝檀Ā;l⃢⃣檂;檄Ā;e⃪⃭쀀⋛︀s;檔r;쀀𝔤Ā;gٳ؛mel;愷cy;䑓Ȁ;Eajٚℌℎℐ;檒;檥;檤ȀEaesℛℝ℩ℴ;扩pĀ;p℣ℤ檊rox»ℤĀ;q℮ℯ檈Ā;q℮ℛim;拧pf;쀀𝕘Āci⅃ⅆr;愊mƀ;el٫ⅎ⅐;檎;檐茀>;cdlqr׮ⅠⅪⅮⅳⅹĀciⅥⅧ;檧r;橺ot;拗Par;榕uest;橼ʀadelsↄⅪ←ٖ↛ǰ↉\0↎proø₞r;楸qĀlqؿ↖lesó₈ií٫Āen↣↭rtneqq;쀀≩︀Å↪ԀAabcefkosy⇄⇇⇱⇵⇺∘∝∯≨≽ròΠȀilmr⇐⇔⇗⇛rsðᒄf»․ilôکĀdr⇠⇤cy;䑊ƀ;cwࣴ⇫⇯ir;楈;憭ar;意irc;䄥ƀalr∁∎∓rtsĀ;u∉∊晥it»∊lip;怦con;抹r;쀀𝔥sĀew∣∩arow;椥arow;椦ʀamopr∺∾≃≞≣rr;懿tht;戻kĀlr≉≓eftarrow;憩ightarrow;憪f;쀀𝕙bar;怕ƀclt≯≴≸r;쀀𝒽asè⇴rok;䄧Ābp⊂⊇ull;恃hen»ᱛૡ⊣\0⊪\0⊸⋅⋎\0⋕⋳\0\0⋸⌢⍧⍢⍿\0⎆⎪⎴cute耻í䃭ƀ;iyݱ⊰⊵rc耻î䃮;䐸Ācx⊼⊿y;䐵cl耻¡䂡ĀfrΟ⋉;쀀𝔦rave耻ì䃬Ȁ;inoܾ⋝⋩⋮Āin⋢⋦nt;樌t;戭fin;槜ta;愩lig;䄳ƀaop⋾⌚⌝ƀcgt⌅⌈⌗r;䄫ƀelpܟ⌏⌓inåގarôܠh;䄱f;抷ed;䆵ʀ;cfotӴ⌬⌱⌽⍁are;愅inĀ;t⌸⌹戞ie;槝doô⌙ʀ;celpݗ⍌⍐⍛⍡al;抺Āgr⍕⍙eróᕣã⍍arhk;樗rod;樼Ȁcgpt⍯⍲⍶⍻y;䑑on;䄯f;쀀𝕚a;䎹uest耻¿䂿Āci⎊⎏r;쀀𝒾nʀ;EdsvӴ⎛⎝⎡ӳ;拹ot;拵Ā;v⎦⎧拴;拳Ā;iݷ⎮lde;䄩ǫ⎸\0⎼cy;䑖l耻ï䃯̀cfmosu⏌⏗⏜⏡⏧⏵Āiy⏑⏕rc;䄵;䐹r;쀀𝔧ath;䈷pf;쀀𝕛ǣ⏬\0⏱r;쀀𝒿rcy;䑘kcy;䑔Ѐacfghjos␋␖␢␧␭␱␵␻ppaĀ;v␓␔䎺;䏰Āey␛␠dil;䄷;䐺r;쀀𝔨reen;䄸cy;䑅cy;䑜pf;쀀𝕜cr;쀀𝓀஀ABEHabcdefghjlmnoprstuv⑰⒁⒆⒍⒑┎┽╚▀♎♞♥♹♽⚚⚲⛘❝❨➋⟀⠁⠒ƀart⑷⑺⑼rò৆òΕail;椛arr;椎Ā;gঔ⒋;檋ar;楢ॣ⒥\0⒪\0⒱\0\0\0\0\0⒵Ⓔ\0ⓆⓈⓍ\0⓹ute;䄺mptyv;榴raîࡌbda;䎻gƀ;dlࢎⓁⓃ;榑åࢎ;檅uo耻«䂫rЀ;bfhlpst࢙ⓞⓦⓩ⓫⓮⓱⓵Ā;f࢝ⓣs;椟s;椝ë≒p;憫l;椹im;楳l;憢ƀ;ae⓿─┄檫il;椙Ā;s┉┊檭;쀀⪭︀ƀabr┕┙┝rr;椌rk;杲Āak┢┬cĀek┨┪;䁻;䁛Āes┱┳;榋lĀdu┹┻;榏;榍Ȁaeuy╆╋╖╘ron;䄾Ādi═╔il;䄼ìࢰâ┩;䐻Ȁcqrs╣╦╭╽a;椶uoĀ;rนᝆĀdu╲╷har;楧shar;楋h;憲ʀ;fgqs▋▌উ◳◿扤tʀahlrt▘▤▷◂◨rrowĀ;t࢙□aé⓶arpoonĀdu▯▴own»њp»०eftarrows;懇ightƀahs◍◖◞rrowĀ;sࣴࢧarpoonó྘quigarro÷⇰hreetimes;拋ƀ;qs▋ও◺lanôবʀ;cdgsব☊☍☝☨c;檨otĀ;o☔☕橿Ā;r☚☛檁;檃Ā;e☢☥쀀⋚︀s;檓ʀadegs☳☹☽♉♋pproøⓆot;拖qĀgq♃♅ôউgtò⒌ôছiíলƀilr♕࣡♚sht;楼;쀀𝔩Ā;Eজ♣;檑š♩♶rĀdu▲♮Ā;l॥♳;楪lk;斄cy;䑙ʀ;achtੈ⚈⚋⚑⚖rò◁orneòᴈard;楫ri;旺Āio⚟⚤dot;䅀ustĀ;a⚬⚭掰che»⚭ȀEaes⚻⚽⛉⛔;扨pĀ;p⛃⛄檉rox»⛄Ā;q⛎⛏檇Ā;q⛎⚻im;拦Ѐabnoptwz⛩⛴⛷✚✯❁❇❐Ānr⛮⛱g;柬r;懽rëࣁgƀlmr⛿✍✔eftĀar০✇ightá৲apsto;柼ightá৽parrowĀlr✥✩efô⓭ight;憬ƀafl✶✹✽r;榅;쀀𝕝us;樭imes;樴š❋❏st;戗áፎƀ;ef❗❘᠀旊nge»❘arĀ;l❤❥䀨t;榓ʀachmt❳❶❼➅➇ròࢨorneòᶌarĀ;d྘➃;業;怎ri;抿̀achiqt➘➝ੀ➢➮➻quo;怹r;쀀𝓁mƀ;egল➪➬;檍;檏Ābu┪➳oĀ;rฟ➹;怚rok;䅂萀<;cdhilqrࠫ⟒☹⟜⟠⟥⟪⟰Āci⟗⟙;檦r;橹reå◲mes;拉arr;楶uest;橻ĀPi⟵⟹ar;榖ƀ;ef⠀भ᠛旃rĀdu⠇⠍shar;楊har;楦Āen⠗⠡rtneqq;쀀≨︀Å⠞܀Dacdefhilnopsu⡀⡅⢂⢎⢓⢠⢥⢨⣚⣢⣤ઃ⣳⤂Dot;戺Ȁclpr⡎⡒⡣⡽r耻¯䂯Āet⡗⡙;時Ā;e⡞⡟朠se»⡟Ā;sျ⡨toȀ;dluျ⡳⡷⡻owîҌefôएðᏑker;斮Āoy⢇⢌mma;権;䐼ash;怔asuredangle»ᘦr;쀀𝔪o;愧ƀcdn⢯⢴⣉ro耻µ䂵Ȁ;acdᑤ⢽⣀⣄sôᚧir;櫰ot肻·Ƶusƀ;bd⣒ᤃ⣓戒Ā;uᴼ⣘;横ţ⣞⣡p;櫛ò−ðઁĀdp⣩⣮els;抧f;쀀𝕞Āct⣸⣽r;쀀𝓂pos»ᖝƀ;lm⤉⤊⤍䎼timap;抸ఀGLRVabcdefghijlmoprstuvw⥂⥓⥾⦉⦘⧚⧩⨕⨚⩘⩝⪃⪕⪤⪨⬄⬇⭄⭿⮮ⰴⱧⱼ⳩Āgt⥇⥋;쀀⋙̸Ā;v⥐௏쀀≫⃒ƀelt⥚⥲⥶ftĀar⥡⥧rrow;懍ightarrow;懎;쀀⋘̸Ā;v⥻ే쀀≪⃒ightarrow;懏ĀDd⦎⦓ash;抯ash;抮ʀbcnpt⦣⦧⦬⦱⧌la»˞ute;䅄g;쀀∠⃒ʀ;Eiop඄⦼⧀⧅⧈;쀀⩰̸d;쀀≋̸s;䅉roø඄urĀ;a⧓⧔普lĀ;s⧓ସǳ⧟\0⧣p肻 ଷmpĀ;e௹ఀʀaeouy⧴⧾⨃⨐⨓ǰ⧹\0⧻;橃on;䅈dil;䅆ngĀ;dൾ⨊ot;쀀⩭̸p;橂;䐽ash;怓΀;Aadqsxஒ⨩⨭⨻⩁⩅⩐rr;懗rĀhr⨳⨶k;椤Ā;oᏲᏰot;쀀≐̸uiöୣĀei⩊⩎ar;椨í஘istĀ;s஠டr;쀀𝔫ȀEest௅⩦⩹⩼ƀ;qs஼⩭௡ƀ;qs஼௅⩴lanô௢ií௪Ā;rஶ⪁»ஷƀAap⪊⪍⪑rò⥱rr;憮ar;櫲ƀ;svྍ⪜ྌĀ;d⪡⪢拼;拺cy;䑚΀AEadest⪷⪺⪾⫂⫅⫶⫹rò⥦;쀀≦̸rr;憚r;急Ȁ;fqs఻⫎⫣⫯tĀar⫔⫙rro÷⫁ightarro÷⪐ƀ;qs఻⪺⫪lanôౕĀ;sౕ⫴»శiíౝĀ;rవ⫾iĀ;eచథiäඐĀpt⬌⬑f;쀀𝕟膀¬;in⬙⬚⬶䂬nȀ;Edvஉ⬤⬨⬮;쀀⋹̸ot;쀀⋵̸ǡஉ⬳⬵;拷;拶iĀ;vಸ⬼ǡಸ⭁⭃;拾;拽ƀaor⭋⭣⭩rȀ;ast୻⭕⭚⭟lleì୻l;쀀⫽⃥;쀀∂̸lint;樔ƀ;ceಒ⭰⭳uåಥĀ;cಘ⭸Ā;eಒ⭽ñಘȀAait⮈⮋⮝⮧rò⦈rrƀ;cw⮔⮕⮙憛;쀀⤳̸;쀀↝̸ghtarrow»⮕riĀ;eೋೖ΀chimpqu⮽⯍⯙⬄୸⯤⯯Ȁ;cerല⯆ഷ⯉uå൅;쀀𝓃ortɭ⬅\0\0⯖ará⭖mĀ;e൮⯟Ā;q൴൳suĀbp⯫⯭å೸åഋƀbcp⯶ⰑⰙȀ;Ees⯿ⰀഢⰄ抄;쀀⫅̸etĀ;eഛⰋqĀ;qണⰀcĀ;eലⰗñസȀ;EesⰢⰣൟⰧ抅;쀀⫆̸etĀ;e൘ⰮqĀ;qൠⰣȀgilrⰽⰿⱅⱇìௗlde耻ñ䃱çృiangleĀlrⱒⱜeftĀ;eచⱚñదightĀ;eೋⱥñ೗Ā;mⱬⱭ䎽ƀ;esⱴⱵⱹ䀣ro;愖p;怇ҀDHadgilrsⲏⲔⲙⲞⲣⲰⲶⳓⳣash;抭arr;椄p;쀀≍⃒ash;抬ĀetⲨⲬ;쀀≥⃒;쀀>⃒nfin;槞ƀAetⲽⳁⳅrr;椂;쀀≤⃒Ā;rⳊⳍ쀀<⃒ie;쀀⊴⃒ĀAtⳘⳜrr;椃rie;쀀⊵⃒im;쀀∼⃒ƀAan⳰⳴ⴂrr;懖rĀhr⳺⳽k;椣Ā;oᏧᏥear;椧ቓ᪕\0\0\0\0\0\0\0\0\0\0\0\0\0ⴭ\0ⴸⵈⵠⵥ⵲ⶄᬇ\0\0ⶍⶫ\0ⷈⷎ\0ⷜ⸙⸫⸾⹃Ācsⴱ᪗ute耻ó䃳ĀiyⴼⵅrĀ;c᪞ⵂ耻ô䃴;䐾ʀabios᪠ⵒⵗǈⵚlac;䅑v;樸old;榼lig;䅓Ācr⵩⵭ir;榿;쀀𝔬ͯ⵹\0\0⵼\0ⶂn;䋛ave耻ò䃲;槁Ābmⶈ෴ar;榵Ȁacitⶕ⶘ⶥⶨrò᪀Āir⶝ⶠr;榾oss;榻nå๒;槀ƀaeiⶱⶵⶹcr;䅍ga;䏉ƀcdnⷀⷅǍron;䎿;榶pf;쀀𝕠ƀaelⷔ⷗ǒr;榷rp;榹΀;adiosvⷪⷫⷮ⸈⸍⸐⸖戨rò᪆Ȁ;efmⷷⷸ⸂⸅橝rĀ;oⷾⷿ愴f»ⷿ耻ª䂪耻º䂺gof;抶r;橖lope;橗;橛ƀclo⸟⸡⸧ò⸁ash耻ø䃸l;折iŬⸯ⸴de耻õ䃵esĀ;aǛ⸺s;樶ml耻ö䃶bar;挽ૡ⹞\0⹽\0⺀⺝\0⺢⺹\0\0⻋ຜ\0⼓\0\0⼫⾼\0⿈rȀ;astЃ⹧⹲຅脀¶;l⹭⹮䂶leìЃɩ⹸\0\0⹻m;櫳;櫽y;䐿rʀcimpt⺋⺏⺓ᡥ⺗nt;䀥od;䀮il;怰enk;怱r;쀀𝔭ƀimo⺨⺰⺴Ā;v⺭⺮䏆;䏕maô੶ne;明ƀ;tv⺿⻀⻈䏀chfork»´;䏖Āau⻏⻟nĀck⻕⻝kĀ;h⇴⻛;愎ö⇴sҀ;abcdemst⻳⻴ᤈ⻹⻽⼄⼆⼊⼎䀫cir;樣ir;樢Āouᵀ⼂;樥;橲n肻±ຝim;樦wo;樧ƀipu⼙⼠⼥ntint;樕f;쀀𝕡nd耻£䂣Ԁ;Eaceinosu່⼿⽁⽄⽇⾁⾉⾒⽾⾶;檳p;檷uå໙Ā;c໎⽌̀;acens່⽙⽟⽦⽨⽾pproø⽃urlyeñ໙ñ໎ƀaes⽯⽶⽺pprox;檹qq;檵im;拨iíໟmeĀ;s⾈ຮ怲ƀEas⽸⾐⽺ð⽵ƀdfp໬⾙⾯ƀals⾠⾥⾪lar;挮ine;挒urf;挓Ā;t໻⾴ï໻rel;抰Āci⿀⿅r;쀀𝓅;䏈ncsp;怈̀fiopsu⿚⋢⿟⿥⿫⿱r;쀀𝔮pf;쀀𝕢rime;恗cr;쀀𝓆ƀaeo⿸〉〓tĀei⿾々rnionóڰnt;樖stĀ;e【】䀿ñἙô༔઀ABHabcdefhilmnoprstux぀けさすムㄎㄫㅇㅢㅲㆎ㈆㈕㈤㈩㉘㉮㉲㊐㊰㊷ƀartぇおがròႳòϝail;検aròᱥar;楤΀cdenqrtとふへみわゔヌĀeuねぱ;쀀∽̱te;䅕iãᅮmptyv;榳gȀ;del࿑らるろ;榒;榥å࿑uo耻»䂻rր;abcfhlpstw࿜ガクシスゼゾダッデナp;極Ā;f࿠ゴs;椠;椳s;椞ë≝ð✮l;楅im;楴l;憣;憝Āaiパフil;椚oĀ;nホボ戶aló༞ƀabrョリヮrò៥rk;杳ĀakンヽcĀekヹ・;䁽;䁝Āes㄂㄄;榌lĀduㄊㄌ;榎;榐Ȁaeuyㄗㄜㄧㄩron;䅙Ādiㄡㄥil;䅗ì࿲âヺ;䑀Ȁclqsㄴㄷㄽㅄa;椷dhar;楩uoĀ;rȎȍh;憳ƀacgㅎㅟངlȀ;ipsླྀㅘㅛႜnåႻarôྩt;断ƀilrㅩဣㅮsht;楽;쀀𝔯ĀaoㅷㆆrĀduㅽㅿ»ѻĀ;l႑ㆄ;楬Ā;vㆋㆌ䏁;䏱ƀgns㆕ㇹㇼht̀ahlrstㆤㆰ㇂㇘㇤㇮rrowĀ;t࿜ㆭaéトarpoonĀduㆻㆿowîㅾp»႒eftĀah㇊㇐rrowó࿪arpoonóՑightarrows;應quigarro÷ニhreetimes;拌g;䋚ingdotseñἲƀahm㈍㈐㈓rò࿪aòՑ;怏oustĀ;a㈞㈟掱che»㈟mid;櫮Ȁabpt㈲㈽㉀㉒Ānr㈷㈺g;柭r;懾rëဃƀafl㉇㉊㉎r;榆;쀀𝕣us;樮imes;樵Āap㉝㉧rĀ;g㉣㉤䀩t;榔olint;樒arò㇣Ȁachq㉻㊀Ⴜ㊅quo;怺r;쀀𝓇Ābu・㊊oĀ;rȔȓƀhir㊗㊛㊠reåㇸmes;拊iȀ;efl㊪ၙᠡ㊫方tri;槎luhar;楨;愞ൡ㋕㋛㋟㌬㌸㍱\0㍺㎤\0\0㏬㏰\0㐨㑈㑚㒭㒱㓊㓱\0㘖\0\0㘳cute;䅛quï➺Ԁ;Eaceinpsyᇭ㋳㋵㋿㌂㌋㌏㌟㌦㌩;檴ǰ㋺\0㋼;檸on;䅡uåᇾĀ;dᇳ㌇il;䅟rc;䅝ƀEas㌖㌘㌛;檶p;檺im;择olint;樓iíሄ;䑁otƀ;be㌴ᵇ㌵担;橦΀Aacmstx㍆㍊㍗㍛㍞㍣㍭rr;懘rĀhr㍐㍒ë∨Ā;oਸ਼਴t耻§䂧i;䀻war;椩mĀin㍩ðnuóñt;朶rĀ;o㍶⁕쀀𝔰Ȁacoy㎂㎆㎑㎠rp;景Āhy㎋㎏cy;䑉;䑈rtɭ㎙\0\0㎜iäᑤaraì⹯耻­䂭Āgm㎨㎴maƀ;fv㎱㎲㎲䏃;䏂Ѐ;deglnprካ㏅㏉㏎㏖㏞㏡㏦ot;橪Ā;q኱ኰĀ;E㏓㏔檞;檠Ā;E㏛㏜檝;檟e;扆lus;樤arr;楲aròᄽȀaeit㏸㐈㐏㐗Āls㏽㐄lsetmé㍪hp;樳parsl;槤Ādlᑣ㐔e;挣Ā;e㐜㐝檪Ā;s㐢㐣檬;쀀⪬︀ƀflp㐮㐳㑂tcy;䑌Ā;b㐸㐹䀯Ā;a㐾㐿槄r;挿f;쀀𝕤aĀdr㑍ЂesĀ;u㑔㑕晠it»㑕ƀcsu㑠㑹㒟Āau㑥㑯pĀ;sᆈ㑫;쀀⊓︀pĀ;sᆴ㑵;쀀⊔︀uĀbp㑿㒏ƀ;esᆗᆜ㒆etĀ;eᆗ㒍ñᆝƀ;esᆨᆭ㒖etĀ;eᆨ㒝ñᆮƀ;afᅻ㒦ְrť㒫ֱ»ᅼaròᅈȀcemt㒹㒾㓂㓅r;쀀𝓈tmîñiì㐕aræᆾĀar㓎㓕rĀ;f㓔ឿ昆Āan㓚㓭ightĀep㓣㓪psiloîỠhé⺯s»⡒ʀbcmnp㓻㕞ሉ㖋㖎Ҁ;Edemnprs㔎㔏㔑㔕㔞㔣㔬㔱㔶抂;櫅ot;檽Ā;dᇚ㔚ot;櫃ult;櫁ĀEe㔨㔪;櫋;把lus;檿arr;楹ƀeiu㔽㕒㕕tƀ;en㔎㕅㕋qĀ;qᇚ㔏eqĀ;q㔫㔨m;櫇Ābp㕚㕜;櫕;櫓c̀;acensᇭ㕬㕲㕹㕻㌦pproø㋺urlyeñᇾñᇳƀaes㖂㖈㌛pproø㌚qñ㌗g;晪ڀ123;Edehlmnps㖩㖬㖯ሜ㖲㖴㗀㗉㗕㗚㗟㗨㗭耻¹䂹耻²䂲耻³䂳;櫆Āos㖹㖼t;檾ub;櫘Ā;dሢ㗅ot;櫄sĀou㗏㗒l;柉b;櫗arr;楻ult;櫂ĀEe㗤㗦;櫌;抋lus;櫀ƀeiu㗴㘉㘌tƀ;enሜ㗼㘂qĀ;qሢ㖲eqĀ;q㗧㗤m;櫈Ābp㘑㘓;櫔;櫖ƀAan㘜㘠㘭rr;懙rĀhr㘦㘨ë∮Ā;oਫ਩war;椪lig耻ß䃟௡㙑㙝㙠ዎ㙳㙹\0㙾㛂\0\0\0\0\0㛛㜃\0㜉㝬\0\0\0㞇ɲ㙖\0\0㙛get;挖;䏄rë๟ƀaey㙦㙫㙰ron;䅥dil;䅣;䑂lrec;挕r;쀀𝔱Ȁeiko㚆㚝㚵㚼ǲ㚋\0㚑eĀ4fኄኁaƀ;sv㚘㚙㚛䎸ym;䏑Ācn㚢㚲kĀas㚨㚮pproø዁im»ኬsðኞĀas㚺㚮ð዁rn耻þ䃾Ǭ̟㛆⋧es膀×;bd㛏㛐㛘䃗Ā;aᤏ㛕r;樱;樰ƀeps㛡㛣㜀á⩍Ȁ;bcf҆㛬㛰㛴ot;挶ir;櫱Ā;o㛹㛼쀀𝕥rk;櫚á㍢rime;怴ƀaip㜏㜒㝤dåቈ΀adempst㜡㝍㝀㝑㝗㝜㝟ngleʀ;dlqr㜰㜱㜶㝀㝂斵own»ᶻeftĀ;e⠀㜾ñम;扜ightĀ;e㊪㝋ñၚot;旬inus;樺lus;樹b;槍ime;樻ezium;揢ƀcht㝲㝽㞁Āry㝷㝻;쀀𝓉;䑆cy;䑛rok;䅧Āio㞋㞎xô᝷headĀlr㞗㞠eftarro÷ࡏightarrow»ཝऀAHabcdfghlmoprstuw㟐㟓㟗㟤㟰㟼㠎㠜㠣㠴㡑㡝㡫㢩㣌㣒㣪㣶ròϭar;楣Ācr㟜㟢ute耻ú䃺òᅐrǣ㟪\0㟭y;䑞ve;䅭Āiy㟵㟺rc耻û䃻;䑃ƀabh㠃㠆㠋ròᎭlac;䅱aòᏃĀir㠓㠘sht;楾;쀀𝔲rave耻ù䃹š㠧㠱rĀlr㠬㠮»ॗ»ႃlk;斀Āct㠹㡍ɯ㠿\0\0㡊rnĀ;e㡅㡆挜r»㡆op;挏ri;旸Āal㡖㡚cr;䅫肻¨͉Āgp㡢㡦on;䅳f;쀀𝕦̀adhlsuᅋ㡸㡽፲㢑㢠ownáᎳarpoonĀlr㢈㢌efô㠭ighô㠯iƀ;hl㢙㢚㢜䏅»ᏺon»㢚parrows;懈ƀcit㢰㣄㣈ɯ㢶\0\0㣁rnĀ;e㢼㢽挝r»㢽op;挎ng;䅯ri;旹cr;쀀𝓊ƀdir㣙㣝㣢ot;拰lde;䅩iĀ;f㜰㣨»᠓Āam㣯㣲rò㢨l耻ü䃼angle;榧ހABDacdeflnoprsz㤜㤟㤩㤭㦵㦸㦽㧟㧤㧨㧳㧹㧽㨁㨠ròϷarĀ;v㤦㤧櫨;櫩asèϡĀnr㤲㤷grt;榜΀eknprst㓣㥆㥋㥒㥝㥤㦖appá␕othinçẖƀhir㓫⻈㥙opô⾵Ā;hᎷ㥢ïㆍĀiu㥩㥭gmá㎳Ābp㥲㦄setneqĀ;q㥽㦀쀀⊊︀;쀀⫋︀setneqĀ;q㦏㦒쀀⊋︀;쀀⫌︀Āhr㦛㦟etá㚜iangleĀlr㦪㦯eft»थight»ၑy;䐲ash»ံƀelr㧄㧒㧗ƀ;beⷪ㧋㧏ar;抻q;扚lip;拮Ābt㧜ᑨaòᑩr;쀀𝔳tré㦮suĀbp㧯㧱»ജ»൙pf;쀀𝕧roð໻tré㦴Ācu㨆㨋r;쀀𝓋Ābp㨐㨘nĀEe㦀㨖»㥾nĀEe㦒㨞»㦐igzag;榚΀cefoprs㨶㨻㩖㩛㩔㩡㩪irc;䅵Ādi㩀㩑Ābg㩅㩉ar;機eĀ;qᗺ㩏;扙erp;愘r;쀀𝔴pf;쀀𝕨Ā;eᑹ㩦atèᑹcr;쀀𝓌ૣណ㪇\0㪋\0㪐㪛\0\0㪝㪨㪫㪯\0\0㫃㫎\0㫘ៜ៟tré៑r;쀀𝔵ĀAa㪔㪗ròσrò৶;䎾ĀAa㪡㪤ròθrò৫að✓is;拻ƀdptឤ㪵㪾Āfl㪺ឩ;쀀𝕩imåឲĀAa㫇㫊ròώròਁĀcq㫒ីr;쀀𝓍Āpt៖㫜ré។Ѐacefiosu㫰㫽㬈㬌㬑㬕㬛㬡cĀuy㫶㫻te耻ý䃽;䑏Āiy㬂㬆rc;䅷;䑋n耻¥䂥r;쀀𝔶cy;䑗pf;쀀𝕪cr;쀀𝓎Ācm㬦㬩y;䑎l耻ÿ䃿Ԁacdefhiosw㭂㭈㭔㭘㭤㭩㭭㭴㭺㮀cute;䅺Āay㭍㭒ron;䅾;䐷ot;䅼Āet㭝㭡træᕟa;䎶r;쀀𝔷cy;䐶grarr;懝pf;쀀𝕫cr;쀀𝓏Ājn㮅㮇;怍j;怌'.split("").map(u=>u.charCodeAt(0))),we=new Uint16Array("Ȁaglq	\x1Bɭ\0\0p;䀦os;䀧t;䀾t;䀼uot;䀢".split("").map(u=>u.charCodeAt(0)));var Bu;const Se=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]),Be=(Bu=String.fromCodePoint)!==null&&Bu!==void 0?Bu:function(u){let e="";return u>65535&&(u-=65536,e+=String.fromCharCode(u>>>10&1023|55296),u=56320|u&1023),e+=String.fromCharCode(u),e};function Te(u){var e;return u>=55296&&u<=57343||u>1114111?65533:(e=Se.get(u))!==null&&e!==void 0?e:u}var I;(function(u){u[u.NUM=35]="NUM",u[u.SEMI=59]="SEMI",u[u.EQUALS=61]="EQUALS",u[u.ZERO=48]="ZERO",u[u.NINE=57]="NINE",u[u.LOWER_A=97]="LOWER_A",u[u.LOWER_F=102]="LOWER_F",u[u.LOWER_X=120]="LOWER_X",u[u.LOWER_Z=122]="LOWER_Z",u[u.UPPER_A=65]="UPPER_A",u[u.UPPER_F=70]="UPPER_F",u[u.UPPER_Z=90]="UPPER_Z"})(I||(I={}));const ze=32;var K;(function(u){u[u.VALUE_LENGTH=49152]="VALUE_LENGTH",u[u.BRANCH_LENGTH=16256]="BRANCH_LENGTH",u[u.JUMP_TABLE=127]="JUMP_TABLE"})(K||(K={}));function Nu(u){return u>=I.ZERO&&u<=I.NINE}function Ie(u){return u>=I.UPPER_A&&u<=I.UPPER_F||u>=I.LOWER_A&&u<=I.LOWER_F}function Me(u){return u>=I.UPPER_A&&u<=I.UPPER_Z||u>=I.LOWER_A&&u<=I.LOWER_Z||Nu(u)}function qe(u){return u===I.EQUALS||Me(u)}var z;(function(u){u[u.EntityStart=0]="EntityStart",u[u.NumericStart=1]="NumericStart",u[u.NumericDecimal=2]="NumericDecimal",u[u.NumericHex=3]="NumericHex",u[u.NamedEntity=4]="NamedEntity"})(z||(z={}));var Y;(function(u){u[u.Legacy=0]="Legacy",u[u.Strict=1]="Strict",u[u.Attribute=2]="Attribute"})(Y||(Y={}));class Re{constructor(e,t,n){this.decodeTree=e,this.emitCodePoint=t,this.errors=n,this.state=z.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=Y.Strict}startEntity(e){this.decodeMode=e,this.state=z.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1}write(e,t){switch(this.state){case z.EntityStart:return e.charCodeAt(t)===I.NUM?(this.state=z.NumericStart,this.consumed+=1,this.stateNumericStart(e,t+1)):(this.state=z.NamedEntity,this.stateNamedEntity(e,t));case z.NumericStart:return this.stateNumericStart(e,t);case z.NumericDecimal:return this.stateNumericDecimal(e,t);case z.NumericHex:return this.stateNumericHex(e,t);case z.NamedEntity:return this.stateNamedEntity(e,t)}}stateNumericStart(e,t){return t>=e.length?-1:(e.charCodeAt(t)|ze)===I.LOWER_X?(this.state=z.NumericHex,this.consumed+=1,this.stateNumericHex(e,t+1)):(this.state=z.NumericDecimal,this.stateNumericDecimal(e,t))}addToNumericResult(e,t,n,r){if(t!==n){const c=n-t;this.result=this.result*Math.pow(r,c)+parseInt(e.substr(t,c),r),this.consumed+=c}}stateNumericHex(e,t){const n=t;for(;t<e.length;){const r=e.charCodeAt(t);if(Nu(r)||Ie(r))t+=1;else return this.addToNumericResult(e,n,t,16),this.emitNumericEntity(r,3)}return this.addToNumericResult(e,n,t,16),-1}stateNumericDecimal(e,t){const n=t;for(;t<e.length;){const r=e.charCodeAt(t);if(Nu(r))t+=1;else return this.addToNumericResult(e,n,t,10),this.emitNumericEntity(r,2)}return this.addToNumericResult(e,n,t,10),-1}emitNumericEntity(e,t){var n;if(this.consumed<=t)return(n=this.errors)===null||n===void 0||n.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(e===I.SEMI)this.consumed+=1;else if(this.decodeMode===Y.Strict)return 0;return this.emitCodePoint(Te(this.result),this.consumed),this.errors&&(e!==I.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed}stateNamedEntity(e,t){const{decodeTree:n}=this;let r=n[this.treeIndex],c=(r&K.VALUE_LENGTH)>>14;for(;t<e.length;t++,this.excess++){const o=e.charCodeAt(t);if(this.treeIndex=Ne(n,r,this.treeIndex+Math.max(1,c),o),this.treeIndex<0)return this.result===0||this.decodeMode===Y.Attribute&&(c===0||qe(o))?0:this.emitNotTerminatedNamedEntity();if(r=n[this.treeIndex],c=(r&K.VALUE_LENGTH)>>14,c!==0){if(o===I.SEMI)return this.emitNamedEntityData(this.treeIndex,c,this.consumed+this.excess);this.decodeMode!==Y.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return-1}emitNotTerminatedNamedEntity(){var e;const{result:t,decodeTree:n}=this,r=(n[t]&K.VALUE_LENGTH)>>14;return this.emitNamedEntityData(t,r,this.consumed),(e=this.errors)===null||e===void 0||e.missingSemicolonAfterCharacterReference(),this.consumed}emitNamedEntityData(e,t,n){const{decodeTree:r}=this;return this.emitCodePoint(t===1?r[e]&~K.VALUE_LENGTH:r[e+1],n),t===3&&this.emitCodePoint(r[e+2],n),n}end(){var e;switch(this.state){case z.NamedEntity:return this.result!==0&&(this.decodeMode!==Y.Attribute||this.result===this.treeIndex)?this.emitNotTerminatedNamedEntity():0;case z.NumericDecimal:return this.emitNumericEntity(0,2);case z.NumericHex:return this.emitNumericEntity(0,3);case z.NumericStart:return(e=this.errors)===null||e===void 0||e.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case z.EntityStart:return 0}}}function C0(u){let e="";const t=new Re(u,n=>e+=Be(n));return function(r,c){let o=0,i=0;for(;(i=r.indexOf("&",i))>=0;){e+=r.slice(o,i),t.startEntity(c);const s=t.write(r,i+1);if(s<0){o=i+t.end();break}o=i+s,i=s===0?o+1:o}const a=e+r.slice(o);return e="",a}}function Ne(u,e,t,n){const r=(e&K.BRANCH_LENGTH)>>7,c=e&K.JUMP_TABLE;if(r===0)return c!==0&&n===c?t:-1;if(c){const a=n-c;return a<0||a>=r?-1:u[t+a]-1}let o=t,i=o+r-1;for(;o<=i;){const a=o+i>>>1,s=u[a];if(s<n)o=a+1;else if(s>n)i=a-1;else return u[a+r]}return-1}const Le=C0(ve);C0(we);function E0(u,e=Y.Legacy){return Le(u,e)}function Pe(u){return Object.prototype.toString.call(u)}function Uu(u){return Pe(u)==="[object String]"}const Oe=Object.prototype.hasOwnProperty;function je(u,e){return Oe.call(u,e)}function Au(u){return Array.prototype.slice.call(arguments,1).forEach(function(t){if(t){if(typeof t!="object")throw new TypeError(t+"must be object");Object.keys(t).forEach(function(n){u[n]=t[n]})}}),u}function A0(u,e,t){return[].concat(u.slice(0,e),t,u.slice(e+1))}function Zu(u){return!(u>=55296&&u<=57343||u>=64976&&u<=65007||(u&65535)===65535||(u&65535)===65534||u>=0&&u<=8||u===11||u>=14&&u<=31||u>=127&&u<=159||u>1114111)}function Cu(u){if(u>65535){u-=65536;const e=55296+(u>>10),t=56320+(u&1023);return String.fromCharCode(e,t)}return String.fromCharCode(u)}const y0=/\\([!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~])/g,$e=/&([a-z#][a-z0-9]{1,31});/gi,Ue=new RegExp(y0.source+"|"+$e.source,"gi"),Ze=/^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))$/i;function He(u,e){if(e.charCodeAt(0)===35&&Ze.test(e)){const n=e[1].toLowerCase()==="x"?parseInt(e.slice(2),16):parseInt(e.slice(1),10);return Zu(n)?Cu(n):u}const t=E0(u);return t!==u?t:u}function Ve(u){return u.indexOf("\\")<0?u:u.replace(y0,"$1")}function ou(u){return u.indexOf("\\")<0&&u.indexOf("&")<0?u:u.replace(Ue,function(e,t,n){return t||He(e,n)})}const Ge=/[&<>"]/,We=/[&<>"]/g,Je={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};function Qe(u){return Je[u]}function uu(u){return Ge.test(u)?u.replace(We,Qe):u}const Xe=/[.?*+^$[\]\\(){}|-]/g;function Ye(u){return u.replace(Xe,"\\$&")}function B(u){switch(u){case 9:case 32:return!0}return!1}function lu(u){if(u>=8192&&u<=8202)return!0;switch(u){case 9:case 10:case 11:case 12:case 13:case 32:case 160:case 5760:case 8239:case 8287:case 12288:return!0}return!1}function fu(u){return $u.test(u)||g0.test(u)}function du(u){switch(u){case 33:case 34:case 35:case 36:case 37:case 38:case 39:case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:case 58:case 59:case 60:case 61:case 62:case 63:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 124:case 125:case 126:return!0;default:return!1}}function yu(u){return u=u.trim().replace(/\s+/g," "),"ẞ".toLowerCase()==="Ṿ"&&(u=u.replace(/ẞ/g,"ß")),u.toLowerCase().toUpperCase()}const Ke={mdurl:Ae,ucmicro:Fe},ut=Object.freeze(Object.defineProperty({__proto__:null,arrayReplaceAt:A0,assign:Au,escapeHtml:uu,escapeRE:Ye,fromCodePoint:Cu,has:je,isMdAsciiPunct:du,isPunctChar:fu,isSpace:B,isString:Uu,isValidEntityCode:Zu,isWhiteSpace:lu,lib:Ke,normalizeReference:yu,unescapeAll:ou,unescapeMd:Ve},Symbol.toStringTag,{value:"Module"}));function et(u,e,t){let n,r,c,o;const i=u.posMax,a=u.pos;for(u.pos=e+1,n=1;u.pos<i;){if(c=u.src.charCodeAt(u.pos),c===93&&(n--,n===0)){r=!0;break}if(o=u.pos,u.md.inline.skipToken(u),c===91){if(o===u.pos-1)n++;else if(t)return u.pos=a,-1}}let s=-1;return r&&(s=u.pos),u.pos=a,s}function tt(u,e,t){let n,r=e;const c={ok:!1,pos:0,str:""};if(u.charCodeAt(r)===60){for(r++;r<t;){if(n=u.charCodeAt(r),n===10||n===60)return c;if(n===62)return c.pos=r+1,c.str=ou(u.slice(e+1,r)),c.ok=!0,c;if(n===92&&r+1<t){r+=2;continue}r++}return c}let o=0;for(;r<t&&(n=u.charCodeAt(r),!(n===32||n<32||n===127));){if(n===92&&r+1<t){if(u.charCodeAt(r+1)===32)break;r+=2;continue}if(n===40&&(o++,o>32))return c;if(n===41){if(o===0)break;o--}r++}return e===r||o!==0||(c.str=ou(u.slice(e,r)),c.pos=r,c.ok=!0),c}function rt(u,e,t,n){let r,c=e;const o={ok:!1,can_continue:!1,pos:0,str:"",marker:0};if(n)o.str=n.str,o.marker=n.marker;else{if(c>=t)return o;let i=u.charCodeAt(c);if(i!==34&&i!==39&&i!==40)return o;e++,c++,i===40&&(i=41),o.marker=i}for(;c<t;){if(r=u.charCodeAt(c),r===o.marker)return o.pos=c+1,o.str+=ou(u.slice(e,c)),o.ok=!0,o;if(r===40&&o.marker===41)return o;r===92&&c+1<t&&c++,c++}return o.can_continue=!0,o.str+=ou(u.slice(e,c)),o}const nt=Object.freeze(Object.defineProperty({__proto__:null,parseLinkDestination:tt,parseLinkLabel:et,parseLinkTitle:rt},Symbol.toStringTag,{value:"Module"})),V={};V.code_inline=function(u,e,t,n,r){const c=u[e];return"<code"+r.renderAttrs(c)+">"+uu(c.content)+"</code>"};V.code_block=function(u,e,t,n,r){const c=u[e];return"<pre"+r.renderAttrs(c)+"><code>"+uu(u[e].content)+`</code></pre>
`};V.fence=function(u,e,t,n,r){const c=u[e],o=c.info?ou(c.info).trim():"";let i="",a="";if(o){const l=o.split(/(\s+)/g);i=l[0],a=l.slice(2).join("")}let s;if(t.highlight?s=t.highlight(c.content,i,a)||uu(c.content):s=uu(c.content),s.indexOf("<pre")===0)return s+`
`;if(o){const l=c.attrIndex("class"),d=c.attrs?c.attrs.slice():[];l<0?d.push(["class",t.langPrefix+i]):(d[l]=d[l].slice(),d[l][1]+=" "+t.langPrefix+i);const b={attrs:d};return`<pre><code${r.renderAttrs(b)}>${s}</code></pre>
`}return`<pre><code${r.renderAttrs(c)}>${s}</code></pre>
`};V.image=function(u,e,t,n,r){const c=u[e];return c.attrs[c.attrIndex("alt")][1]=r.renderInlineAsText(c.children,t,n),r.renderToken(u,e,t)};V.hardbreak=function(u,e,t){return t.xhtmlOut?`<br />
`:`<br>
`};V.softbreak=function(u,e,t){return t.breaks?t.xhtmlOut?`<br />
`:`<br>
`:`
`};V.text=function(u,e){return uu(u[e].content)};V.html_block=function(u,e){return u[e].content};V.html_inline=function(u,e){return u[e].content};function au(){this.rules=Au({},V)}au.prototype.renderAttrs=function(e){let t,n,r;if(!e.attrs)return"";for(r="",t=0,n=e.attrs.length;t<n;t++)r+=" "+uu(e.attrs[t][0])+'="'+uu(e.attrs[t][1])+'"';return r};au.prototype.renderToken=function(e,t,n){const r=e[t];let c="";if(r.hidden)return"";r.block&&r.nesting!==-1&&t&&e[t-1].hidden&&(c+=`
`),c+=(r.nesting===-1?"</":"<")+r.tag,c+=this.renderAttrs(r),r.nesting===0&&n.xhtmlOut&&(c+=" /");let o=!1;if(r.block&&(o=!0,r.nesting===1&&t+1<e.length)){const i=e[t+1];(i.type==="inline"||i.hidden||i.nesting===-1&&i.tag===r.tag)&&(o=!1)}return c+=o?`>
`:">",c};au.prototype.renderInline=function(u,e,t){let n="";const r=this.rules;for(let c=0,o=u.length;c<o;c++){const i=u[c].type;typeof r[i]<"u"?n+=r[i](u,c,e,t,this):n+=this.renderToken(u,c,e)}return n};au.prototype.renderInlineAsText=function(u,e,t){let n="";for(let r=0,c=u.length;r<c;r++)switch(u[r].type){case"text":n+=u[r].content;break;case"image":n+=this.renderInlineAsText(u[r].children,e,t);break;case"html_inline":case"html_block":n+=u[r].content;break;case"softbreak":case"hardbreak":n+=`
`;break}return n};au.prototype.render=function(u,e,t){let n="";const r=this.rules;for(let c=0,o=u.length;c<o;c++){const i=u[c].type;i==="inline"?n+=this.renderInline(u[c].children,e,t):typeof r[i]<"u"?n+=r[i](u,c,e,t,this):n+=this.renderToken(u,c,e,t)}return n};function q(){this.__rules__=[],this.__cache__=null}q.prototype.__find__=function(u){for(let e=0;e<this.__rules__.length;e++)if(this.__rules__[e].name===u)return e;return-1};q.prototype.__compile__=function(){const u=this,e=[""];u.__rules__.forEach(function(t){t.enabled&&t.alt.forEach(function(n){e.indexOf(n)<0&&e.push(n)})}),u.__cache__={},e.forEach(function(t){u.__cache__[t]=[],u.__rules__.forEach(function(n){n.enabled&&(t&&n.alt.indexOf(t)<0||u.__cache__[t].push(n.fn))})})};q.prototype.at=function(u,e,t){const n=this.__find__(u),r=t||{};if(n===-1)throw new Error("Parser rule not found: "+u);this.__rules__[n].fn=e,this.__rules__[n].alt=r.alt||[],this.__cache__=null};q.prototype.before=function(u,e,t,n){const r=this.__find__(u),c=n||{};if(r===-1)throw new Error("Parser rule not found: "+u);this.__rules__.splice(r,0,{name:e,enabled:!0,fn:t,alt:c.alt||[]}),this.__cache__=null};q.prototype.after=function(u,e,t,n){const r=this.__find__(u),c=n||{};if(r===-1)throw new Error("Parser rule not found: "+u);this.__rules__.splice(r+1,0,{name:e,enabled:!0,fn:t,alt:c.alt||[]}),this.__cache__=null};q.prototype.push=function(u,e,t){const n=t||{};this.__rules__.push({name:u,enabled:!0,fn:e,alt:n.alt||[]}),this.__cache__=null};q.prototype.enable=function(u,e){Array.isArray(u)||(u=[u]);const t=[];return u.forEach(function(n){const r=this.__find__(n);if(r<0){if(e)return;throw new Error("Rules manager: invalid rule name "+n)}this.__rules__[r].enabled=!0,t.push(n)},this),this.__cache__=null,t};q.prototype.enableOnly=function(u,e){Array.isArray(u)||(u=[u]),this.__rules__.forEach(function(t){t.enabled=!1}),this.enable(u,e)};q.prototype.disable=function(u,e){Array.isArray(u)||(u=[u]);const t=[];return u.forEach(function(n){const r=this.__find__(n);if(r<0){if(e)return;throw new Error("Rules manager: invalid rule name "+n)}this.__rules__[r].enabled=!1,t.push(n)},this),this.__cache__=null,t};q.prototype.getRules=function(u){return this.__cache__===null&&this.__compile__(),this.__cache__[u]||[]};function U(u,e,t){this.type=u,this.tag=e,this.attrs=null,this.map=null,this.nesting=t,this.level=0,this.children=null,this.content="",this.markup="",this.info="",this.meta=null,this.block=!1,this.hidden=!1}U.prototype.attrIndex=function(e){if(!this.attrs)return-1;const t=this.attrs;for(let n=0,r=t.length;n<r;n++)if(t[n][0]===e)return n;return-1};U.prototype.attrPush=function(e){this.attrs?this.attrs.push(e):this.attrs=[e]};U.prototype.attrSet=function(e,t){const n=this.attrIndex(e),r=[e,t];n<0?this.attrPush(r):this.attrs[n]=r};U.prototype.attrGet=function(e){const t=this.attrIndex(e);let n=null;return t>=0&&(n=this.attrs[t][1]),n};U.prototype.attrJoin=function(e,t){const n=this.attrIndex(e);n<0?this.attrPush([e,t]):this.attrs[n][1]=this.attrs[n][1]+" "+t};function F0(u,e,t){this.src=u,this.env=t,this.tokens=[],this.inlineMode=!1,this.md=e}F0.prototype.Token=U;const ct=/\r\n?|\n/g,it=/\0/g;function ot(u){let e;e=u.src.replace(ct,`
`),e=e.replace(it,"�"),u.src=e}function at(u){let e;u.inlineMode?(e=new u.Token("inline","",0),e.content=u.src,e.map=[0,1],e.children=[],u.tokens.push(e)):u.md.block.parse(u.src,u.md,u.env,u.tokens)}function st(u){const e=u.tokens;for(let t=0,n=e.length;t<n;t++){const r=e[t];r.type==="inline"&&u.md.inline.parse(r.content,u.md,u.env,r.children)}}function lt(u){return/^<a[>\s]/i.test(u)}function ft(u){return/^<\/a\s*>/i.test(u)}function dt(u){const e=u.tokens;if(u.md.options.linkify)for(let t=0,n=e.length;t<n;t++){if(e[t].type!=="inline"||!u.md.linkify.pretest(e[t].content))continue;let r=e[t].children,c=0;for(let o=r.length-1;o>=0;o--){const i=r[o];if(i.type==="link_close"){for(o--;r[o].level!==i.level&&r[o].type!=="link_open";)o--;continue}if(i.type==="html_inline"&&(lt(i.content)&&c>0&&c--,ft(i.content)&&c++),!(c>0)&&i.type==="text"&&u.md.linkify.test(i.content)){const a=i.content;let s=u.md.linkify.match(a);const l=[];let d=i.level,b=0;s.length>0&&s[0].index===0&&o>0&&r[o-1].type==="text_special"&&(s=s.slice(1));for(let h=0;h<s.length;h++){const f=s[h].url,k=u.md.normalizeLink(f);if(!u.md.validateLink(k))continue;let C=s[h].text;s[h].schema?s[h].schema==="mailto:"&&!/^mailto:/i.test(C)?C=u.md.normalizeLinkText("mailto:"+C).replace(/^mailto:/,""):C=u.md.normalizeLinkText(C):C=u.md.normalizeLinkText("http://"+C).replace(/^http:\/\//,"");const v=s[h].index;if(v>b){const g=new u.Token("text","",0);g.content=a.slice(b,v),g.level=d,l.push(g)}const x=new u.Token("link_open","a",1);x.attrs=[["href",k]],x.level=d++,x.markup="linkify",x.info="auto",l.push(x);const _=new u.Token("text","",0);_.content=C,_.level=d,l.push(_);const m=new u.Token("link_close","a",-1);m.level=--d,m.markup="linkify",m.info="auto",l.push(m),b=s[h].lastIndex}if(b<a.length){const h=new u.Token("text","",0);h.content=a.slice(b),h.level=d,l.push(h)}e[t].children=r=A0(r,o,l)}}}}const v0=/\+-|\.\.|\?\?\?\?|!!!!|,,|--/,ht=/\((c|tm|r)\)/i,bt=/\((c|tm|r)\)/ig,pt={c:"©",r:"®",tm:"™"};function xt(u,e){return pt[e.toLowerCase()]}function _t(u){let e=0;for(let t=u.length-1;t>=0;t--){const n=u[t];n.type==="text"&&!e&&(n.content=n.content.replace(bt,xt)),n.type==="link_open"&&n.info==="auto"&&e--,n.type==="link_close"&&n.info==="auto"&&e++}}function mt(u){let e=0;for(let t=u.length-1;t>=0;t--){const n=u[t];n.type==="text"&&!e&&v0.test(n.content)&&(n.content=n.content.replace(/\+-/g,"±").replace(/\.{2,}/g,"…").replace(/([?!])…/g,"$1..").replace(/([?!]){4,}/g,"$1$1$1").replace(/,{2,}/g,",").replace(/(^|[^-])---(?=[^-]|$)/mg,"$1—").replace(/(^|\s)--(?=\s|$)/mg,"$1–").replace(/(^|[^-\s])--(?=[^-\s]|$)/mg,"$1–")),n.type==="link_open"&&n.info==="auto"&&e--,n.type==="link_close"&&n.info==="auto"&&e++}}function kt(u){let e;if(u.md.options.typographer)for(e=u.tokens.length-1;e>=0;e--)u.tokens[e].type==="inline"&&(ht.test(u.tokens[e].content)&&_t(u.tokens[e].children),v0.test(u.tokens[e].content)&&mt(u.tokens[e].children))}const gt=/['"]/,o0=/['"]/g,a0="’";function ku(u,e,t){return u.slice(0,e)+t+u.slice(e+1)}function Dt(u,e){let t;const n=[];for(let r=0;r<u.length;r++){const c=u[r],o=u[r].level;for(t=n.length-1;t>=0&&!(n[t].level<=o);t--);if(n.length=t+1,c.type!=="text")continue;let i=c.content,a=0,s=i.length;u:for(;a<s;){o0.lastIndex=a;const l=o0.exec(i);if(!l)break;let d=!0,b=!0;a=l.index+1;const h=l[0]==="'";let f=32;if(l.index-1>=0)f=i.charCodeAt(l.index-1);else for(t=r-1;t>=0&&!(u[t].type==="softbreak"||u[t].type==="hardbreak");t--)if(u[t].content){f=u[t].content.charCodeAt(u[t].content.length-1);break}let k=32;if(a<s)k=i.charCodeAt(a);else for(t=r+1;t<u.length&&!(u[t].type==="softbreak"||u[t].type==="hardbreak");t++)if(u[t].content){k=u[t].content.charCodeAt(0);break}const C=du(f)||fu(String.fromCharCode(f)),v=du(k)||fu(String.fromCharCode(k)),x=lu(f),_=lu(k);if(_?d=!1:v&&(x||C||(d=!1)),x?b=!1:C&&(_||v||(b=!1)),k===34&&l[0]==='"'&&f>=48&&f<=57&&(b=d=!1),d&&b&&(d=C,b=v),!d&&!b){h&&(c.content=ku(c.content,l.index,a0));continue}if(b)for(t=n.length-1;t>=0;t--){let m=n[t];if(n[t].level<o)break;if(m.single===h&&n[t].level===o){m=n[t];let g,A;h?(g=e.md.options.quotes[2],A=e.md.options.quotes[3]):(g=e.md.options.quotes[0],A=e.md.options.quotes[1]),c.content=ku(c.content,l.index,A),u[m.token].content=ku(u[m.token].content,m.pos,g),a+=A.length-1,m.token===r&&(a+=g.length-1),i=c.content,s=i.length,n.length=t;continue u}}d?n.push({token:r,pos:l.index,single:h,level:o}):b&&h&&(c.content=ku(c.content,l.index,a0))}}}function Ct(u){if(u.md.options.typographer)for(let e=u.tokens.length-1;e>=0;e--)u.tokens[e].type!=="inline"||!gt.test(u.tokens[e].content)||Dt(u.tokens[e].children,u)}function Et(u){let e,t;const n=u.tokens,r=n.length;for(let c=0;c<r;c++){if(n[c].type!=="inline")continue;const o=n[c].children,i=o.length;for(e=0;e<i;e++)o[e].type==="text_special"&&(o[e].type="text");for(e=t=0;e<i;e++)o[e].type==="text"&&e+1<i&&o[e+1].type==="text"?o[e+1].content=o[e].content+o[e+1].content:(e!==t&&(o[t]=o[e]),t++);e!==t&&(o.length=t)}}const Tu=[["normalize",ot],["block",at],["inline",st],["linkify",dt],["replacements",kt],["smartquotes",Ct],["text_join",Et]];function Hu(){this.ruler=new q;for(let u=0;u<Tu.length;u++)this.ruler.push(Tu[u][0],Tu[u][1])}Hu.prototype.process=function(u){const e=this.ruler.getRules("");for(let t=0,n=e.length;t<n;t++)e[t](u)};Hu.prototype.State=F0;function G(u,e,t,n){this.src=u,this.md=e,this.env=t,this.tokens=n,this.bMarks=[],this.eMarks=[],this.tShift=[],this.sCount=[],this.bsCount=[],this.blkIndent=0,this.line=0,this.lineMax=0,this.tight=!1,this.ddIndent=-1,this.listIndent=-1,this.parentType="root",this.level=0;const r=this.src;for(let c=0,o=0,i=0,a=0,s=r.length,l=!1;o<s;o++){const d=r.charCodeAt(o);if(!l)if(B(d)){i++,d===9?a+=4-a%4:a++;continue}else l=!0;(d===10||o===s-1)&&(d!==10&&o++,this.bMarks.push(c),this.eMarks.push(o),this.tShift.push(i),this.sCount.push(a),this.bsCount.push(0),l=!1,i=0,a=0,c=o+1)}this.bMarks.push(r.length),this.eMarks.push(r.length),this.tShift.push(0),this.sCount.push(0),this.bsCount.push(0),this.lineMax=this.bMarks.length-1}G.prototype.push=function(u,e,t){const n=new U(u,e,t);return n.block=!0,t<0&&this.level--,n.level=this.level,t>0&&this.level++,this.tokens.push(n),n};G.prototype.isEmpty=function(e){return this.bMarks[e]+this.tShift[e]>=this.eMarks[e]};G.prototype.skipEmptyLines=function(e){for(let t=this.lineMax;e<t&&!(this.bMarks[e]+this.tShift[e]<this.eMarks[e]);e++);return e};G.prototype.skipSpaces=function(e){for(let t=this.src.length;e<t;e++){const n=this.src.charCodeAt(e);if(!B(n))break}return e};G.prototype.skipSpacesBack=function(e,t){if(e<=t)return e;for(;e>t;)if(!B(this.src.charCodeAt(--e)))return e+1;return e};G.prototype.skipChars=function(e,t){for(let n=this.src.length;e<n&&this.src.charCodeAt(e)===t;e++);return e};G.prototype.skipCharsBack=function(e,t,n){if(e<=n)return e;for(;e>n;)if(t!==this.src.charCodeAt(--e))return e+1;return e};G.prototype.getLines=function(e,t,n,r){if(e>=t)return"";const c=new Array(t-e);for(let o=0,i=e;i<t;i++,o++){let a=0;const s=this.bMarks[i];let l=s,d;for(i+1<t||r?d=this.eMarks[i]+1:d=this.eMarks[i];l<d&&a<n;){const b=this.src.charCodeAt(l);if(B(b))b===9?a+=4-(a+this.bsCount[i])%4:a++;else if(l-s<this.tShift[i])a++;else break;l++}a>n?c[o]=new Array(a-n+1).join(" ")+this.src.slice(l,d):c[o]=this.src.slice(l,d)}return c.join("")};G.prototype.Token=U;const At=65536;function zu(u,e){const t=u.bMarks[e]+u.tShift[e],n=u.eMarks[e];return u.src.slice(t,n)}function s0(u){const e=[],t=u.length;let n=0,r=u.charCodeAt(n),c=!1,o=0,i="";for(;n<t;)r===124&&(c?(i+=u.substring(o,n-1),o=n):(e.push(i+u.substring(o,n)),i="",o=n+1)),c=r===92,n++,r=u.charCodeAt(n);return e.push(i+u.substring(o)),e}function yt(u,e,t,n){if(e+2>t)return!1;let r=e+1;if(u.sCount[r]<u.blkIndent||u.sCount[r]-u.blkIndent>=4)return!1;let c=u.bMarks[r]+u.tShift[r];if(c>=u.eMarks[r])return!1;const o=u.src.charCodeAt(c++);if(o!==124&&o!==45&&o!==58||c>=u.eMarks[r])return!1;const i=u.src.charCodeAt(c++);if(i!==124&&i!==45&&i!==58&&!B(i)||o===45&&B(i))return!1;for(;c<u.eMarks[r];){const m=u.src.charCodeAt(c);if(m!==124&&m!==45&&m!==58&&!B(m))return!1;c++}let a=zu(u,e+1),s=a.split("|");const l=[];for(let m=0;m<s.length;m++){const g=s[m].trim();if(!g){if(m===0||m===s.length-1)continue;return!1}if(!/^:?-+:?$/.test(g))return!1;g.charCodeAt(g.length-1)===58?l.push(g.charCodeAt(0)===58?"center":"right"):g.charCodeAt(0)===58?l.push("left"):l.push("")}if(a=zu(u,e).trim(),a.indexOf("|")===-1||u.sCount[e]-u.blkIndent>=4)return!1;s=s0(a),s.length&&s[0]===""&&s.shift(),s.length&&s[s.length-1]===""&&s.pop();const d=s.length;if(d===0||d!==l.length)return!1;if(n)return!0;const b=u.parentType;u.parentType="table";const h=u.md.block.ruler.getRules("blockquote"),f=u.push("table_open","table",1),k=[e,0];f.map=k;const C=u.push("thead_open","thead",1);C.map=[e,e+1];const v=u.push("tr_open","tr",1);v.map=[e,e+1];for(let m=0;m<s.length;m++){const g=u.push("th_open","th",1);l[m]&&(g.attrs=[["style","text-align:"+l[m]]]);const A=u.push("inline","",0);A.content=s[m].trim(),A.children=[],u.push("th_close","th",-1)}u.push("tr_close","tr",-1),u.push("thead_close","thead",-1);let x,_=0;for(r=e+2;r<t&&!(u.sCount[r]<u.blkIndent);r++){let m=!1;for(let A=0,T=h.length;A<T;A++)if(h[A](u,r,t,!0)){m=!0;break}if(m||(a=zu(u,r).trim(),!a)||u.sCount[r]-u.blkIndent>=4||(s=s0(a),s.length&&s[0]===""&&s.shift(),s.length&&s[s.length-1]===""&&s.pop(),_+=d-s.length,_>At))break;if(r===e+2){const A=u.push("tbody_open","tbody",1);A.map=x=[e+2,0]}const g=u.push("tr_open","tr",1);g.map=[r,r+1];for(let A=0;A<d;A++){const T=u.push("td_open","td",1);l[A]&&(T.attrs=[["style","text-align:"+l[A]]]);const R=u.push("inline","",0);R.content=s[A]?s[A].trim():"",R.children=[],u.push("td_close","td",-1)}u.push("tr_close","tr",-1)}return x&&(u.push("tbody_close","tbody",-1),x[1]=r),u.push("table_close","table",-1),k[1]=r,u.parentType=b,u.line=r,!0}function Ft(u,e,t){if(u.sCount[e]-u.blkIndent<4)return!1;let n=e+1,r=n;for(;n<t;){if(u.isEmpty(n)){n++;continue}if(u.sCount[n]-u.blkIndent>=4){n++,r=n;continue}break}u.line=r;const c=u.push("code_block","code",0);return c.content=u.getLines(e,r,4+u.blkIndent,!1)+`
`,c.map=[e,u.line],!0}function vt(u,e,t,n){let r=u.bMarks[e]+u.tShift[e],c=u.eMarks[e];if(u.sCount[e]-u.blkIndent>=4||r+3>c)return!1;const o=u.src.charCodeAt(r);if(o!==126&&o!==96)return!1;let i=r;r=u.skipChars(r,o);let a=r-i;if(a<3)return!1;const s=u.src.slice(i,r),l=u.src.slice(r,c);if(o===96&&l.indexOf(String.fromCharCode(o))>=0)return!1;if(n)return!0;let d=e,b=!1;for(;d++,!(d>=t||(r=i=u.bMarks[d]+u.tShift[d],c=u.eMarks[d],r<c&&u.sCount[d]<u.blkIndent));)if(u.src.charCodeAt(r)===o&&!(u.sCount[d]-u.blkIndent>=4)&&(r=u.skipChars(r,o),!(r-i<a)&&(r=u.skipSpaces(r),!(r<c)))){b=!0;break}a=u.sCount[e],u.line=d+(b?1:0);const h=u.push("fence","code",0);return h.info=l,h.content=u.getLines(e+1,d,a,!0),h.markup=s,h.map=[e,u.line],!0}function wt(u,e,t,n){let r=u.bMarks[e]+u.tShift[e],c=u.eMarks[e];const o=u.lineMax;if(u.sCount[e]-u.blkIndent>=4||u.src.charCodeAt(r)!==62)return!1;if(n)return!0;const i=[],a=[],s=[],l=[],d=u.md.block.ruler.getRules("blockquote"),b=u.parentType;u.parentType="blockquote";let h=!1,f;for(f=e;f<t;f++){const _=u.sCount[f]<u.blkIndent;if(r=u.bMarks[f]+u.tShift[f],c=u.eMarks[f],r>=c)break;if(u.src.charCodeAt(r++)===62&&!_){let g=u.sCount[f]+1,A,T;u.src.charCodeAt(r)===32?(r++,g++,T=!1,A=!0):u.src.charCodeAt(r)===9?(A=!0,(u.bsCount[f]+g)%4===3?(r++,g++,T=!1):T=!0):A=!1;let R=g;for(i.push(u.bMarks[f]),u.bMarks[f]=r;r<c;){const y=u.src.charCodeAt(r);if(B(y))y===9?R+=4-(R+u.bsCount[f]+(T?1:0))%4:R++;else break;r++}h=r>=c,a.push(u.bsCount[f]),u.bsCount[f]=u.sCount[f]+1+(A?1:0),s.push(u.sCount[f]),u.sCount[f]=R-g,l.push(u.tShift[f]),u.tShift[f]=r-u.bMarks[f];continue}if(h)break;let m=!1;for(let g=0,A=d.length;g<A;g++)if(d[g](u,f,t,!0)){m=!0;break}if(m){u.lineMax=f,u.blkIndent!==0&&(i.push(u.bMarks[f]),a.push(u.bsCount[f]),l.push(u.tShift[f]),s.push(u.sCount[f]),u.sCount[f]-=u.blkIndent);break}i.push(u.bMarks[f]),a.push(u.bsCount[f]),l.push(u.tShift[f]),s.push(u.sCount[f]),u.sCount[f]=-1}const k=u.blkIndent;u.blkIndent=0;const C=u.push("blockquote_open","blockquote",1);C.markup=">";const v=[e,0];C.map=v,u.md.block.tokenize(u,e,f);const x=u.push("blockquote_close","blockquote",-1);x.markup=">",u.lineMax=o,u.parentType=b,v[1]=u.line;for(let _=0;_<l.length;_++)u.bMarks[_+e]=i[_],u.tShift[_+e]=l[_],u.sCount[_+e]=s[_],u.bsCount[_+e]=a[_];return u.blkIndent=k,!0}function St(u,e,t,n){const r=u.eMarks[e];if(u.sCount[e]-u.blkIndent>=4)return!1;let c=u.bMarks[e]+u.tShift[e];const o=u.src.charCodeAt(c++);if(o!==42&&o!==45&&o!==95)return!1;let i=1;for(;c<r;){const s=u.src.charCodeAt(c++);if(s!==o&&!B(s))return!1;s===o&&i++}if(i<3)return!1;if(n)return!0;u.line=e+1;const a=u.push("hr","hr",0);return a.map=[e,u.line],a.markup=Array(i+1).join(String.fromCharCode(o)),!0}function l0(u,e){const t=u.eMarks[e];let n=u.bMarks[e]+u.tShift[e];const r=u.src.charCodeAt(n++);if(r!==42&&r!==45&&r!==43)return-1;if(n<t){const c=u.src.charCodeAt(n);if(!B(c))return-1}return n}function f0(u,e){const t=u.bMarks[e]+u.tShift[e],n=u.eMarks[e];let r=t;if(r+1>=n)return-1;let c=u.src.charCodeAt(r++);if(c<48||c>57)return-1;for(;;){if(r>=n)return-1;if(c=u.src.charCodeAt(r++),c>=48&&c<=57){if(r-t>=10)return-1;continue}if(c===41||c===46)break;return-1}return r<n&&(c=u.src.charCodeAt(r),!B(c))?-1:r}function Bt(u,e){const t=u.level+2;for(let n=e+2,r=u.tokens.length-2;n<r;n++)u.tokens[n].level===t&&u.tokens[n].type==="paragraph_open"&&(u.tokens[n+2].hidden=!0,u.tokens[n].hidden=!0,n+=2)}function Tt(u,e,t,n){let r,c,o,i,a=e,s=!0;if(u.sCount[a]-u.blkIndent>=4||u.listIndent>=0&&u.sCount[a]-u.listIndent>=4&&u.sCount[a]<u.blkIndent)return!1;let l=!1;n&&u.parentType==="paragraph"&&u.sCount[a]>=u.blkIndent&&(l=!0);let d,b,h;if((h=f0(u,a))>=0){if(d=!0,o=u.bMarks[a]+u.tShift[a],b=Number(u.src.slice(o,h-1)),l&&b!==1)return!1}else if((h=l0(u,a))>=0)d=!1;else return!1;if(l&&u.skipSpaces(h)>=u.eMarks[a])return!1;if(n)return!0;const f=u.src.charCodeAt(h-1),k=u.tokens.length;d?(i=u.push("ordered_list_open","ol",1),b!==1&&(i.attrs=[["start",b]])):i=u.push("bullet_list_open","ul",1);const C=[a,0];i.map=C,i.markup=String.fromCharCode(f);let v=!1;const x=u.md.block.ruler.getRules("list"),_=u.parentType;for(u.parentType="list";a<t;){c=h,r=u.eMarks[a];const m=u.sCount[a]+h-(u.bMarks[a]+u.tShift[a]);let g=m;for(;c<r;){const W=u.src.charCodeAt(c);if(W===9)g+=4-(g+u.bsCount[a])%4;else if(W===32)g++;else break;c++}const A=c;let T;A>=r?T=1:T=g-m,T>4&&(T=1);const R=m+T;i=u.push("list_item_open","li",1),i.markup=String.fromCharCode(f);const y=[a,0];i.map=y,d&&(i.info=u.src.slice(o,h-1));const p=u.tight,w=u.tShift[a],F=u.sCount[a],j=u.listIndent;if(u.listIndent=u.blkIndent,u.blkIndent=R,u.tight=!0,u.tShift[a]=A-u.bMarks[a],u.sCount[a]=g,A>=r&&u.isEmpty(a+1)?u.line=Math.min(u.line+2,t):u.md.block.tokenize(u,a,t,!0),(!u.tight||v)&&(s=!1),v=u.line-a>1&&u.isEmpty(u.line-1),u.blkIndent=u.listIndent,u.listIndent=j,u.tShift[a]=w,u.sCount[a]=F,u.tight=p,i=u.push("list_item_close","li",-1),i.markup=String.fromCharCode(f),a=u.line,y[1]=a,a>=t||u.sCount[a]<u.blkIndent||u.sCount[a]-u.blkIndent>=4)break;let J=!1;for(let W=0,_u=x.length;W<_u;W++)if(x[W](u,a,t,!0)){J=!0;break}if(J)break;if(d){if(h=f0(u,a),h<0)break;o=u.bMarks[a]+u.tShift[a]}else if(h=l0(u,a),h<0)break;if(f!==u.src.charCodeAt(h-1))break}return d?i=u.push("ordered_list_close","ol",-1):i=u.push("bullet_list_close","ul",-1),i.markup=String.fromCharCode(f),C[1]=a,u.line=a,u.parentType=_,s&&Bt(u,k),!0}function zt(u,e,t,n){let r=u.bMarks[e]+u.tShift[e],c=u.eMarks[e],o=e+1;if(u.sCount[e]-u.blkIndent>=4||u.src.charCodeAt(r)!==91)return!1;function i(x){const _=u.lineMax;if(x>=_||u.isEmpty(x))return null;let m=!1;if(u.sCount[x]-u.blkIndent>3&&(m=!0),u.sCount[x]<0&&(m=!0),!m){const T=u.md.block.ruler.getRules("reference"),R=u.parentType;u.parentType="reference";let y=!1;for(let p=0,w=T.length;p<w;p++)if(T[p](u,x,_,!0)){y=!0;break}if(u.parentType=R,y)return null}const g=u.bMarks[x]+u.tShift[x],A=u.eMarks[x];return u.src.slice(g,A+1)}let a=u.src.slice(r,c+1);c=a.length;let s=-1;for(r=1;r<c;r++){const x=a.charCodeAt(r);if(x===91)return!1;if(x===93){s=r;break}else if(x===10){const _=i(o);_!==null&&(a+=_,c=a.length,o++)}else if(x===92&&(r++,r<c&&a.charCodeAt(r)===10)){const _=i(o);_!==null&&(a+=_,c=a.length,o++)}}if(s<0||a.charCodeAt(s+1)!==58)return!1;for(r=s+2;r<c;r++){const x=a.charCodeAt(r);if(x===10){const _=i(o);_!==null&&(a+=_,c=a.length,o++)}else if(!B(x))break}const l=u.md.helpers.parseLinkDestination(a,r,c);if(!l.ok)return!1;const d=u.md.normalizeLink(l.str);if(!u.md.validateLink(d))return!1;r=l.pos;const b=r,h=o,f=r;for(;r<c;r++){const x=a.charCodeAt(r);if(x===10){const _=i(o);_!==null&&(a+=_,c=a.length,o++)}else if(!B(x))break}let k=u.md.helpers.parseLinkTitle(a,r,c);for(;k.can_continue;){const x=i(o);if(x===null)break;a+=x,r=c,c=a.length,o++,k=u.md.helpers.parseLinkTitle(a,r,c,k)}let C;for(r<c&&f!==r&&k.ok?(C=k.str,r=k.pos):(C="",r=b,o=h);r<c;){const x=a.charCodeAt(r);if(!B(x))break;r++}if(r<c&&a.charCodeAt(r)!==10&&C)for(C="",r=b,o=h;r<c;){const x=a.charCodeAt(r);if(!B(x))break;r++}if(r<c&&a.charCodeAt(r)!==10)return!1;const v=yu(a.slice(1,s));return v?(n||(typeof u.env.references>"u"&&(u.env.references={}),typeof u.env.references[v]>"u"&&(u.env.references[v]={title:C,href:d}),u.line=o),!0):!1}const It=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],Mt="[a-zA-Z_:][a-zA-Z0-9:._-]*",qt="[^\"'=<>`\\x00-\\x20]+",Rt="'[^']*'",Nt='"[^"]*"',Lt="(?:"+qt+"|"+Rt+"|"+Nt+")",Pt="(?:\\s+"+Mt+"(?:\\s*=\\s*"+Lt+")?)",w0="<[A-Za-z][A-Za-z0-9\\-]*"+Pt+"*\\s*\\/?>",S0="<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>",Ot="<!---?>|<!--(?:[^-]|-[^-]|--[^>])*-->",jt="<[?][\\s\\S]*?[?]>",$t="<![A-Za-z][^>]*>",Ut="<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",Zt=new RegExp("^(?:"+w0+"|"+S0+"|"+Ot+"|"+jt+"|"+$t+"|"+Ut+")"),Ht=new RegExp("^(?:"+w0+"|"+S0+")"),nu=[[/^<(script|pre|style|textarea)(?=(\s|>|$))/i,/<\/(script|pre|style|textarea)>/i,!0],[/^<!--/,/-->/,!0],[/^<\?/,/\?>/,!0],[/^<![A-Z]/,/>/,!0],[/^<!\[CDATA\[/,/\]\]>/,!0],[new RegExp("^</?("+It.join("|")+")(?=(\\s|/?>|$))","i"),/^$/,!0],[new RegExp(Ht.source+"\\s*$"),/^$/,!1]];function Vt(u,e,t,n){let r=u.bMarks[e]+u.tShift[e],c=u.eMarks[e];if(u.sCount[e]-u.blkIndent>=4||!u.md.options.html||u.src.charCodeAt(r)!==60)return!1;let o=u.src.slice(r,c),i=0;for(;i<nu.length&&!nu[i][0].test(o);i++);if(i===nu.length)return!1;if(n)return nu[i][2];let a=e+1;if(!nu[i][1].test(o)){for(;a<t&&!(u.sCount[a]<u.blkIndent);a++)if(r=u.bMarks[a]+u.tShift[a],c=u.eMarks[a],o=u.src.slice(r,c),nu[i][1].test(o)){o.length!==0&&a++;break}}u.line=a;const s=u.push("html_block","",0);return s.map=[e,a],s.content=u.getLines(e,a,u.blkIndent,!0),!0}function Gt(u,e,t,n){let r=u.bMarks[e]+u.tShift[e],c=u.eMarks[e];if(u.sCount[e]-u.blkIndent>=4)return!1;let o=u.src.charCodeAt(r);if(o!==35||r>=c)return!1;let i=1;for(o=u.src.charCodeAt(++r);o===35&&r<c&&i<=6;)i++,o=u.src.charCodeAt(++r);if(i>6||r<c&&!B(o))return!1;if(n)return!0;c=u.skipSpacesBack(c,r);const a=u.skipCharsBack(c,35,r);a>r&&B(u.src.charCodeAt(a-1))&&(c=a),u.line=e+1;const s=u.push("heading_open","h"+String(i),1);s.markup="########".slice(0,i),s.map=[e,u.line];const l=u.push("inline","",0);l.content=u.src.slice(r,c).trim(),l.map=[e,u.line],l.children=[];const d=u.push("heading_close","h"+String(i),-1);return d.markup="########".slice(0,i),!0}function Wt(u,e,t){const n=u.md.block.ruler.getRules("paragraph");if(u.sCount[e]-u.blkIndent>=4)return!1;const r=u.parentType;u.parentType="paragraph";let c=0,o,i=e+1;for(;i<t&&!u.isEmpty(i);i++){if(u.sCount[i]-u.blkIndent>3)continue;if(u.sCount[i]>=u.blkIndent){let h=u.bMarks[i]+u.tShift[i];const f=u.eMarks[i];if(h<f&&(o=u.src.charCodeAt(h),(o===45||o===61)&&(h=u.skipChars(h,o),h=u.skipSpaces(h),h>=f))){c=o===61?1:2;break}}if(u.sCount[i]<0)continue;let b=!1;for(let h=0,f=n.length;h<f;h++)if(n[h](u,i,t,!0)){b=!0;break}if(b)break}if(!c)return!1;const a=u.getLines(e,i,u.blkIndent,!1).trim();u.line=i+1;const s=u.push("heading_open","h"+String(c),1);s.markup=String.fromCharCode(o),s.map=[e,u.line];const l=u.push("inline","",0);l.content=a,l.map=[e,u.line-1],l.children=[];const d=u.push("heading_close","h"+String(c),-1);return d.markup=String.fromCharCode(o),u.parentType=r,!0}function Jt(u,e,t){const n=u.md.block.ruler.getRules("paragraph"),r=u.parentType;let c=e+1;for(u.parentType="paragraph";c<t&&!u.isEmpty(c);c++){if(u.sCount[c]-u.blkIndent>3||u.sCount[c]<0)continue;let s=!1;for(let l=0,d=n.length;l<d;l++)if(n[l](u,c,t,!0)){s=!0;break}if(s)break}const o=u.getLines(e,c,u.blkIndent,!1).trim();u.line=c;const i=u.push("paragraph_open","p",1);i.map=[e,u.line];const a=u.push("inline","",0);return a.content=o,a.map=[e,u.line],a.children=[],u.push("paragraph_close","p",-1),u.parentType=r,!0}const gu=[["table",yt,["paragraph","reference"]],["code",Ft],["fence",vt,["paragraph","reference","blockquote","list"]],["blockquote",wt,["paragraph","reference","blockquote","list"]],["hr",St,["paragraph","reference","blockquote","list"]],["list",Tt,["paragraph","reference","blockquote"]],["reference",zt],["html_block",Vt,["paragraph","reference","blockquote"]],["heading",Gt,["paragraph","reference","blockquote"]],["lheading",Wt],["paragraph",Jt]];function Fu(){this.ruler=new q;for(let u=0;u<gu.length;u++)this.ruler.push(gu[u][0],gu[u][1],{alt:(gu[u][2]||[]).slice()})}Fu.prototype.tokenize=function(u,e,t){const n=this.ruler.getRules(""),r=n.length,c=u.md.options.maxNesting;let o=e,i=!1;for(;o<t&&(u.line=o=u.skipEmptyLines(o),!(o>=t||u.sCount[o]<u.blkIndent));){if(u.level>=c){u.line=t;break}const a=u.line;let s=!1;for(let l=0;l<r;l++)if(s=n[l](u,o,t,!1),s){if(a>=u.line)throw new Error("block rule didn't increment state.line");break}if(!s)throw new Error("none of the block rules matched");u.tight=!i,u.isEmpty(u.line-1)&&(i=!0),o=u.line,o<t&&u.isEmpty(o)&&(i=!0,o++,u.line=o)}};Fu.prototype.parse=function(u,e,t,n){if(!u)return;const r=new this.State(u,e,t,n);this.tokenize(r,r.line,r.lineMax)};Fu.prototype.State=G;function pu(u,e,t,n){this.src=u,this.env=t,this.md=e,this.tokens=n,this.tokens_meta=Array(n.length),this.pos=0,this.posMax=this.src.length,this.level=0,this.pending="",this.pendingLevel=0,this.cache={},this.delimiters=[],this._prev_delimiters=[],this.backticks={},this.backticksScanned=!1,this.linkLevel=0}pu.prototype.pushPending=function(){const u=new U("text","",0);return u.content=this.pending,u.level=this.pendingLevel,this.tokens.push(u),this.pending="",u};pu.prototype.push=function(u,e,t){this.pending&&this.pushPending();const n=new U(u,e,t);let r=null;return t<0&&(this.level--,this.delimiters=this._prev_delimiters.pop()),n.level=this.level,t>0&&(this.level++,this._prev_delimiters.push(this.delimiters),this.delimiters=[],r={delimiters:this.delimiters}),this.pendingLevel=this.level,this.tokens.push(n),this.tokens_meta.push(r),n};pu.prototype.scanDelims=function(u,e){const t=this.posMax,n=this.src.charCodeAt(u),r=u>0?this.src.charCodeAt(u-1):32;let c=u;for(;c<t&&this.src.charCodeAt(c)===n;)c++;const o=c-u,i=c<t?this.src.charCodeAt(c):32,a=du(r)||fu(String.fromCharCode(r)),s=du(i)||fu(String.fromCharCode(i)),l=lu(r),d=lu(i),b=!d&&(!s||l||a),h=!l&&(!a||d||s);return{can_open:b&&(e||!h||a),can_close:h&&(e||!b||s),length:o}};pu.prototype.Token=U;function Qt(u){switch(u){case 10:case 33:case 35:case 36:case 37:case 38:case 42:case 43:case 45:case 58:case 60:case 61:case 62:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 125:case 126:return!0;default:return!1}}function Xt(u,e){let t=u.pos;for(;t<u.posMax&&!Qt(u.src.charCodeAt(t));)t++;return t===u.pos?!1:(e||(u.pending+=u.src.slice(u.pos,t)),u.pos=t,!0)}const Yt=/(?:^|[^a-z0-9.+-])([a-z][a-z0-9.+-]*)$/i;function Kt(u,e){if(!u.md.options.linkify||u.linkLevel>0)return!1;const t=u.pos,n=u.posMax;if(t+3>n||u.src.charCodeAt(t)!==58||u.src.charCodeAt(t+1)!==47||u.src.charCodeAt(t+2)!==47)return!1;const r=u.pending.match(Yt);if(!r)return!1;const c=r[1],o=u.md.linkify.matchAtStart(u.src.slice(t-c.length));if(!o)return!1;let i=o.url;if(i.length<=c.length)return!1;i=i.replace(/\*+$/,"");const a=u.md.normalizeLink(i);if(!u.md.validateLink(a))return!1;if(!e){u.pending=u.pending.slice(0,-c.length);const s=u.push("link_open","a",1);s.attrs=[["href",a]],s.markup="linkify",s.info="auto";const l=u.push("text","",0);l.content=u.md.normalizeLinkText(i);const d=u.push("link_close","a",-1);d.markup="linkify",d.info="auto"}return u.pos+=i.length-c.length,!0}function ur(u,e){let t=u.pos;if(u.src.charCodeAt(t)!==10)return!1;const n=u.pending.length-1,r=u.posMax;if(!e)if(n>=0&&u.pending.charCodeAt(n)===32)if(n>=1&&u.pending.charCodeAt(n-1)===32){let c=n-1;for(;c>=1&&u.pending.charCodeAt(c-1)===32;)c--;u.pending=u.pending.slice(0,c),u.push("hardbreak","br",0)}else u.pending=u.pending.slice(0,-1),u.push("softbreak","br",0);else u.push("softbreak","br",0);for(t++;t<r&&B(u.src.charCodeAt(t));)t++;return u.pos=t,!0}const Vu=[];for(let u=0;u<256;u++)Vu.push(0);"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach(function(u){Vu[u.charCodeAt(0)]=1});function er(u,e){let t=u.pos;const n=u.posMax;if(u.src.charCodeAt(t)!==92||(t++,t>=n))return!1;let r=u.src.charCodeAt(t);if(r===10){for(e||u.push("hardbreak","br",0),t++;t<n&&(r=u.src.charCodeAt(t),!!B(r));)t++;return u.pos=t,!0}let c=u.src[t];if(r>=55296&&r<=56319&&t+1<n){const i=u.src.charCodeAt(t+1);i>=56320&&i<=57343&&(c+=u.src[t+1],t++)}const o="\\"+c;if(!e){const i=u.push("text_special","",0);r<256&&Vu[r]!==0?i.content=c:i.content=o,i.markup=o,i.info="escape"}return u.pos=t+1,!0}function tr(u,e){let t=u.pos;if(u.src.charCodeAt(t)!==96)return!1;const r=t;t++;const c=u.posMax;for(;t<c&&u.src.charCodeAt(t)===96;)t++;const o=u.src.slice(r,t),i=o.length;if(u.backticksScanned&&(u.backticks[i]||0)<=r)return e||(u.pending+=o),u.pos+=i,!0;let a=t,s;for(;(s=u.src.indexOf("`",a))!==-1;){for(a=s+1;a<c&&u.src.charCodeAt(a)===96;)a++;const l=a-s;if(l===i){if(!e){const d=u.push("code_inline","code",0);d.markup=o,d.content=u.src.slice(t,s).replace(/\n/g," ").replace(/^ (.+) $/,"$1")}return u.pos=a,!0}u.backticks[l]=s}return u.backticksScanned=!0,e||(u.pending+=o),u.pos+=i,!0}function rr(u,e){const t=u.pos,n=u.src.charCodeAt(t);if(e||n!==126)return!1;const r=u.scanDelims(u.pos,!0);let c=r.length;const o=String.fromCharCode(n);if(c<2)return!1;let i;c%2&&(i=u.push("text","",0),i.content=o,c--);for(let a=0;a<c;a+=2)i=u.push("text","",0),i.content=o+o,u.delimiters.push({marker:n,length:0,token:u.tokens.length-1,end:-1,open:r.can_open,close:r.can_close});return u.pos+=r.length,!0}function d0(u,e){let t;const n=[],r=e.length;for(let c=0;c<r;c++){const o=e[c];if(o.marker!==126||o.end===-1)continue;const i=e[o.end];t=u.tokens[o.token],t.type="s_open",t.tag="s",t.nesting=1,t.markup="~~",t.content="",t=u.tokens[i.token],t.type="s_close",t.tag="s",t.nesting=-1,t.markup="~~",t.content="",u.tokens[i.token-1].type==="text"&&u.tokens[i.token-1].content==="~"&&n.push(i.token-1)}for(;n.length;){const c=n.pop();let o=c+1;for(;o<u.tokens.length&&u.tokens[o].type==="s_close";)o++;o--,c!==o&&(t=u.tokens[o],u.tokens[o]=u.tokens[c],u.tokens[c]=t)}}function nr(u){const e=u.tokens_meta,t=u.tokens_meta.length;d0(u,u.delimiters);for(let n=0;n<t;n++)e[n]&&e[n].delimiters&&d0(u,e[n].delimiters)}const B0={tokenize:rr,postProcess:nr};function cr(u,e){const t=u.pos,n=u.src.charCodeAt(t);if(e||n!==95&&n!==42)return!1;const r=u.scanDelims(u.pos,n===42);for(let c=0;c<r.length;c++){const o=u.push("text","",0);o.content=String.fromCharCode(n),u.delimiters.push({marker:n,length:r.length,token:u.tokens.length-1,end:-1,open:r.can_open,close:r.can_close})}return u.pos+=r.length,!0}function h0(u,e){const t=e.length;for(let n=t-1;n>=0;n--){const r=e[n];if(r.marker!==95&&r.marker!==42||r.end===-1)continue;const c=e[r.end],o=n>0&&e[n-1].end===r.end+1&&e[n-1].marker===r.marker&&e[n-1].token===r.token-1&&e[r.end+1].token===c.token+1,i=String.fromCharCode(r.marker),a=u.tokens[r.token];a.type=o?"strong_open":"em_open",a.tag=o?"strong":"em",a.nesting=1,a.markup=o?i+i:i,a.content="";const s=u.tokens[c.token];s.type=o?"strong_close":"em_close",s.tag=o?"strong":"em",s.nesting=-1,s.markup=o?i+i:i,s.content="",o&&(u.tokens[e[n-1].token].content="",u.tokens[e[r.end+1].token].content="",n--)}}function ir(u){const e=u.tokens_meta,t=u.tokens_meta.length;h0(u,u.delimiters);for(let n=0;n<t;n++)e[n]&&e[n].delimiters&&h0(u,e[n].delimiters)}const T0={tokenize:cr,postProcess:ir};function or(u,e){let t,n,r,c,o="",i="",a=u.pos,s=!0;if(u.src.charCodeAt(u.pos)!==91)return!1;const l=u.pos,d=u.posMax,b=u.pos+1,h=u.md.helpers.parseLinkLabel(u,u.pos,!0);if(h<0)return!1;let f=h+1;if(f<d&&u.src.charCodeAt(f)===40){for(s=!1,f++;f<d&&(t=u.src.charCodeAt(f),!(!B(t)&&t!==10));f++);if(f>=d)return!1;if(a=f,r=u.md.helpers.parseLinkDestination(u.src,f,u.posMax),r.ok){for(o=u.md.normalizeLink(r.str),u.md.validateLink(o)?f=r.pos:o="",a=f;f<d&&(t=u.src.charCodeAt(f),!(!B(t)&&t!==10));f++);if(r=u.md.helpers.parseLinkTitle(u.src,f,u.posMax),f<d&&a!==f&&r.ok)for(i=r.str,f=r.pos;f<d&&(t=u.src.charCodeAt(f),!(!B(t)&&t!==10));f++);}(f>=d||u.src.charCodeAt(f)!==41)&&(s=!0),f++}if(s){if(typeof u.env.references>"u")return!1;if(f<d&&u.src.charCodeAt(f)===91?(a=f+1,f=u.md.helpers.parseLinkLabel(u,f),f>=0?n=u.src.slice(a,f++):f=h+1):f=h+1,n||(n=u.src.slice(b,h)),c=u.env.references[yu(n)],!c)return u.pos=l,!1;o=c.href,i=c.title}if(!e){u.pos=b,u.posMax=h;const k=u.push("link_open","a",1),C=[["href",o]];k.attrs=C,i&&C.push(["title",i]),u.linkLevel++,u.md.inline.tokenize(u),u.linkLevel--,u.push("link_close","a",-1)}return u.pos=f,u.posMax=d,!0}function ar(u,e){let t,n,r,c,o,i,a,s,l="";const d=u.pos,b=u.posMax;if(u.src.charCodeAt(u.pos)!==33||u.src.charCodeAt(u.pos+1)!==91)return!1;const h=u.pos+2,f=u.md.helpers.parseLinkLabel(u,u.pos+1,!1);if(f<0)return!1;if(c=f+1,c<b&&u.src.charCodeAt(c)===40){for(c++;c<b&&(t=u.src.charCodeAt(c),!(!B(t)&&t!==10));c++);if(c>=b)return!1;for(s=c,i=u.md.helpers.parseLinkDestination(u.src,c,u.posMax),i.ok&&(l=u.md.normalizeLink(i.str),u.md.validateLink(l)?c=i.pos:l=""),s=c;c<b&&(t=u.src.charCodeAt(c),!(!B(t)&&t!==10));c++);if(i=u.md.helpers.parseLinkTitle(u.src,c,u.posMax),c<b&&s!==c&&i.ok)for(a=i.str,c=i.pos;c<b&&(t=u.src.charCodeAt(c),!(!B(t)&&t!==10));c++);else a="";if(c>=b||u.src.charCodeAt(c)!==41)return u.pos=d,!1;c++}else{if(typeof u.env.references>"u")return!1;if(c<b&&u.src.charCodeAt(c)===91?(s=c+1,c=u.md.helpers.parseLinkLabel(u,c),c>=0?r=u.src.slice(s,c++):c=f+1):c=f+1,r||(r=u.src.slice(h,f)),o=u.env.references[yu(r)],!o)return u.pos=d,!1;l=o.href,a=o.title}if(!e){n=u.src.slice(h,f);const k=[];u.md.inline.parse(n,u.md,u.env,k);const C=u.push("image","img",0),v=[["src",l],["alt",""]];C.attrs=v,C.children=k,C.content=n,a&&v.push(["title",a])}return u.pos=c,u.posMax=b,!0}const sr=/^([a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/,lr=/^([a-zA-Z][a-zA-Z0-9+.-]{1,31}):([^<>\x00-\x20]*)$/;function fr(u,e){let t=u.pos;if(u.src.charCodeAt(t)!==60)return!1;const n=u.pos,r=u.posMax;for(;;){if(++t>=r)return!1;const o=u.src.charCodeAt(t);if(o===60)return!1;if(o===62)break}const c=u.src.slice(n+1,t);if(lr.test(c)){const o=u.md.normalizeLink(c);if(!u.md.validateLink(o))return!1;if(!e){const i=u.push("link_open","a",1);i.attrs=[["href",o]],i.markup="autolink",i.info="auto";const a=u.push("text","",0);a.content=u.md.normalizeLinkText(c);const s=u.push("link_close","a",-1);s.markup="autolink",s.info="auto"}return u.pos+=c.length+2,!0}if(sr.test(c)){const o=u.md.normalizeLink("mailto:"+c);if(!u.md.validateLink(o))return!1;if(!e){const i=u.push("link_open","a",1);i.attrs=[["href",o]],i.markup="autolink",i.info="auto";const a=u.push("text","",0);a.content=u.md.normalizeLinkText(c);const s=u.push("link_close","a",-1);s.markup="autolink",s.info="auto"}return u.pos+=c.length+2,!0}return!1}function dr(u){return/^<a[>\s]/i.test(u)}function hr(u){return/^<\/a\s*>/i.test(u)}function br(u){const e=u|32;return e>=97&&e<=122}function pr(u,e){if(!u.md.options.html)return!1;const t=u.posMax,n=u.pos;if(u.src.charCodeAt(n)!==60||n+2>=t)return!1;const r=u.src.charCodeAt(n+1);if(r!==33&&r!==63&&r!==47&&!br(r))return!1;const c=u.src.slice(n).match(Zt);if(!c)return!1;if(!e){const o=u.push("html_inline","",0);o.content=c[0],dr(o.content)&&u.linkLevel++,hr(o.content)&&u.linkLevel--}return u.pos+=c[0].length,!0}const xr=/^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i,_r=/^&([a-z][a-z0-9]{1,31});/i;function mr(u,e){const t=u.pos,n=u.posMax;if(u.src.charCodeAt(t)!==38||t+1>=n)return!1;if(u.src.charCodeAt(t+1)===35){const c=u.src.slice(t).match(xr);if(c){if(!e){const o=c[1][0].toLowerCase()==="x"?parseInt(c[1].slice(1),16):parseInt(c[1],10),i=u.push("text_special","",0);i.content=Zu(o)?Cu(o):Cu(65533),i.markup=c[0],i.info="entity"}return u.pos+=c[0].length,!0}}else{const c=u.src.slice(t).match(_r);if(c){const o=E0(c[0]);if(o!==c[0]){if(!e){const i=u.push("text_special","",0);i.content=o,i.markup=c[0],i.info="entity"}return u.pos+=c[0].length,!0}}}return!1}function b0(u){const e={},t=u.length;if(!t)return;let n=0,r=-2;const c=[];for(let o=0;o<t;o++){const i=u[o];if(c.push(0),(u[n].marker!==i.marker||r!==i.token-1)&&(n=o),r=i.token,i.length=i.length||0,!i.close)continue;e.hasOwnProperty(i.marker)||(e[i.marker]=[-1,-1,-1,-1,-1,-1]);const a=e[i.marker][(i.open?3:0)+i.length%3];let s=n-c[n]-1,l=s;for(;s>a;s-=c[s]+1){const d=u[s];if(d.marker===i.marker&&d.open&&d.end<0){let b=!1;if((d.close||i.open)&&(d.length+i.length)%3===0&&(d.length%3!==0||i.length%3!==0)&&(b=!0),!b){const h=s>0&&!u[s-1].open?c[s-1]+1:0;c[o]=o-s+h,c[s]=h,i.open=!1,d.end=o,d.close=!1,l=-1,r=-2;break}}}l!==-1&&(e[i.marker][(i.open?3:0)+(i.length||0)%3]=l)}}function kr(u){const e=u.tokens_meta,t=u.tokens_meta.length;b0(u.delimiters);for(let n=0;n<t;n++)e[n]&&e[n].delimiters&&b0(e[n].delimiters)}function gr(u){let e,t,n=0;const r=u.tokens,c=u.tokens.length;for(e=t=0;e<c;e++)r[e].nesting<0&&n--,r[e].level=n,r[e].nesting>0&&n++,r[e].type==="text"&&e+1<c&&r[e+1].type==="text"?r[e+1].content=r[e].content+r[e+1].content:(e!==t&&(r[t]=r[e]),t++);e!==t&&(r.length=t)}const Iu=[["text",Xt],["linkify",Kt],["newline",ur],["escape",er],["backticks",tr],["strikethrough",B0.tokenize],["emphasis",T0.tokenize],["link",or],["image",ar],["autolink",fr],["html_inline",pr],["entity",mr]],Mu=[["balance_pairs",kr],["strikethrough",B0.postProcess],["emphasis",T0.postProcess],["fragments_join",gr]];function xu(){this.ruler=new q;for(let u=0;u<Iu.length;u++)this.ruler.push(Iu[u][0],Iu[u][1]);this.ruler2=new q;for(let u=0;u<Mu.length;u++)this.ruler2.push(Mu[u][0],Mu[u][1])}xu.prototype.skipToken=function(u){const e=u.pos,t=this.ruler.getRules(""),n=t.length,r=u.md.options.maxNesting,c=u.cache;if(typeof c[e]<"u"){u.pos=c[e];return}let o=!1;if(u.level<r){for(let i=0;i<n;i++)if(u.level++,o=t[i](u,!0),u.level--,o){if(e>=u.pos)throw new Error("inline rule didn't increment state.pos");break}}else u.pos=u.posMax;o||u.pos++,c[e]=u.pos};xu.prototype.tokenize=function(u){const e=this.ruler.getRules(""),t=e.length,n=u.posMax,r=u.md.options.maxNesting;for(;u.pos<n;){const c=u.pos;let o=!1;if(u.level<r){for(let i=0;i<t;i++)if(o=e[i](u,!1),o){if(c>=u.pos)throw new Error("inline rule didn't increment state.pos");break}}if(o){if(u.pos>=n)break;continue}u.pending+=u.src[u.pos++]}u.pending&&u.pushPending()};xu.prototype.parse=function(u,e,t,n){const r=new this.State(u,e,t,n);this.tokenize(r);const c=this.ruler2.getRules(""),o=c.length;for(let i=0;i<o;i++)c[i](r)};xu.prototype.State=pu;function Dr(u){const e={};u=u||{},e.src_Any=m0.source,e.src_Cc=k0.source,e.src_Z=D0.source,e.src_P=$u.source,e.src_ZPCc=[e.src_Z,e.src_P,e.src_Cc].join("|"),e.src_ZCc=[e.src_Z,e.src_Cc].join("|");const t="[><｜]";return e.src_pseudo_letter="(?:(?!"+t+"|"+e.src_ZPCc+")"+e.src_Any+")",e.src_ip4="(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)",e.src_auth="(?:(?:(?!"+e.src_ZCc+"|[@/\\[\\]()]).)+@)?",e.src_port="(?::(?:6(?:[0-4]\\d{3}|5(?:[0-4]\\d{2}|5(?:[0-2]\\d|3[0-5])))|[1-5]?\\d{1,4}))?",e.src_host_terminator="(?=$|"+t+"|"+e.src_ZPCc+")(?!"+(u["---"]?"-(?!--)|":"-|")+"_|:\\d|\\.-|\\.(?!$|"+e.src_ZPCc+"))",e.src_path="(?:[/?#](?:(?!"+e.src_ZCc+"|"+t+`|[()[\\]{}.,"'?!\\-;]).|\\[(?:(?!`+e.src_ZCc+"|\\]).)*\\]|\\((?:(?!"+e.src_ZCc+"|[)]).)*\\)|\\{(?:(?!"+e.src_ZCc+'|[}]).)*\\}|\\"(?:(?!'+e.src_ZCc+`|["]).)+\\"|\\'(?:(?!`+e.src_ZCc+"|[']).)+\\'|\\'(?="+e.src_pseudo_letter+"|[-])|\\.{2,}[a-zA-Z0-9%/&]|\\.(?!"+e.src_ZCc+"|[.]|$)|"+(u["---"]?"\\-(?!--(?:[^-]|$))(?:-*)|":"\\-+|")+",(?!"+e.src_ZCc+"|$)|;(?!"+e.src_ZCc+"|$)|\\!+(?!"+e.src_ZCc+"|[!]|$)|\\?(?!"+e.src_ZCc+"|[?]|$))+|\\/)?",e.src_email_name='[\\-;:&=\\+\\$,\\.a-zA-Z0-9_][\\-;:&=\\+\\$,\\"\\.a-zA-Z0-9_]*',e.src_xn="xn--[a-z0-9\\-]{1,59}",e.src_domain_root="(?:"+e.src_xn+"|"+e.src_pseudo_letter+"{1,63})",e.src_domain="(?:"+e.src_xn+"|(?:"+e.src_pseudo_letter+")|(?:"+e.src_pseudo_letter+"(?:-|"+e.src_pseudo_letter+"){0,61}"+e.src_pseudo_letter+"))",e.src_host="(?:(?:(?:(?:"+e.src_domain+")\\.)*"+e.src_domain+"))",e.tpl_host_fuzzy="(?:"+e.src_ip4+"|(?:(?:(?:"+e.src_domain+")\\.)+(?:%TLDS%)))",e.tpl_host_no_ip_fuzzy="(?:(?:(?:"+e.src_domain+")\\.)+(?:%TLDS%))",e.src_host_strict=e.src_host+e.src_host_terminator,e.tpl_host_fuzzy_strict=e.tpl_host_fuzzy+e.src_host_terminator,e.src_host_port_strict=e.src_host+e.src_port+e.src_host_terminator,e.tpl_host_port_fuzzy_strict=e.tpl_host_fuzzy+e.src_port+e.src_host_terminator,e.tpl_host_port_no_ip_fuzzy_strict=e.tpl_host_no_ip_fuzzy+e.src_port+e.src_host_terminator,e.tpl_host_fuzzy_test="localhost|www\\.|\\.\\d{1,3}\\.|(?:\\.(?:%TLDS%)(?:"+e.src_ZPCc+"|>|$))",e.tpl_email_fuzzy="(^|"+t+'|"|\\(|'+e.src_ZCc+")("+e.src_email_name+"@"+e.tpl_host_fuzzy_strict+")",e.tpl_link_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+e.src_ZPCc+"))((?![$+<=>^`|｜])"+e.tpl_host_port_fuzzy_strict+e.src_path+")",e.tpl_link_no_ip_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+e.src_ZPCc+"))((?![$+<=>^`|｜])"+e.tpl_host_port_no_ip_fuzzy_strict+e.src_path+")",e}function Lu(u){return Array.prototype.slice.call(arguments,1).forEach(function(t){t&&Object.keys(t).forEach(function(n){u[n]=t[n]})}),u}function vu(u){return Object.prototype.toString.call(u)}function Cr(u){return vu(u)==="[object String]"}function Er(u){return vu(u)==="[object Object]"}function Ar(u){return vu(u)==="[object RegExp]"}function p0(u){return vu(u)==="[object Function]"}function yr(u){return u.replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}const z0={fuzzyLink:!0,fuzzyEmail:!0,fuzzyIP:!1};function Fr(u){return Object.keys(u||{}).reduce(function(e,t){return e||z0.hasOwnProperty(t)},!1)}const vr={"http:":{validate:function(u,e,t){const n=u.slice(e);return t.re.http||(t.re.http=new RegExp("^\\/\\/"+t.re.src_auth+t.re.src_host_port_strict+t.re.src_path,"i")),t.re.http.test(n)?n.match(t.re.http)[0].length:0}},"https:":"http:","ftp:":"http:","//":{validate:function(u,e,t){const n=u.slice(e);return t.re.no_http||(t.re.no_http=new RegExp("^"+t.re.src_auth+"(?:localhost|(?:(?:"+t.re.src_domain+")\\.)+"+t.re.src_domain_root+")"+t.re.src_port+t.re.src_host_terminator+t.re.src_path,"i")),t.re.no_http.test(n)?e>=3&&u[e-3]===":"||e>=3&&u[e-3]==="/"?0:n.match(t.re.no_http)[0].length:0}},"mailto:":{validate:function(u,e,t){const n=u.slice(e);return t.re.mailto||(t.re.mailto=new RegExp("^"+t.re.src_email_name+"@"+t.re.src_host_strict,"i")),t.re.mailto.test(n)?n.match(t.re.mailto)[0].length:0}}},wr="a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]",Sr="biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф".split("|");function Br(u){u.__index__=-1,u.__text_cache__=""}function Tr(u){return function(e,t){const n=e.slice(t);return u.test(n)?n.match(u)[0].length:0}}function x0(){return function(u,e){e.normalize(u)}}function Eu(u){const e=u.re=Dr(u.__opts__),t=u.__tlds__.slice();u.onCompile(),u.__tlds_replaced__||t.push(wr),t.push(e.src_xn),e.src_tlds=t.join("|");function n(i){return i.replace("%TLDS%",e.src_tlds)}e.email_fuzzy=RegExp(n(e.tpl_email_fuzzy),"i"),e.link_fuzzy=RegExp(n(e.tpl_link_fuzzy),"i"),e.link_no_ip_fuzzy=RegExp(n(e.tpl_link_no_ip_fuzzy),"i"),e.host_fuzzy_test=RegExp(n(e.tpl_host_fuzzy_test),"i");const r=[];u.__compiled__={};function c(i,a){throw new Error('(LinkifyIt) Invalid schema "'+i+'": '+a)}Object.keys(u.__schemas__).forEach(function(i){const a=u.__schemas__[i];if(a===null)return;const s={validate:null,link:null};if(u.__compiled__[i]=s,Er(a)){Ar(a.validate)?s.validate=Tr(a.validate):p0(a.validate)?s.validate=a.validate:c(i,a),p0(a.normalize)?s.normalize=a.normalize:a.normalize?c(i,a):s.normalize=x0();return}if(Cr(a)){r.push(i);return}c(i,a)}),r.forEach(function(i){u.__compiled__[u.__schemas__[i]]&&(u.__compiled__[i].validate=u.__compiled__[u.__schemas__[i]].validate,u.__compiled__[i].normalize=u.__compiled__[u.__schemas__[i]].normalize)}),u.__compiled__[""]={validate:null,normalize:x0()};const o=Object.keys(u.__compiled__).filter(function(i){return i.length>0&&u.__compiled__[i]}).map(yr).join("|");u.re.schema_test=RegExp("(^|(?!_)(?:[><｜]|"+e.src_ZPCc+"))("+o+")","i"),u.re.schema_search=RegExp("(^|(?!_)(?:[><｜]|"+e.src_ZPCc+"))("+o+")","ig"),u.re.schema_at_start=RegExp("^"+u.re.schema_search.source,"i"),u.re.pretest=RegExp("("+u.re.schema_test.source+")|("+u.re.host_fuzzy_test.source+")|@","i"),Br(u)}function zr(u,e){const t=u.__index__,n=u.__last_index__,r=u.__text_cache__.slice(t,n);this.schema=u.__schema__.toLowerCase(),this.index=t+e,this.lastIndex=n+e,this.raw=r,this.text=r,this.url=r}function Pu(u,e){const t=new zr(u,e);return u.__compiled__[t.schema].normalize(t,u),t}function N(u,e){if(!(this instanceof N))return new N(u,e);e||Fr(u)&&(e=u,u={}),this.__opts__=Lu({},z0,e),this.__index__=-1,this.__last_index__=-1,this.__schema__="",this.__text_cache__="",this.__schemas__=Lu({},vr,u),this.__compiled__={},this.__tlds__=Sr,this.__tlds_replaced__=!1,this.re={},Eu(this)}N.prototype.add=function(e,t){return this.__schemas__[e]=t,Eu(this),this};N.prototype.set=function(e){return this.__opts__=Lu(this.__opts__,e),this};N.prototype.test=function(e){if(this.__text_cache__=e,this.__index__=-1,!e.length)return!1;let t,n,r,c,o,i,a,s,l;if(this.re.schema_test.test(e)){for(a=this.re.schema_search,a.lastIndex=0;(t=a.exec(e))!==null;)if(c=this.testSchemaAt(e,t[2],a.lastIndex),c){this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+c;break}}return this.__opts__.fuzzyLink&&this.__compiled__["http:"]&&(s=e.search(this.re.host_fuzzy_test),s>=0&&(this.__index__<0||s<this.__index__)&&(n=e.match(this.__opts__.fuzzyIP?this.re.link_fuzzy:this.re.link_no_ip_fuzzy))!==null&&(o=n.index+n[1].length,(this.__index__<0||o<this.__index__)&&(this.__schema__="",this.__index__=o,this.__last_index__=n.index+n[0].length))),this.__opts__.fuzzyEmail&&this.__compiled__["mailto:"]&&(l=e.indexOf("@"),l>=0&&(r=e.match(this.re.email_fuzzy))!==null&&(o=r.index+r[1].length,i=r.index+r[0].length,(this.__index__<0||o<this.__index__||o===this.__index__&&i>this.__last_index__)&&(this.__schema__="mailto:",this.__index__=o,this.__last_index__=i))),this.__index__>=0};N.prototype.pretest=function(e){return this.re.pretest.test(e)};N.prototype.testSchemaAt=function(e,t,n){return this.__compiled__[t.toLowerCase()]?this.__compiled__[t.toLowerCase()].validate(e,n,this):0};N.prototype.match=function(e){const t=[];let n=0;this.__index__>=0&&this.__text_cache__===e&&(t.push(Pu(this,n)),n=this.__last_index__);let r=n?e.slice(n):e;for(;this.test(r);)t.push(Pu(this,n)),r=r.slice(this.__last_index__),n+=this.__last_index__;return t.length?t:null};N.prototype.matchAtStart=function(e){if(this.__text_cache__=e,this.__index__=-1,!e.length)return null;const t=this.re.schema_at_start.exec(e);if(!t)return null;const n=this.testSchemaAt(e,t[2],t[0].length);return n?(this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+n,Pu(this,0)):null};N.prototype.tlds=function(e,t){return e=Array.isArray(e)?e:[e],t?(this.__tlds__=this.__tlds__.concat(e).sort().filter(function(n,r,c){return n!==c[r-1]}).reverse(),Eu(this),this):(this.__tlds__=e.slice(),this.__tlds_replaced__=!0,Eu(this),this)};N.prototype.normalize=function(e){e.schema||(e.url="http://"+e.url),e.schema==="mailto:"&&!/^mailto:/i.test(e.url)&&(e.url="mailto:"+e.url)};N.prototype.onCompile=function(){};const cu=2147483647,Z=36,Gu=1,hu=26,Ir=38,Mr=700,I0=72,M0=128,q0="-",qr=/^xn--/,Rr=/[^\0-\x7F]/,Nr=/[\x2E\u3002\uFF0E\uFF61]/g,Lr={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},qu=Z-Gu,H=Math.floor,Ru=String.fromCharCode;function X(u){throw new RangeError(Lr[u])}function Pr(u,e){const t=[];let n=u.length;for(;n--;)t[n]=e(u[n]);return t}function R0(u,e){const t=u.split("@");let n="";t.length>1&&(n=t[0]+"@",u=t[1]),u=u.replace(Nr,".");const r=u.split("."),c=Pr(r,e).join(".");return n+c}function N0(u){const e=[];let t=0;const n=u.length;for(;t<n;){const r=u.charCodeAt(t++);if(r>=55296&&r<=56319&&t<n){const c=u.charCodeAt(t++);(c&64512)==56320?e.push(((r&1023)<<10)+(c&1023)+65536):(e.push(r),t--)}else e.push(r)}return e}const Or=u=>String.fromCodePoint(...u),jr=function(u){return u>=48&&u<58?26+(u-48):u>=65&&u<91?u-65:u>=97&&u<123?u-97:Z},_0=function(u,e){return u+22+75*(u<26)-((e!=0)<<5)},L0=function(u,e,t){let n=0;for(u=t?H(u/Mr):u>>1,u+=H(u/e);u>qu*hu>>1;n+=Z)u=H(u/qu);return H(n+(qu+1)*u/(u+Ir))},P0=function(u){const e=[],t=u.length;let n=0,r=M0,c=I0,o=u.lastIndexOf(q0);o<0&&(o=0);for(let i=0;i<o;++i)u.charCodeAt(i)>=128&&X("not-basic"),e.push(u.charCodeAt(i));for(let i=o>0?o+1:0;i<t;){const a=n;for(let l=1,d=Z;;d+=Z){i>=t&&X("invalid-input");const b=jr(u.charCodeAt(i++));b>=Z&&X("invalid-input"),b>H((cu-n)/l)&&X("overflow"),n+=b*l;const h=d<=c?Gu:d>=c+hu?hu:d-c;if(b<h)break;const f=Z-h;l>H(cu/f)&&X("overflow"),l*=f}const s=e.length+1;c=L0(n-a,s,a==0),H(n/s)>cu-r&&X("overflow"),r+=H(n/s),n%=s,e.splice(n++,0,r)}return String.fromCodePoint(...e)},O0=function(u){const e=[];u=N0(u);const t=u.length;let n=M0,r=0,c=I0;for(const a of u)a<128&&e.push(Ru(a));const o=e.length;let i=o;for(o&&e.push(q0);i<t;){let a=cu;for(const l of u)l>=n&&l<a&&(a=l);const s=i+1;a-n>H((cu-r)/s)&&X("overflow"),r+=(a-n)*s,n=a;for(const l of u)if(l<n&&++r>cu&&X("overflow"),l===n){let d=r;for(let b=Z;;b+=Z){const h=b<=c?Gu:b>=c+hu?hu:b-c;if(d<h)break;const f=d-h,k=Z-h;e.push(Ru(_0(h+f%k,0))),d=H(f/k)}e.push(Ru(_0(d,0))),c=L0(r,s,i===o),r=0,++i}++r,++n}return e.join("")},$r=function(u){return R0(u,function(e){return qr.test(e)?P0(e.slice(4).toLowerCase()):e})},Ur=function(u){return R0(u,function(e){return Rr.test(e)?"xn--"+O0(e):e})},j0={version:"2.3.1",ucs2:{decode:N0,encode:Or},decode:P0,encode:O0,toASCII:Ur,toUnicode:$r},Zr={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:100},components:{core:{},block:{},inline:{}}},Hr={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline","text_join"]},block:{rules:["paragraph"]},inline:{rules:["text"],rules2:["balance_pairs","fragments_join"]}}},Vr={options:{html:!0,xhtmlOut:!0,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline","text_join"]},block:{rules:["blockquote","code","fence","heading","hr","html_block","lheading","list","reference","paragraph"]},inline:{rules:["autolink","backticks","emphasis","entity","escape","html_inline","image","link","newline","text"],rules2:["balance_pairs","emphasis","fragments_join"]}}},Gr={default:Zr,zero:Hr,commonmark:Vr},Wr=/^(vbscript|javascript|file|data):/,Jr=/^data:image\/(gif|png|jpeg|webp);/;function Qr(u){const e=u.trim().toLowerCase();return Wr.test(e)?Jr.test(e):!0}const $0=["http:","https:","mailto:"];function Xr(u){const e=ju(u,!0);if(e.hostname&&(!e.protocol||$0.indexOf(e.protocol)>=0))try{e.hostname=j0.toASCII(e.hostname)}catch{}return bu(Ou(e))}function Yr(u){const e=ju(u,!0);if(e.hostname&&(!e.protocol||$0.indexOf(e.protocol)>=0))try{e.hostname=j0.toUnicode(e.hostname)}catch{}return iu(Ou(e),iu.defaultChars+"%")}function O(u,e){if(!(this instanceof O))return new O(u,e);e||Uu(u)||(e=u||{},u="default"),this.inline=new xu,this.block=new Fu,this.core=new Hu,this.renderer=new au,this.linkify=new N,this.validateLink=Qr,this.normalizeLink=Xr,this.normalizeLinkText=Yr,this.utils=ut,this.helpers=Au({},nt),this.options={},this.configure(u),e&&this.set(e)}O.prototype.set=function(u){return Au(this.options,u),this};O.prototype.configure=function(u){const e=this;if(Uu(u)){const t=u;if(u=Gr[t],!u)throw new Error('Wrong `markdown-it` preset "'+t+'", check name')}if(!u)throw new Error("Wrong `markdown-it` preset, can't be empty");return u.options&&e.set(u.options),u.components&&Object.keys(u.components).forEach(function(t){u.components[t].rules&&e[t].ruler.enableOnly(u.components[t].rules),u.components[t].rules2&&e[t].ruler2.enableOnly(u.components[t].rules2)}),this};O.prototype.enable=function(u,e){let t=[];Array.isArray(u)||(u=[u]),["core","block","inline"].forEach(function(r){t=t.concat(this[r].ruler.enable(u,!0))},this),t=t.concat(this.inline.ruler2.enable(u,!0));const n=u.filter(function(r){return t.indexOf(r)<0});if(n.length&&!e)throw new Error("MarkdownIt. Failed to enable unknown rule(s): "+n);return this};O.prototype.disable=function(u,e){let t=[];Array.isArray(u)||(u=[u]),["core","block","inline"].forEach(function(r){t=t.concat(this[r].ruler.disable(u,!0))},this),t=t.concat(this.inline.ruler2.disable(u,!0));const n=u.filter(function(r){return t.indexOf(r)<0});if(n.length&&!e)throw new Error("MarkdownIt. Failed to disable unknown rule(s): "+n);return this};O.prototype.use=function(u){const e=[this].concat(Array.prototype.slice.call(arguments,1));return u.apply(u,e),this};O.prototype.parse=function(u,e){if(typeof u!="string")throw new Error("Input data should be a String");const t=new this.core.State(u,this,e);return this.core.process(t),t.tokens};O.prototype.render=function(u,e){return e=e||{},this.renderer.render(this.parse(u,e),this.options,e)};O.prototype.parseInline=function(u,e){const t=new this.core.State(u,this,e);return t.inlineMode=!0,this.core.process(t),t.tokens};O.prototype.renderInline=function(u,e){return e=e||{},this.renderer.render(this.parseInline(u,e),this.options,e)};const Kr={class:"adapter-detail"},un={class:"adapter-header"},en={class:"adapter-title"},tn={class:"title-text"},rn=["innerHTML"],nn={class:"adapter-content"},cn={class:"instances-panel"},on={class:"panel-header"},an={class:"instances-list"},sn={class:"config-panel"},ln={class:"panel-header"},fn={key:0,class:"config-form"},dn={key:0,class:"dynamic-config"},hn={key:1,class:"empty-config"},bn=U0({__name:"IMAdapterDetail",setup(u,{expose:e}){const t=G0(),n=W0(),r=Z0(),c=H0(()=>t.params.adapterType),o=Q(!1),i=Q(!1),a=Q(null),s=Q([]),l=Q(null),d=Q(null),b=Q(!1),h=new O,f=h.renderer.rules.link_open||function(y,p,w,F,j){return j.renderToken(y,p,w)};h.renderer.rules.link_open=function(y,p,w,F,j){return y[p].attrSet("target","_blank"),f(y,p,w,F,j)};const k=Q(null),C=async()=>{try{o.value=!0;const{configSchema:y}=await tu.getAdapterConfigSchema(c.value);a.value=y}catch(y){r.error("获取适配器配置模式失败: "+y),console.error("获取适配器配置模式失败:",y)}finally{o.value=!1}},v=async()=>{try{o.value=!0;const{adapters:y}=await tu.getAdapters();let p=new Map;s.value.length>0&&s.value.forEach(w=>{p.set(w.name,w.bot_profile)}),s.value=y.filter(w=>w.adapter===c.value),s.value.length>0&&s.value.forEach(w=>{w.bot_profile=p.get(w.name)||null}),s.value.forEach(async w=>{const{adapter:F}=await tu.getAdapterDetail(w.name);w.bot_profile=F.bot_profile})}catch(y){r.error("获取适配器列表失败: "+y),console.error("获取适配器列表失败:",y)}finally{o.value=!1}},x=async()=>{b.value=!1,await C(),l.value={name:"",adapter:c.value,config:{},is_running:!1,enable:!0,bot_profile:null}},_=y=>{b.value=!0,l.value={...y}},m=async()=>{var y,p;if(l.value)try{i.value=!0;try{const w=await((y=d.value)==null?void 0:y.validate());if((p=w==null?void 0:w.warnings)!=null&&p.length)return}catch{r.error("保存适配器失败: 请检查输入内容");return}b.value?await tu.updateAdapter(l.value.name,l.value):(await tu.createAdapter(l.value),b.value=!0),r.success("保存适配器成功")}catch(w){r.error("保存适配器失败: "+w),console.error("保存适配器失败:",w)}finally{i.value=!1,await v()}},g=async y=>{try{i.value=!0,await tu.deleteAdapter(y),l.value=null,r.success("删除适配器成功")}catch(p){r.error("删除适配器失败: "+p),console.error("删除适配器失败:",p)}finally{i.value=!1,await v()}},A=()=>{n.push("/im")},T={name:[{required:!0,message:"请输入适配器名称",trigger:"blur"},{validator:(y,p)=>l.value&&s.value.some(F=>{var j;return F.name===p&&F.name!==((j=l.value)==null?void 0:j.name)})?new Error("适配器名称已存在"):!0,trigger:"blur"}]},R=async()=>{const{adapters:y}=await tu.getAdapterTypes();y?k.value=y[c.value]||null:k.value=null};return V0(async()=>{await R(),await C(),await v()}),e({fetchAdapters:v,currentAdapter:l,processing:i,formRef:d}),(y,p)=>(M(),su("div",Kr,[S(D(he),{show:o.value||i.value},{default:E(()=>[S(D(Wu),{class:"adapter-card",style:{"min-height":"var(--n-window-height)"}},{header:E(()=>{var w;return[L("div",un,[L("div",en,[S(D(ru),{quaternary:"",circle:"",onClick:A,class:"back-button"},{icon:E(()=>[S(D(mu),null,{default:E(()=>[S(D(ne))]),_:1})]),_:1}),L("span",tn,Ju(((w=k.value)==null?void 0:w.localized_name)||c.value),1)])])]}),default:E(()=>{var w;return[(w=k.value)!=null&&w.detail_info_markdown?(M(),P(D(ce),{key:0,type:"info",class:"adapter-info","show-icon":!1},{default:E(()=>{var F;return[L("div",{innerHTML:D(h).render((F=k.value)==null?void 0:F.detail_info_markdown),class:"markdown-content"},null,8,rn)]}),_:1})):eu("",!0),L("div",nn,[L("div",cn,[L("div",on,[S(D(wu),{justify:"space-between",align:"center"},{default:E(()=>[p[4]||(p[4]=L("h3",{class:"panel-title"},"实例列表",-1)),s.value.length>0?(M(),P(D(ru),{key:0,type:"primary",onClick:x,size:"small",class:"add-button"},{icon:E(()=>[S(D(mu),null,{default:E(()=>[S(D(Xu))]),_:1})]),default:E(()=>[p[3]||(p[3]=$(" 添加配置 "))]),_:1})):eu("",!0)]),_:1})]),L("div",an,[S(D(te),{style:{"max-height":"600px"}},{default:E(()=>[s.value.length===0?(M(),P(D(Ku),{key:0,description:"暂无配置",class:"empty-state"})):eu("",!0),(M(!0),su(J0,null,Q0(s.value,F=>{var j;return M(),P(D(Wu),{key:F.name,hoverable:"",onClick:J=>_(F),class:K0(["instance-card",{active:((j=l.value)==null?void 0:j.name)===F.name}])},{default:E(()=>[S(D(se),{title:F.name,description:F.bot_profile?F.bot_profile.display_name:"",class:"instance-thing"},{avatar:E(()=>{var J,W;return[F.bot_profile&&((J=F.bot_profile)!=null&&J.avatar_url)?(M(),P(D(Qu),{key:0,round:"",src:(W=F.bot_profile)==null?void 0:W.avatar_url,class:"avatar"},null,8,["src"])):(M(),P(D(Qu),{key:1,round:"",class:"avatar default-avatar"},{default:E(()=>{var _u;return[$(Ju((F.bot_profile?(_u=F.bot_profile)==null?void 0:_u.username:F.name).slice(0,1).toUpperCase()),1)]}),_:2},1024))]}),"header-extra":E(()=>[F.enable?F.is_running?(M(),P(D(Su),{key:1,type:"success",class:"status-tag running"},{default:E(()=>p[6]||(p[6]=[$(" 运行中 ")])),_:1})):(M(),P(D(Su),{key:2,type:"warning",class:"status-tag stopped"},{default:E(()=>p[7]||(p[7]=[$(" 已停止 ")])),_:1})):(M(),P(D(Su),{key:0,type:"default",class:"status-tag disabled"},{default:E(()=>p[5]||(p[5]=[$(" 未启用 ")])),_:1}))]),action:E(()=>[S(D(wu),{class:"action-buttons"},{default:E(()=>[S(D(ru),{onClick:ue(J=>_(F),["stop"]),size:"small",class:"edit-button"},{default:E(()=>p[8]||(p[8]=[$(" 编辑 ")])),_:2},1032,["onClick"]),S(D(le),{onPositiveClick:J=>g(F.name),"positive-text":"确定","negative-text":"取消",class:"delete-confirm"},{trigger:E(()=>[S(D(ru),{size:"small",class:"delete-button"},{default:E(()=>p[9]||(p[9]=[$(" 删除 ")])),_:1})]),default:E(()=>[p[10]||(p[10]=$(" 确定要删除配置吗？ "))]),_:2},1032,["onPositiveClick"])]),_:2},1024)]),_:2},1032,["title","description"])]),_:2},1032,["onClick","class"])}),128))]),_:1})])]),L("div",sn,[L("div",ln,[S(D(wu),{justify:"space-between",align:"center"},{default:E(()=>[p[12]||(p[12]=L("h3",{class:"panel-title"}," 配置详情 ",-1)),l.value?(M(),P(D(ru),{key:0,type:"primary",size:"small",onClick:m,loading:i.value,class:"save-button"},{icon:E(()=>[S(D(mu),null,{default:E(()=>[S(D(ie))]),_:1})]),default:E(()=>[p[11]||(p[11]=$(" 保存配置 "))]),_:1},8,["loading"])):eu("",!0)]),_:1})]),l.value?(M(),su("div",fn,[S(D(oe),{ref_key:"formRef",ref:d,model:l.value,"label-placement":"left","label-width":"150",rules:T,class:"form"},{default:E(()=>[S(D(Yu),{label:"名称",path:"name",class:"form-item"},{feedback:E(()=>[S(D(X0),{depth:"3",class:"form-hint"},{default:E(()=>p[13]||(p[13]=[$("用于区分不同的配置，必须唯一")])),_:1})]),default:E(()=>[l.value?(M(),P(D(fe),{key:0,value:l.value.name,"onUpdate:value":p[0]||(p[0]=F=>l.value.name=F),placeholder:"配置名称",class:"input"},null,8,["value"])):eu("",!0)]),_:1}),S(D(Yu),{label:"开启",class:"form-item"},{default:E(()=>[l.value?(M(),P(D(de),{key:0,value:l.value.enable,"onUpdate:value":p[1]||(p[1]=F=>l.value.enable=F),class:"switch"},null,8,["value"])):eu("",!0)]),_:1}),S(D(ae),{class:"divider"}),a.value&&l.value?(M(),su("div",dn,[S(re,{schema:a.value,modelValue:l.value.config,"onUpdate:modelValue":p[2]||(p[2]=F=>l.value.config=F)},null,8,["schema","modelValue"])])):eu("",!0)]),_:1},8,["model"])])):(M(),su("div",hn,[S(D(Ku),{description:"请选择或添加一个配置",class:"empty-state"},Y0({_:2},[s.value.length==0?{name:"extra",fn:E(()=>[S(D(ru),{type:"primary",onClick:x,class:"add-button-large"},{icon:E(()=>[S(D(mu),null,{default:E(()=>[S(D(Xu))]),_:1})]),default:E(()=>[p[14]||(p[14]=$(" 添加配置 "))]),_:1})]),key:"0"}:void 0]),1024)]))])])]}),_:1})]),_:1},8,["show"])]))}});const qn=ee(bn,[["__scopeId","data-v-748e9d26"]]);export{qn as default};
