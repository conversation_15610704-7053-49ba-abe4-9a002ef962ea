import{h as w,y as T,r as c,d as V,u as I,e as P,c as _,a as e,t as b,j as o,k as l,i,o as m,g as B,C as h,N as C,m as L,a5 as M,_ as S}from"./index.js";import{a as k,N as A}from"./FormItem.js";import{N as y}from"./Input.js";import"./cryptojs.js";import"./use-locale.js";import"./en-US.js";const N={checkFirstTime(){return w.get("/auth/check-first-time")},login(u){return w.post("/auth/login",{password:u})}};function q(){const u=T(),a=c(!1),n=c(!1),t=c({password:"",confirmPassword:""});return{isFirstTime:a,loading:n,formModel:t,rules:{password:[{required:!0,message:"请输入密码"},{min:6,message:"密码长度不能小于6位"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:["input","blur"]},{validator:(r,d)=>d===t.value.password,message:"两次输入的密码不一致",trigger:["input","blur"]}]},handleSubmit:async()=>{if(!(a.value&&t.value.password!==t.value.confirmPassword)){n.value=!0;try{const{access_token:r}=await N.login(t.value.password);localStorage.setItem("token",r),u.push("/")}finally{n.value=!1}}},checkFirstTime:async()=>{try{const{is_first_time:r}=await N.checkFirstTime();a.value=r}catch(r){r instanceof Error&&r.message==="No password set"&&(a.value=!0)}}}}const K={class:"login-view"},z={class:"login-container"},E={class:"login-form-section"},R={class:"login-form-wrapper"},U={class:"login-header"},j={class:"login-subtitle"},D={class:"form-actions"},G={key:0,class:"forgot-password"},H=V({__name:"LoginView",setup(u){const{isFirstTime:a,loading:n,formModel:t,rules:g,handleSubmit:f,checkFirstTime:v}=q(),r=I(),d=c(void 0),x=async()=>{try{d.value=void 0,await f()}catch{d.value="error",r.error("登录失败，请重试")}};return P(()=>{v()}),(F,s)=>(m(),_("div",K,[s[10]||(s[10]=e("div",{class:"login-bg-layer"},null,-1)),e("div",z,[s[9]||(s[9]=e("div",{class:"login-image-section"},[e("div",{class:"login-image-content"},[e("h1",{class:"brand-title"},"Kirara AI"),e("p",{class:"brand-slogan"},"探索人工智能的无限可能")])],-1)),e("div",E,[e("div",R,[e("div",U,[s[2]||(s[2]=e("div",{class:"login-logo"},[e("div",{class:"i-carbon-bot text-36px animate-float"})],-1)),s[3]||(s[3]=e("h2",{class:"login-title"},"欢迎使用 Kirara AI",-1)),e("p",j,b(o(a)?"首次使用，请设置管理员密码":"请输入管理员密码继续"),1)]),l(o(A),{ref:"formRef",model:o(t),rules:o(g),"label-placement":"left","label-width":"0","require-mark-placement":"right-hanging",size:"large",class:"login-form"},{default:i(()=>[l(o(k),{path:"password",class:"form-item"},{default:i(()=>[l(o(y),{value:o(t).password,"onUpdate:value":s[0]||(s[0]=p=>o(t).password=p),type:"password",placeholder:"请输入密码","show-password-on":"click",status:d.value,class:"password-input"},{prefix:i(()=>s[4]||(s[4]=[e("div",{class:"i-carbon-password"},null,-1)])),_:1},8,["value","status"])]),_:1}),o(a)?(m(),B(o(k),{key:0,path:"confirmPassword",class:"form-item"},{default:i(()=>[l(o(y),{value:o(t).confirmPassword,"onUpdate:value":s[1]||(s[1]=p=>o(t).confirmPassword=p),type:"password",placeholder:"请确认密码",maxlength:32,"show-password-on":"click",class:"password-input"},{prefix:i(()=>s[5]||(s[5]=[e("div",{class:"i-carbon-password-confirmation"},null,-1)])),_:1},8,["value"])]),_:1})):h("",!0),e("div",D,[l(o(C),{type:"primary",size:"large",block:"",loading:o(n),onClick:x,class:"login-button"},{default:i(()=>[L(b(o(a)?"设置密码":"登录")+" ",1),s[6]||(s[6]=e("div",{class:"button-effect"},null,-1))]),_:1},8,["loading"]),o(a)?h("",!0):(m(),_("div",G,[l(o(M),{trigger:"hover",placement:"bottom"},{trigger:i(()=>s[7]||(s[7]=[e("span",{class:"forgot-password-text"}," 忘记密码？ ",-1)])),default:i(()=>[s[8]||(s[8]=e("span",null,"删除项目下的 data\\web\\password.hash 文件即可重置密码",-1))]),_:1})]))])]),_:1},8,["model","rules"])])])]),s[11]||(s[11]=e("div",{class:"login-footer"},[e("a",{href:"https://github.com/lss233/kirara-ai",target:"_blank",class:"footer-link"},[e("span",null,"Powered by Kirara AI")])],-1))]))}});const Z=S(H,[["__scopeId","data-v-823579f0"]]);export{Z as default};
