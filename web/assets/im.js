import{h as e}from"./index.js";const p={getAdapterTypes(){return e.get("/im/types")},getAdapters(){return e.get("/im/adapters")},getAdapter(t){return e.get(`/im/adapters/${t}`)},getAdapterDetail(t){return e.get(`/im/adapters/${t}`)},createAdapter(t){return e.post("/im/adapters",t)},updateAdapter(t,r){return e.put(`/im/adapters/${t}`,r)},deleteAdapter(t){return e.delete(`/im/adapters/${t}`)},startAdapter(t){return e.post(`/im/adapters/${t}/start`)},stopAdapter(t){return e.post(`/im/adapters/${t}/stop`)},getAdapterConfigSchema(t){return e.get(`/im/types/${t}/config-schema`)}};export{p as i};
