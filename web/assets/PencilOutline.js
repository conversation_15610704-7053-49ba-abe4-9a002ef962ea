import{d as o,o as n,c as t,a as e}from"./index.js";const l={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},r=e("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M364.13 125.25L87 403l-23 45l44.99-23l277.76-277.13l-22.62-22.62z"},null,-1),s=e("path",{d:"M420.69 68.69l-22.62 22.62l22.62 22.63l22.62-22.63a16 16 0 0 0 0-22.62h0a16 16 0 0 0-22.62 0z",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),i=[r,s],k=o({name:"PencilOutline",render:function(a,d){return n(),t("svg",l,i)}});export{k as P};
