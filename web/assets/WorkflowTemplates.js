import{N as l}from"./Result.js";import{d as p,r as c,e as n,c as d,k as e,i as a,j as s,H as m,o as u,a as b,p as f,N as r,m as i,_ as g}from"./index.js";import"./cryptojs.js";const _={class:"container"},h=p({__name:"WorkflowTemplates",setup(k){const o=c(!1);return n(()=>{o.value=!0}),(v,t)=>(u(),d("div",_,[e(s(l),{status:"404",title:o.value?"努力 строительстве 中... 喵~":"迷路了吗？",description:"这个功能还在小猫咪的秘密工坊里，加班加点 строительстве 呢... 🚀"},{icon:a(()=>t[0]||(t[0]=[b("div",{class:"text-8xl animated-icon"},"🚧",-1)])),footer:a(()=>[e(s(f),{style:{width:"100%","justify-content":"center"}},{default:a(()=>[e(s(r),{tag:"a",href:"https://github.com/DarkSkyTeam/chatgpt-for-bot-webui",target:"_blank",type:"primary"},{default:a(()=>t[1]||(t[1]=[i(" 去 GitHub 看看 ")])),_:1}),e(s(r),{tag:"a",href:"https://kirara-docs.app.lss233.com/",target:"_blank",type:"primary"},{default:a(()=>t[2]||(t[2]=[i(" 去文档看看 ")])),_:1})]),_:1})]),_:1},8,["title"]),t[3]||(t[3]=m('<div class="badges" data-v-b228ec52><a href="https://github.com/DarkSkyTeam/chatgpt-for-bot-webui" target="_blank" data-v-b228ec52><img src="https://img.shields.io/github/stars/DarkSkyTeam/chatgpt-for-bot-webui?style=flat&amp;color=green" alt="GitHub Stars" data-v-b228ec52></a><a href="https://github.com/lss233/chatgpt-mirai-qq-bot" target="_blank" data-v-b228ec52><img src="https://img.shields.io/github/stars/lss233/chatgpt-mirai-qq-bot?style=flat&amp;color=blue" alt="GitHub Stars" data-v-b228ec52></a></div>',1))]))}});const x=g(h,[["__scopeId","data-v-b228ec52"]]);export{x as default};
