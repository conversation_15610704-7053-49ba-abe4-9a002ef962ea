import{e as B}from"./en-US.js";function l(e){const t=Object.prototype.toString.call(e);return e instanceof Date||typeof e=="object"&&t==="[object Date]"?new e.constructor(+e):typeof e=="number"||t==="[object Number]"||typeof e=="string"||t==="[object String]"?new Date(e):new Date(NaN)}let X={};function M(){return X}function k(e,t){var f,o,m,h;const n=M(),r=(t==null?void 0:t.weekStartsOn)??((o=(f=t==null?void 0:t.locale)==null?void 0:f.options)==null?void 0:o.weekStartsOn)??n.weekStartsOn??((h=(m=n.locale)==null?void 0:m.options)==null?void 0:h.weekStartsOn)??0,a=l(e),s=a.getDay(),d=(s<r?7:0)+s-r;return a.setDate(a.getDate()-d),a.setHours(0,0,0,0),a}function y(e,t){return e instanceof Date?new e.constructor(t):new Date(t)}const G=6048e5,I=864e5,mt=6e4,gt=36e5,wt=1e3;function Y(e){return k(e,{weekStartsOn:1})}function Q(e){const t=l(e),n=t.getFullYear(),r=y(e,0);r.setFullYear(n+1,0,4),r.setHours(0,0,0,0);const a=Y(r),s=y(e,0);s.setFullYear(n,0,4),s.setHours(0,0,0,0);const d=Y(s);return t.getTime()>=a.getTime()?n+1:t.getTime()>=d.getTime()?n:n-1}function q(e){const t=l(e);return t.setHours(0,0,0,0),t}function N(e){const t=l(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),+e-+n}function R(e,t){const n=q(e),r=q(t),a=+n-N(n),s=+r-N(r);return Math.round((a-s)/I)}function p(e){const t=Q(e),n=y(e,0);return n.setFullYear(t,0,4),n.setHours(0,0,0,0),Y(n)}function j(e){return e instanceof Date||typeof e=="object"&&Object.prototype.toString.call(e)==="[object Date]"}function $(e){if(!j(e)&&typeof e!="number")return!1;const t=l(e);return!isNaN(Number(t))}function U(e){const t=l(e),n=y(e,0);return n.setFullYear(t.getFullYear(),0,1),n.setHours(0,0,0,0),n}function V(e){const t=l(e);return R(t,U(t))+1}function A(e){const t=l(e),n=+Y(t)-+p(t);return Math.round(n/G)+1}function _(e,t){var h,O,x,D;const n=l(e),r=n.getFullYear(),a=M(),s=(t==null?void 0:t.firstWeekContainsDate)??((O=(h=t==null?void 0:t.locale)==null?void 0:h.options)==null?void 0:O.firstWeekContainsDate)??a.firstWeekContainsDate??((D=(x=a.locale)==null?void 0:x.options)==null?void 0:D.firstWeekContainsDate)??1,d=y(e,0);d.setFullYear(r+1,0,s),d.setHours(0,0,0,0);const f=k(d,t),o=y(e,0);o.setFullYear(r,0,s),o.setHours(0,0,0,0);const m=k(o,t);return n.getTime()>=f.getTime()?r+1:n.getTime()>=m.getTime()?r:r-1}function J(e,t){var f,o,m,h;const n=M(),r=(t==null?void 0:t.firstWeekContainsDate)??((o=(f=t==null?void 0:t.locale)==null?void 0:f.options)==null?void 0:o.firstWeekContainsDate)??n.firstWeekContainsDate??((h=(m=n.locale)==null?void 0:m.options)==null?void 0:h.firstWeekContainsDate)??1,a=_(e,t),s=y(e,0);return s.setFullYear(a,0,r),s.setHours(0,0,0,0),k(s,t)}function K(e,t){const n=l(e),r=+k(n,t)-+J(n,t);return Math.round(r/G)+1}function c(e,t){const n=e<0?"-":"",r=Math.abs(e).toString().padStart(t,"0");return n+r}const g={y(e,t){const n=e.getFullYear(),r=n>0?n:1-n;return c(t==="yy"?r%100:r,t.length)},M(e,t){const n=e.getMonth();return t==="M"?String(n+1):c(n+1,2)},d(e,t){return c(e.getDate(),t.length)},a(e,t){const n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];case"aaaa":default:return n==="am"?"a.m.":"p.m."}},h(e,t){return c(e.getHours()%12||12,t.length)},H(e,t){return c(e.getHours(),t.length)},m(e,t){return c(e.getMinutes(),t.length)},s(e,t){return c(e.getSeconds(),t.length)},S(e,t){const n=t.length,r=e.getMilliseconds(),a=Math.trunc(r*Math.pow(10,n-3));return c(a,t.length)}},b={am:"am",pm:"pm",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},F={G:function(e,t,n){const r=e.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});case"GGGG":default:return n.era(r,{width:"wide"})}},y:function(e,t,n){if(t==="yo"){const r=e.getFullYear(),a=r>0?r:1-r;return n.ordinalNumber(a,{unit:"year"})}return g.y(e,t)},Y:function(e,t,n,r){const a=_(e,r),s=a>0?a:1-a;if(t==="YY"){const d=s%100;return c(d,2)}return t==="Yo"?n.ordinalNumber(s,{unit:"year"}):c(s,t.length)},R:function(e,t){const n=Q(e);return c(n,t.length)},u:function(e,t){const n=e.getFullYear();return c(n,t.length)},Q:function(e,t,n){const r=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return c(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(e,t,n){const r=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return c(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(e,t,n){const r=e.getMonth();switch(t){case"M":case"MM":return g.M(e,t);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(e,t,n){const r=e.getMonth();switch(t){case"L":return String(r+1);case"LL":return c(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(e,t,n,r){const a=K(e,r);return t==="wo"?n.ordinalNumber(a,{unit:"week"}):c(a,t.length)},I:function(e,t,n){const r=A(e);return t==="Io"?n.ordinalNumber(r,{unit:"week"}):c(r,t.length)},d:function(e,t,n){return t==="do"?n.ordinalNumber(e.getDate(),{unit:"date"}):g.d(e,t)},D:function(e,t,n){const r=V(e);return t==="Do"?n.ordinalNumber(r,{unit:"dayOfYear"}):c(r,t.length)},E:function(e,t,n){const r=e.getDay();switch(t){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});case"EEEE":default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(e,t,n,r){const a=e.getDay(),s=(a-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(s);case"ee":return c(s,2);case"eo":return n.ordinalNumber(s,{unit:"day"});case"eee":return n.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(a,{width:"short",context:"formatting"});case"eeee":default:return n.day(a,{width:"wide",context:"formatting"})}},c:function(e,t,n,r){const a=e.getDay(),s=(a-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(s);case"cc":return c(s,t.length);case"co":return n.ordinalNumber(s,{unit:"day"});case"ccc":return n.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(a,{width:"narrow",context:"standalone"});case"cccccc":return n.day(a,{width:"short",context:"standalone"});case"cccc":default:return n.day(a,{width:"wide",context:"standalone"})}},i:function(e,t,n){const r=e.getDay(),a=r===0?7:r;switch(t){case"i":return String(a);case"ii":return c(a,t.length);case"io":return n.ordinalNumber(a,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});case"iiii":default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(e,t,n){const a=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(a,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(e,t,n){const r=e.getHours();let a;switch(r===12?a=b.noon:r===0?a=b.midnight:a=r/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(a,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(e,t,n){const r=e.getHours();let a;switch(r>=17?a=b.evening:r>=12?a=b.afternoon:r>=4?a=b.morning:a=b.night,t){case"B":case"BB":case"BBB":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(a,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(e,t,n){if(t==="ho"){let r=e.getHours()%12;return r===0&&(r=12),n.ordinalNumber(r,{unit:"hour"})}return g.h(e,t)},H:function(e,t,n){return t==="Ho"?n.ordinalNumber(e.getHours(),{unit:"hour"}):g.H(e,t)},K:function(e,t,n){const r=e.getHours()%12;return t==="Ko"?n.ordinalNumber(r,{unit:"hour"}):c(r,t.length)},k:function(e,t,n){let r=e.getHours();return r===0&&(r=24),t==="ko"?n.ordinalNumber(r,{unit:"hour"}):c(r,t.length)},m:function(e,t,n){return t==="mo"?n.ordinalNumber(e.getMinutes(),{unit:"minute"}):g.m(e,t)},s:function(e,t,n){return t==="so"?n.ordinalNumber(e.getSeconds(),{unit:"second"}):g.s(e,t)},S:function(e,t){return g.S(e,t)},X:function(e,t,n){const r=e.getTimezoneOffset();if(r===0)return"Z";switch(t){case"X":return C(r);case"XXXX":case"XX":return w(r);case"XXXXX":case"XXX":default:return w(r,":")}},x:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"x":return C(r);case"xxxx":case"xx":return w(r);case"xxxxx":case"xxx":default:return w(r,":")}},O:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+H(r,":");case"OOOO":default:return"GMT"+w(r,":")}},z:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+H(r,":");case"zzzz":default:return"GMT"+w(r,":")}},t:function(e,t,n){const r=Math.trunc(e.getTime()/1e3);return c(r,t.length)},T:function(e,t,n){const r=e.getTime();return c(r,t.length)}};function H(e,t=""){const n=e>0?"-":"+",r=Math.abs(e),a=Math.trunc(r/60),s=r%60;return s===0?n+String(a):n+String(a)+t+c(s,2)}function C(e,t){return e%60===0?(e>0?"-":"+")+c(Math.abs(e)/60,2):w(e,t)}function w(e,t=""){const n=e>0?"-":"+",r=Math.abs(e),a=c(Math.trunc(r/60),2),s=c(r%60,2);return n+a+t+s}const v=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});case"PPPP":default:return t.date({width:"full"})}},L=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});case"pppp":default:return t.time({width:"full"})}},Z=(e,t)=>{const n=e.match(/(P+)(p+)?/)||[],r=n[1],a=n[2];if(!a)return v(e,t);let s;switch(r){case"P":s=t.dateTime({width:"short"});break;case"PP":s=t.dateTime({width:"medium"});break;case"PPP":s=t.dateTime({width:"long"});break;case"PPPP":default:s=t.dateTime({width:"full"});break}return s.replace("{{date}}",v(r,t)).replace("{{time}}",L(a,t))},z={p:L,P:Z},tt=/^D+$/,et=/^Y+$/,nt=["D","DD","YY","YYYY"];function rt(e){return tt.test(e)}function at(e){return et.test(e)}function st(e,t,n){const r=ct(e,t,n);if(console.warn(r),nt.includes(e))throw new RangeError(r)}function ct(e,t,n){const r=e[0]==="Y"?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${r} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const it=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,ot=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,ut=/^'([^]*?)'?$/,dt=/''/g,ft=/[a-zA-Z]/;function yt(e,t,n){var h,O,x,D,P,S,W,E;const r=M(),a=(n==null?void 0:n.locale)??r.locale??B,s=(n==null?void 0:n.firstWeekContainsDate)??((O=(h=n==null?void 0:n.locale)==null?void 0:h.options)==null?void 0:O.firstWeekContainsDate)??r.firstWeekContainsDate??((D=(x=r.locale)==null?void 0:x.options)==null?void 0:D.firstWeekContainsDate)??1,d=(n==null?void 0:n.weekStartsOn)??((S=(P=n==null?void 0:n.locale)==null?void 0:P.options)==null?void 0:S.weekStartsOn)??r.weekStartsOn??((E=(W=r.locale)==null?void 0:W.options)==null?void 0:E.weekStartsOn)??0,f=l(e);if(!$(f))throw new RangeError("Invalid time value");let o=t.match(ot).map(u=>{const i=u[0];if(i==="p"||i==="P"){const T=z[i];return T(u,a.formatLong)}return u}).join("").match(it).map(u=>{if(u==="''")return{isToken:!1,value:"'"};const i=u[0];if(i==="'")return{isToken:!1,value:ht(u)};if(F[i])return{isToken:!0,value:u};if(i.match(ft))throw new RangeError("Format string contains an unescaped latin alphabet character `"+i+"`");return{isToken:!1,value:u}});a.localize.preprocessor&&(o=a.localize.preprocessor(f,o));const m={firstWeekContainsDate:s,weekStartsOn:d,locale:a};return o.map(u=>{if(!u.isToken)return u.value;const i=u.value;(!(n!=null&&n.useAdditionalWeekYearTokens)&&at(i)||!(n!=null&&n.useAdditionalDayOfYearTokens)&&rt(i))&&st(i,t,String(e));const T=F[i[0]];return T(f,i,a.localize,m)}).join("")}function ht(e){const t=e.match(ut);return t?t[1].replace(dt,"'"):e}export{q as a,mt as b,y as c,wt as d,_ as e,Y as f,M as g,K as h,A as i,N as j,at as k,z as l,gt as m,rt as n,$ as o,yt as p,U as q,k as s,l as t,st as w};
