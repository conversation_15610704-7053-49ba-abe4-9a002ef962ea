var R=Object.defineProperty;var E=(n,t,a)=>t in n?R(n,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):n[t]=a;var x=(n,t,a)=>(E(n,typeof t!="symbol"?t+"":t,a),a);import{y as H,B as j,u as U,r as o,b as O,h as _,O as C,A as G,N as K}from"./index.js";import{u as Q}from"./composables.js";import{p as V}from"./format.js";function X(n="llm",t){const a=H(),v=j(),i=U();Q();const m=o([]),S=o(null),y=o(null),f=o(!1),k=o(!1),T=o(0),c=o(1),g=o(20),F=o(1);let r=null,u=null,b=0;const q=5,I=3e3,s=o({modelId:null,backendName:null,status:null,query:""}),L=O(()=>t.getFilterOptions()),W=O(()=>S.value?t.formatStatistics(S.value):[]),D=()=>`/tracing/${n}`,d=async()=>{try{k.value=!0;const e=await _.post(`${D()}/traces`,{page:c.value,page_size:g.value,model_id:s.value.modelId,backend_name:s.value.backendName,status:s.value.status,query:s.value.query||void 0});m.value=e.items,T.value=e.total,F.value=e.total_pages}catch(e){i.error("获取追踪记录失败"),console.error("获取追踪记录失败:",e)}finally{k.value=!1}},h=async()=>{try{const e=await _.get(`${D()}/statistics`);S.value=e,t.updateFilterOptions&&t.updateFilterOptions(e)}catch(e){console.error("获取统计信息失败:",e)}},$=async e=>{try{k.value=!0;const l=await _.get(`${D()}/detail/${e}`);return y.value=l,l}catch(l){return i.error("获取追踪详情失败"),console.error("获取追踪详情失败:",l),null}finally{k.value=!1}},w=async()=>{try{const e=_.url("/tracing/ws");r&&r.readyState!==WebSocket.CLOSED&&r.close(),r=new WebSocket(e),r.onopen=()=>{r.send(JSON.stringify({token:localStorage.getItem("token")||""})),r.send(JSON.stringify({action:"subscribe",tracer_type:n})),f.value=!0,i.success("已连接到追踪系统"),b=0,u!==null&&(clearTimeout(u),u=null)},r.onmessage=l=>{try{const p=JSON.parse(l.data);P(p)}catch(p){console.error("处理WebSocket消息失败:",p)}},r.onclose=B,r.onerror=M}catch(e){console.error("连接WebSocket失败:",e),f.value=!1,i.error("连接追踪系统失败")}},P=e=>{var l;(e.type==="new"||e.type==="update")&&(c.value===1&&!z()&&A(e),((l=y.value)==null?void 0:l.trace_id)===e.data.trace_id&&(y.value=e.data),h())},z=()=>s.value.modelId||s.value.backendName||s.value.status||s.value.query,A=e=>{const l=e.data;if(e.type==="new")m.value=[l,...m.value].slice(0,g.value),T.value+=1;else{const p=m.value.findIndex(J=>J.trace_id===l.trace_id);p!==-1&&(m.value[p]=l)}},B=e=>{f.value=!1,!e.wasClean&&b<q?u=window.setTimeout(()=>{b++,w()},I):b>=q&&i.error("重连次数已达上限，请手动刷新页面重试")},M=e=>{console.error("WebSocket错误:",e),f.value=!1,i.error("连接追踪系统失败")},N=()=>{u!==null&&(clearTimeout(u),u=null),r&&(r.onclose=null,r.close(),r=null,f.value=!1)};return{traces:m,formattedStatistics:W,traceDetail:y,isConnected:f,isLoading:k,totalTraces:T,currentPage:c,pageSize:g,totalPages:F,filterParams:s,filterOptions:L,fetchTraces:d,fetchStatistics:h,getTraceDetail:$,viewTraceDetail:e=>{a.push(`/tracing/${n}/detail/${e}`)},goBackToList:()=>{a.push(`/tracing/${n}`)},connectWebSocket:w,disconnectWebSocket:N,resetFilter:()=>{s.value={modelId:null,backendName:null,status:null,query:""},c.value=1,d()},applyFilter:()=>{c.value=1,d()},handlePageChange:e=>{c.value=e,d()},handlePageSizeChange:e=>{g.value=e,c.value=1,d()},refreshData:()=>{N(),d(),h(),w()},initialize:async()=>{const e=v.query;e.model&&(s.value.modelId=e.model),e.backend&&(s.value.backendName=e.backend),await d(),await h(),w()},formatDate:e=>{if(!e)return"---";try{const l=typeof e=="string"?new Date(e):e;return V(l,"yyyy-MM-dd HH:mm:ss")}catch{return String(e)}},formatDuration:e=>e===null?"未知":e<1e3?`${e.toFixed(2)} 毫秒`:`${(e/1e3).toFixed(2)} 秒`,formatTokens:e=>e==null?"未知":e.toLocaleString()}}class Y{constructor(){x(this,"filterOptions",o({modelId:[],backendName:[],status:[{label:"请求中",value:"pending"},{label:"成功",value:"success"},{label:"失败",value:"failed"}]}))}getFilterOptions(){return this.filterOptions.value}updateFilterOptions(t){this.filterOptions.value={...this.filterOptions.value,modelId:t.models.map(a=>({label:a.model_id,value:a.model_id})),backendName:t.backends.map(a=>({label:a.backend_name,value:a.backend_name}))},console.log(this.filterOptions.value)}getTableColumns(t){return[{title:"ID",key:"trace_id",width:120,ellipsis:{tooltip:!0}},{title:"模型",key:"model_id",width:160},{title:"后端",key:"backend_name",width:120},{title:"请求时间",key:"request_time",width:180,render:a=>t.formatDate(a.request_time)},{title:"状态",key:"status",width:100,render:a=>{const v={pending:{type:"warning",text:"请求中"},success:{type:"success",text:"成功"},failed:{type:"error",text:"失败"}},i=v[a.status]||v.pending;return C(G,{type:i.type,size:"small"},{default:()=>i.text})}},{title:"耗时",key:"duration",width:100,render:a=>t.formatDuration(a.duration)},{title:"Tokens",key:"total_tokens",width:100,render:a=>t.formatTokens(a.total_tokens)},{title:"操作",key:"actions",width:100,render:a=>C(K,{text:!0,type:"primary",onClick:()=>t.viewTraceDetail(a.trace_id)},{default:()=>"查看详情"})}]}formatStatistics(t){return[{label:"总请求数",value:t.overview.total_requests},{label:"请求中",value:t.overview.pending_requests,type:"warning"},{label:"成功请求",value:t.overview.success_requests,type:"success"},{label:"失败请求",value:t.overview.failed_requests,type:"error"},{label:"总Token数",value:t.overview.total_tokens,type:"info"}]}getDetailFields(){return[{label:"模型",key:"model_id"},{label:"后端",key:"backend_name"},{label:"提示Token",key:"prompt_tokens"},{label:"补全Token",key:"completion_tokens"},{label:"总Token",key:"total_tokens"},{label:"缓存Token",key:"cached_tokens"}]}}function fe(){const n=new Y,t=X("llm",n),a=n.getFilterOptions().status,v=O(()=>n.getTableColumns(t));return{...t,statusOptions:a,columns:v}}export{fe as u};
