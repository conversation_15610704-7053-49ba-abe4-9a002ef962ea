function c(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function l(e){if(e.__esModule)return e;var n=e.default;if(typeof n=="function"){var r=function o(){if(this instanceof o){var t=[null];t.push.apply(t,arguments);var u=Function.bind.apply(n,t);return new u}return n.apply(this,arguments)};r.prototype=n.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach(function(o){var t=Object.getOwnPropertyDescriptor(e,o);Object.defineProperty(r,o,t.get?t:{enumerable:!0,get:function(){return e[o]}})}),r}const a={},p=Object.freeze(Object.defineProperty({__proto__:null,default:a},Symbol.toStringTag,{value:"Module"})),f=l(p);export{c as g,f as r};
