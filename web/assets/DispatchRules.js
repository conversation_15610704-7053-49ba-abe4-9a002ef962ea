import{d as A,o as h,c as R,a as d,h as y,u as ce,r as c,b as pe,e as ve,k as u,i as n,j as o,s as me,N as g,m as b,a6 as H,F as L,q as O,l as U,g as ge,C as fe,p as P,O as _,a5 as ye,_ as _e}from"./index.js";import{l as we}from"./workflow.js";import{N as he,D as ke}from"./DynamicConfigForm.js";import{H as be}from"./HelpCircleOutline.js";import{P as Re}from"./PencilOutline.js";import{N as Ce}from"./DataTable.js";import{N as Ne,a as $}from"./FormItem.js";import{N as z}from"./Input.js";import{N as G}from"./Select.js";import{a as xe,N as I}from"./Switch.js";import{N as Ue}from"./Divider.js";import"./cryptojs.js";import"./AddOutline.js";import"./Empty.js";import"./use-locale.js";import"./en-US.js";import"./Pagination.js";import"./Checkmark.js";const $e={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},De=d("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M256 112v288"},null,-1),Te=d("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M400 256H112"},null,-1),Se=[De,Te],W=A({name:"Add",render:function(a,p){return h(),R("svg",$e,Se)}}),Ve={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Me=d("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M400 256H112"},null,-1),qe=[Me],Be=A({name:"Remove",render:function(a,p){return h(),R("svg",Ve,qe)}}),w={getRuleTypes:()=>y.get("/dispatch/types"),getRules:()=>y.get("/dispatch/rules"),getRuleConfigSchema:l=>y.get(`/dispatch/types/${l}/config-schema`),createRule:l=>y.post("/dispatch/rules",l),updateRule:(l,a)=>y.put(`/dispatch/rules/${l}`,a),deleteRule:l=>y.delete(`/dispatch/rules/${l}`),enableRule:l=>y.post(`/dispatch/rules/${l}/enable`),disableRule:l=>y.post(`/dispatch/rules/${l}/disable`)},Le={prefix:"以……开头",regex:"正则表达式",keyword:"包含……词",random:"以……概率",sender:"发送者为……",sender_mismatch:"发送者不为……",bot_mention:"被@",chat_type:"聊天类型",fallback:"任意输入"},K=l=>Le[l]||l,i=[];for(let l=0;l<256;++l)i.push((l+256).toString(16).slice(1));function je(l,a=0){return(i[l[a+0]]+i[l[a+1]]+i[l[a+2]]+i[l[a+3]]+"-"+i[l[a+4]]+i[l[a+5]]+"-"+i[l[a+6]]+i[l[a+7]]+"-"+i[l[a+8]]+i[l[a+9]]+"-"+i[l[a+10]]+i[l[a+11]]+i[l[a+12]]+i[l[a+13]]+i[l[a+14]]+i[l[a+15]]).toLowerCase()}let j;const Ae=new Uint8Array(16);function Ee(){if(!j){if(typeof crypto>"u"||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");j=crypto.getRandomValues.bind(crypto)}return j(Ae)}const Fe=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),J={randomUUID:Fe};function He(l,a,p){var f;if(J.randomUUID&&!a&&!l)return J.randomUUID();l=l||{};const m=l.random??((f=l.rng)==null?void 0:f.call(l))??Ee();if(m.length<16)throw new Error("Random bytes length must be >= 16");if(m[6]=m[6]&15|64,m[8]=m[8]&63|128,a){if(p=p||0,p<0||p+16>a.length)throw new RangeError(`UUID byte range ${p}:${p+15} is out of buffer bounds`);for(let s=0;s<16;++s)a[p+s]=m[s];return a}return je(m)}const Oe={class:"dispatch-rules"},Pe={class:"rule-edit-container"},ze={class:"rule-basic-form"},Ge={class:"rule-config-form"},Ie={class:"config-form-container"},We={class:"rule-groups"},Ke={class:"rule-list"},Je={class:"rule-item"},Qe=A({__name:"DispatchRules",setup(l){const a=ce(),p=c([]),m=c([]),f=c(!1),s=c({rule_id:"",name:"",description:"",workflow_id:"",priority:5,enabled:!0,rule_groups:[{operator:"or",rules:[]}],metadata:{}}),M=c(null),D=c(!1),q=c([]),B=c(""),k=c(-1),N=c(-1),x=c(!1),Q=[{title:"名称",key:"name"},{title:"描述",key:"description"},{title:"工作流",key:"workflow_id",render:t=>{const e=q.value.find(r=>`${r.group_id}:${r.workflow_id}`===t.workflow_id);return e?`${e.name} (${t.workflow_id})  `:"未指定"}},{title:()=>_(ye,{trigger:"hover",placement:"top"},{trigger:()=>_("div",{style:{display:"flex",alignItems:"center"}},{default:()=>["优先级",_(U,{},{default:()=>_(be)})]}),default:()=>"优先级定义了规则判定的顺序，数值越大优先级越高，会优先被判断。建议根据业务需求合理设置优先级。"}),key:"priority"},{title:"状态",key:"enabled",render:t=>_(I,{value:t.enabled,onUpdateValue:async e=>{try{e?await w.enableRule(t.rule_id):await w.disableRule(t.rule_id),await T(),a.success("操作成功")}catch{a.error("操作失败")}}})},{title:"操作",key:"actions",render:t=>_(P,null,{default:()=>[_(g,{size:"small",onClick:()=>le(t)},{default:()=>"编辑"}),_(g,{size:"small",type:"error",onClick:()=>te(t.rule_id)},{default:()=>"删除"})]})}],E=c(null),X=c({name:[{required:!0,message:"请输入规则名称"},{min:1,max:100,message:"规则名称长度应在1-100个字符之间"}],workflow_id:[{required:!0,message:"请选择工作流"}],priority:[{required:!0,message:"请输入优先级"},{type:"number",min:0,max:100,message:"优先级应在0-100之间"}],enabled:[{required:!0,message:"请选择启用状态"}]}),T=async()=>{try{const{rules:t}=await w.getRules();p.value=t}catch{a.error("加载规则列表失败")}},Y=async()=>{try{const{workflows:t}=await we();q.value=t}catch{a.error("加载工作流列表失败")}},Z=async()=>{try{const{types:t}=await w.getRuleTypes();m.value=t}catch{a.error("加载规则类型失败")}},F=async t=>{try{const{configSchema:e}=await w.getRuleConfigSchema(t);M.value=e}catch{a.error("加载配置模式失败")}},ee=()=>{D.value=!0,s.value={rule_id:He(),priority:5,enabled:!0,name:"",description:"",workflow_id:"",rule_groups:[{operator:"or",rules:[]}],metadata:{}},f.value=!0},le=t=>{D.value=!1,s.value={...t},f.value=!0},te=async t=>{try{await w.deleteRule(t),await T(),a.success("删除成功")}catch{a.error("删除失败")}},ae=async t=>{var e,r;try{const v=await((e=E.value)==null?void 0:e.validate());if((r=v==null?void 0:v.warnings)!=null&&r.length){a.error("请检查输入内容");return}t?await w.createRule(s.value):await w.updateRule(s.value.rule_id,s.value),await T(),f.value=!1,a.success("保存成功")}catch(v){a.error("保存失败:"+v.message)}},re=()=>{s.value.rule_groups||(s.value.rule_groups=[]),s.value.rule_groups.push({operator:"or",rules:[]})},oe=t=>{k.value=t,N.value=s.value.rule_groups[t].rules.length;const e={type:"",config:{}};s.value.rule_groups[t].rules.push(e)},se=(t,e)=>{s.value.rule_groups[t].rules.splice(e,1)},ue=pe(()=>m.value.map(t=>({label:K(t),value:t}))),ne=async(t,e,r)=>{t&&(B.value=t,k.value=e,N.value=r,s.value.rule_groups[e].rules[r].type=t,s.value.rule_groups[e].rules[r].config={},await F(t),x.value=!0)},ie=t=>{k.value===-1||!s.value.rule_groups||(s.value.rule_groups[k.value].rules[N.value].config=t)},de=()=>{x.value=!1};return ve(async()=>{await Promise.all([T(),Z(),Y()])}),(t,e)=>(h(),R("div",Oe,[u(o(P),{vertical:""},{default:n(()=>[u(o(me),{title:"规则列表",class:"dispatch-rules-card"},{"header-extra":n(()=>[u(o(g),{type:"primary",onClick:ee},{default:n(()=>e[8]||(e[8]=[b(" 创建规则 ")])),_:1})]),default:n(()=>[e[9]||(e[9]=d("div",{class:"dispatch-rules-description"},[b(" 触发规则决定了 Kirara AI 何时会执行工作流，更多介绍请阅读"),d("a",{href:"https://kirara-docs.app.lss233.com/guide/configuration/dispatch.html",target:"_blank"},"官方文档"),b("。 ")],-1)),u(o(Ce),{columns:Q,data:p.value,bordered:!1,"single-line":!1},null,8,["data"])]),_:1}),u(o(H),{show:f.value,"onUpdate:show":e[7]||(e[7]=r=>f.value=r),preset:"dialog",title:D.value?"创建规则":"编辑规则",style:{width:"1200px"}},{action:n(()=>[u(o(g),{type:"primary",onClick:e[6]||(e[6]=r=>ae(D.value))},{default:n(()=>e[17]||(e[17]=[b(" 确定 ")])),_:1})]),default:n(()=>[d("div",Pe,[d("div",ze,[u(o(Ne),{"label-placement":"left","label-width":"80",rules:X.value,ref_key:"formRef",ref:E},{default:n(()=>[u(o($),{label:"名称",required:"",feedback:"用于区分不同的规则，必须保持唯一"},{default:n(()=>[u(o(z),{value:s.value.name,"onUpdate:value":e[0]||(e[0]=r=>s.value.name=r),placeholder:"请输入名称"},null,8,["value"])]),_:1}),u(o($),{label:"描述",feedback:"用于描述规则的用途，方便你理解"},{default:n(()=>[u(o(z),{value:s.value.description,"onUpdate:value":e[1]||(e[1]=r=>s.value.description=r),type:"textarea",placeholder:"请输入描述"},null,8,["value"])]),_:1}),u(o($),{label:"工作流",required:"",feedback:"指定触发规则的工作流"},{default:n(()=>[u(o(G),{value:s.value.workflow_id,"onUpdate:value":e[2]||(e[2]=r=>s.value.workflow_id=r),options:q.value.map(r=>({label:r.name+" ("+r.group_id+":"+r.workflow_id+")",value:`${r.group_id}:${r.workflow_id}`})),placeholder:"请选择工作流"},null,8,["value","options"])]),_:1}),u(o($),{label:"优先级",required:"",feedback:"数值越大优先级越高，会优先被判断"},{default:n(()=>[u(o(xe),{value:s.value.priority,"onUpdate:value":e[3]||(e[3]=r=>s.value.priority=r),min:0,max:100},null,8,["value"])]),_:1}),u(o($),{label:"启用状态"},{default:n(()=>[u(o(I),{value:s.value.enabled,"onUpdate:value":e[4]||(e[4]=r=>s.value.enabled=r)},null,8,["value"])]),_:1})]),_:1},8,["rules"])]),d("div",Ge,[u(o(Ue),{vertical:""}),d("div",Ie,[e[15]||(e[15]=d("h3",{class:"config-title"},"触发条件",-1)),u(o(he),{style:{"max-height":"400px"}},{default:n(()=>[d("div",We,[e[14]||(e[14]=d("div",{class:"rule-group-header"},[d("span",{class:"rule-group-label"},"当")],-1)),(h(!0),R(L,null,O(s.value.rule_groups,(r,v)=>(h(),R("div",{key:v,class:"rule-group"},[d("div",Ke,[(h(!0),R(L,null,O(r.rules,(C,S)=>(h(),R(L,{key:S},[d("div",Je,[u(o(G),{value:C.type,"onUpdate:value":[V=>C.type=V,V=>ne(V,v,S)],options:ue.value,class:"rule-type-select",placeholder:"请选择规则类型"},null,8,["value","onUpdate:value","options"]),u(o(g),{circle:"",tertiary:"",type:"info",onClick:()=>{k.value=v,N.value=S,B.value=C.type,F(C.type),x.value=!0},disabled:!C.type},{icon:n(()=>[u(o(U),null,{default:n(()=>[u(o(Re))]),_:1})]),_:2},1032,["onClick","disabled"]),u(o(g),{circle:"",tertiary:"",type:"error",onClick:V=>se(v,S)},{icon:n(()=>[u(o(U),null,{default:n(()=>[u(o(Be))]),_:1})]),_:2},1032,["onClick"])]),e[10]||(e[10]=d("span",{class:"operator"},"或",-1))],64))),128)),u(o(g),{dashed:"",class:"add-rule-btn",onClick:C=>oe(v)},{icon:n(()=>[u(o(U),null,{default:n(()=>[u(o(W))]),_:1})]),default:n(()=>[e[11]||(e[11]=b(" 添加条件 "))]),_:2},1032,["onClick"])]),e[12]||(e[12]=d("div",{class:"group-operator"}," 且 ",-1))]))),128)),u(o(g),{dashed:"",block:"",class:"add-group-btn",onClick:re,disabled:s.value.rule_groups[s.value.rule_groups.length-1].rules.length===0},{icon:n(()=>[u(o(U),null,{default:n(()=>[u(o(W))]),_:1})]),default:n(()=>[e[13]||(e[13]=b(" 添加条件组 "))]),_:1},8,["disabled"])])]),_:1})])])]),u(o(H),{show:x.value,"onUpdate:show":e[5]||(e[5]=r=>x.value=r),preset:"dialog",title:"配置"+o(K)(B.value)+"规则",style:{width:"600px"}},{action:n(()=>[u(o(g),{type:"primary",onClick:de},{default:n(()=>e[16]||(e[16]=[b(" 确定 ")])),_:1})]),default:n(()=>{var r;return[M.value&&k.value>=0?(h(),ge(ke,{key:0,"model-value":((r=s.value.rule_groups[k.value].rules[N.value])==null?void 0:r.config)||{},schema:M.value,"onUpdate:modelValue":ie},null,8,["model-value","schema"])):fe("",!0)]}),_:1},8,["show","title"])]),_:1},8,["show","title"])]),_:1})]))}});const gl=_e(Qe,[["__scopeId","data-v-e1b8ff99"]]);export{gl as default};
