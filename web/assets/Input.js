import{d as D,O as n,be as sr,I as m,J as C,K as s,az as cr,M as Ee,X as xe,aC as ur,aa as q,ac as le,bc as dr,P as hr,b0 as ge,Z as fr,R as A,a1 as Z,r as w,w as me,$ as vr,b as R,bh as pr,L as gr,V as Te,c2 as br,aS as xr,b6 as mr,b7 as Fe,e as wr,aG as yr,ay as Ae,W as Cr,U as zr,Y as Sr,ab as te,ak as $r,F as Fr,a2 as Ar,v as _e,b8 as Re,aj as y,at as be,aV as _r,bw as Be}from"./index.js";import{u as Rr}from"./use-locale.js";const Br=D({name:"ChevronDown",render(){return n("svg",{viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},n("path",{d:"M3.14645 5.64645C3.34171 5.45118 3.65829 5.45118 3.85355 5.64645L8 9.79289L12.1464 5.64645C12.3417 5.45118 12.6583 5.45118 12.8536 5.64645C13.0488 5.84171 13.0488 6.15829 12.8536 6.35355L8.35355 10.8536C8.15829 11.0488 7.84171 11.0488 7.64645 10.8536L3.14645 6.35355C2.95118 6.15829 2.95118 5.84171 3.14645 5.64645Z",fill:"currentColor"}))}}),Pr=sr("clear",()=>n("svg",{viewBox:"0 0 16 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},n("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},n("g",{fill:"currentColor","fill-rule":"nonzero"},n("path",{d:"M8,2 C11.3137085,2 14,4.6862915 14,8 C14,11.3137085 11.3137085,14 8,14 C4.6862915,14 2,11.3137085 2,8 C2,4.6862915 4.6862915,2 8,2 Z M6.5343055,5.83859116 C6.33943736,5.70359511 6.07001296,5.72288026 5.89644661,5.89644661 L5.89644661,5.89644661 L5.83859116,5.9656945 C5.70359511,6.16056264 5.72288026,6.42998704 5.89644661,6.60355339 L5.89644661,6.60355339 L7.293,8 L5.89644661,9.39644661 L5.83859116,9.4656945 C5.70359511,9.66056264 5.72288026,9.92998704 5.89644661,10.1035534 L5.89644661,10.1035534 L5.9656945,10.1614088 C6.16056264,10.2964049 6.42998704,10.2771197 6.60355339,10.1035534 L6.60355339,10.1035534 L8,8.707 L9.39644661,10.1035534 L9.4656945,10.1614088 C9.66056264,10.2964049 9.92998704,10.2771197 10.1035534,10.1035534 L10.1035534,10.1035534 L10.1614088,10.0343055 C10.2964049,9.83943736 10.2771197,9.57001296 10.1035534,9.39644661 L10.1035534,9.39644661 L8.707,8 L10.1035534,6.60355339 L10.1614088,6.5343055 C10.2964049,6.33943736 10.2771197,6.07001296 10.1035534,5.89644661 L10.1035534,5.89644661 L10.0343055,5.83859116 C9.83943736,5.70359511 9.57001296,5.72288026 9.39644661,5.89644661 L9.39644661,5.89644661 L8,7.293 L6.60355339,5.89644661 Z"}))))),Er=D({name:"Eye",render(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"},n("path",{d:"M255.66 112c-77.94 0-157.89 45.11-220.83 135.33a16 16 0 0 0-.27 17.77C82.92 340.8 161.8 400 255.66 400c92.84 0 173.34-59.38 221.79-135.25a16.14 16.14 0 0 0 0-17.47C428.89 172.28 347.8 112 255.66 112z",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"}),n("circle",{cx:"256",cy:"256",r:"80",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"}))}}),Tr=D({name:"EyeOff",render(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"},n("path",{d:"M432 448a15.92 15.92 0 0 1-11.31-4.69l-352-352a16 16 0 0 1 22.62-22.62l352 352A16 16 0 0 1 432 448z",fill:"currentColor"}),n("path",{d:"M255.66 384c-41.49 0-81.5-12.28-118.92-36.5c-34.07-22-64.74-53.51-88.7-91v-.08c19.94-28.57 41.78-52.73 65.24-72.21a2 2 0 0 0 .14-2.94L93.5 161.38a2 2 0 0 0-2.71-.12c-24.92 21-48.05 46.76-69.08 76.92a31.92 31.92 0 0 0-.64 35.54c26.41 41.33 60.4 76.14 98.28 100.65C162 402 207.9 416 255.66 416a239.13 239.13 0 0 0 75.8-12.58a2 2 0 0 0 .77-3.31l-21.58-21.58a4 4 0 0 0-3.83-1a204.8 204.8 0 0 1-51.16 6.47z",fill:"currentColor"}),n("path",{d:"M490.84 238.6c-26.46-40.92-60.79-75.68-99.27-100.53C349 110.55 302 96 255.66 96a227.34 227.34 0 0 0-74.89 12.83a2 2 0 0 0-.75 3.31l21.55 21.55a4 4 0 0 0 3.88 1a192.82 192.82 0 0 1 50.21-6.69c40.69 0 80.58 12.43 118.55 37c34.71 22.4 65.74 53.88 89.76 91a.13.13 0 0 1 0 .16a310.72 310.72 0 0 1-64.12 72.73a2 2 0 0 0-.15 2.95l19.9 19.89a2 2 0 0 0 2.7.13a343.49 343.49 0 0 0 68.64-78.48a32.2 32.2 0 0 0-.1-34.78z",fill:"currentColor"}),n("path",{d:"M256 160a95.88 95.88 0 0 0-21.37 2.4a2 2 0 0 0-1 3.38l112.59 112.56a2 2 0 0 0 3.38-1A96 96 0 0 0 256 160z",fill:"currentColor"}),n("path",{d:"M165.78 233.66a2 2 0 0 0-3.38 1a96 96 0 0 0 115 115a2 2 0 0 0 1-3.38z",fill:"currentColor"}))}}),kr=m("base-clear",`
 flex-shrink: 0;
 height: 1em;
 width: 1em;
 position: relative;
`,[C(">",[s("clear",`
 font-size: var(--n-clear-size);
 height: 1em;
 width: 1em;
 cursor: pointer;
 color: var(--n-clear-color);
 transition: color .3s var(--n-bezier);
 display: flex;
 `,[C("&:hover",`
 color: var(--n-clear-color-hover)!important;
 `),C("&:active",`
 color: var(--n-clear-color-pressed)!important;
 `)]),s("placeholder",`
 display: flex;
 `),s("clear, placeholder",`
 position: absolute;
 left: 50%;
 top: 50%;
 transform: translateX(-50%) translateY(-50%);
 `,[cr({originalTransform:"translateX(-50%) translateY(-50%)",left:"50%",top:"50%"})])])]),we=D({name:"BaseClear",props:{clsPrefix:{type:String,required:!0},show:Boolean,onClear:Function},setup(r){return Ee("-base-clear",kr,xe(r,"clsPrefix")),{handleMouseDown(h){h.preventDefault()}}},render(){const{clsPrefix:r}=this;return n("div",{class:`${r}-base-clear`},n(ur,null,{default:()=>{var h,t;return this.show?n("div",{key:"dismiss",class:`${r}-base-clear__clear`,onClick:this.onClear,onMousedown:this.handleMouseDown,"data-clear":!0},q(this.$slots.icon,()=>[n(le,{clsPrefix:r},{default:()=>n(Pr,null)})])):n("div",{key:"icon",class:`${r}-base-clear__placeholder`},(t=(h=this.$slots).placeholder)===null||t===void 0?void 0:t.call(h))}}))}}),Ir=D({name:"InternalSelectionSuffix",props:{clsPrefix:{type:String,required:!0},showArrow:{type:Boolean,default:void 0},showClear:{type:Boolean,default:void 0},loading:{type:Boolean,default:!1},onClear:Function},setup(r,{slots:h}){return()=>{const{clsPrefix:t}=r;return n(dr,{clsPrefix:t,class:`${t}-base-suffix`,strokeWidth:24,scale:.85,show:r.loading},{default:()=>r.showArrow?n(we,{clsPrefix:t,show:r.showClear,onClear:r.onClear},{placeholder:()=>n(le,{clsPrefix:t,class:`${t}-base-suffix__arrow`},{default:()=>q(h.default,()=>[n(Br,null)])})}):null})}}}),Mr={paddingTiny:"0 8px",paddingSmall:"0 10px",paddingMedium:"0 12px",paddingLarge:"0 14px",clearSize:"16px"};function Lr(r){const{textColor2:h,textColor3:t,textColorDisabled:$,primaryColor:z,primaryColorHover:b,inputColor:d,inputColorDisabled:c,borderColor:u,warningColor:i,warningColorHover:l,errorColor:v,errorColorHover:g,borderRadius:B,lineHeight:S,fontSizeTiny:ie,fontSizeSmall:V,fontSizeMedium:se,fontSizeLarge:F,heightTiny:T,heightSmall:O,heightMedium:P,heightLarge:ce,actionColor:E,clearColor:k,clearColorHover:_,clearColorPressed:I,placeholderColor:N,placeholderColorDisabled:j,iconColor:ue,iconColorDisabled:de,iconColorHover:K,iconColorPressed:he,fontWeight:U}=r;return Object.assign(Object.assign({},Mr),{fontWeight:U,countTextColorDisabled:$,countTextColor:t,heightTiny:T,heightSmall:O,heightMedium:P,heightLarge:ce,fontSizeTiny:ie,fontSizeSmall:V,fontSizeMedium:se,fontSizeLarge:F,lineHeight:S,lineHeightTextarea:S,borderRadius:B,iconSize:"16px",groupLabelColor:E,groupLabelTextColor:h,textColor:h,textColorDisabled:$,textDecorationColor:h,caretColor:z,placeholderColor:N,placeholderColorDisabled:j,color:d,colorDisabled:c,colorFocus:d,groupLabelBorder:`1px solid ${u}`,border:`1px solid ${u}`,borderHover:`1px solid ${b}`,borderDisabled:`1px solid ${u}`,borderFocus:`1px solid ${b}`,boxShadowFocus:`0 0 0 2px ${ge(z,{alpha:.2})}`,loadingColor:z,loadingColorWarning:i,borderWarning:`1px solid ${i}`,borderHoverWarning:`1px solid ${l}`,colorFocusWarning:d,borderFocusWarning:`1px solid ${l}`,boxShadowFocusWarning:`0 0 0 2px ${ge(i,{alpha:.2})}`,caretColorWarning:i,loadingColorError:v,borderError:`1px solid ${v}`,borderHoverError:`1px solid ${g}`,colorFocusError:d,borderFocusError:`1px solid ${g}`,boxShadowFocusError:`0 0 0 2px ${ge(v,{alpha:.2})}`,caretColorError:v,clearColor:k,clearColorHover:_,clearColorPressed:I,iconColor:ue,iconColorDisabled:de,iconColorHover:K,iconColorPressed:he,suffixTextColor:h})}const Wr={name:"Input",common:hr,self:Lr},Dr=Wr,ke=fr("n-input"),Vr=m("input",`
 max-width: 100%;
 cursor: text;
 line-height: 1.5;
 z-index: auto;
 outline: none;
 box-sizing: border-box;
 position: relative;
 display: inline-flex;
 border-radius: var(--n-border-radius);
 background-color: var(--n-color);
 transition: background-color .3s var(--n-bezier);
 font-size: var(--n-font-size);
 font-weight: var(--n-font-weight);
 --n-padding-vertical: calc((var(--n-height) - 1.5 * var(--n-font-size)) / 2);
`,[s("input, textarea",`
 overflow: hidden;
 flex-grow: 1;
 position: relative;
 `),s("input-el, textarea-el, input-mirror, textarea-mirror, separator, placeholder",`
 box-sizing: border-box;
 font-size: inherit;
 line-height: 1.5;
 font-family: inherit;
 border: none;
 outline: none;
 background-color: #0000;
 text-align: inherit;
 transition:
 -webkit-text-fill-color .3s var(--n-bezier),
 caret-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 text-decoration-color .3s var(--n-bezier);
 `),s("input-el, textarea-el",`
 -webkit-appearance: none;
 scrollbar-width: none;
 width: 100%;
 min-width: 0;
 text-decoration-color: var(--n-text-decoration-color);
 color: var(--n-text-color);
 caret-color: var(--n-caret-color);
 background-color: transparent;
 `,[C("&::-webkit-scrollbar, &::-webkit-scrollbar-track-piece, &::-webkit-scrollbar-thumb",`
 width: 0;
 height: 0;
 display: none;
 `),C("&::placeholder",`
 color: #0000;
 -webkit-text-fill-color: transparent !important;
 `),C("&:-webkit-autofill ~",[s("placeholder","display: none;")])]),A("round",[Z("textarea","border-radius: calc(var(--n-height) / 2);")]),s("placeholder",`
 pointer-events: none;
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 overflow: hidden;
 color: var(--n-placeholder-color);
 `,[C("span",`
 width: 100%;
 display: inline-block;
 `)]),A("textarea",[s("placeholder","overflow: visible;")]),Z("autosize","width: 100%;"),A("autosize",[s("textarea-el, input-el",`
 position: absolute;
 top: 0;
 left: 0;
 height: 100%;
 `)]),m("input-wrapper",`
 overflow: hidden;
 display: inline-flex;
 flex-grow: 1;
 position: relative;
 padding-left: var(--n-padding-left);
 padding-right: var(--n-padding-right);
 `),s("input-mirror",`
 padding: 0;
 height: var(--n-height);
 line-height: var(--n-height);
 overflow: hidden;
 visibility: hidden;
 position: static;
 white-space: pre;
 pointer-events: none;
 `),s("input-el",`
 padding: 0;
 height: var(--n-height);
 line-height: var(--n-height);
 `,[C("&[type=password]::-ms-reveal","display: none;"),C("+",[s("placeholder",`
 display: flex;
 align-items: center; 
 `)])]),Z("textarea",[s("placeholder","white-space: nowrap;")]),s("eye",`
 display: flex;
 align-items: center;
 justify-content: center;
 transition: color .3s var(--n-bezier);
 `),A("textarea","width: 100%;",[m("input-word-count",`
 position: absolute;
 right: var(--n-padding-right);
 bottom: var(--n-padding-vertical);
 `),A("resizable",[m("input-wrapper",`
 resize: vertical;
 min-height: var(--n-height);
 `)]),s("textarea-el, textarea-mirror, placeholder",`
 height: 100%;
 padding-left: 0;
 padding-right: 0;
 padding-top: var(--n-padding-vertical);
 padding-bottom: var(--n-padding-vertical);
 word-break: break-word;
 display: inline-block;
 vertical-align: bottom;
 box-sizing: border-box;
 line-height: var(--n-line-height-textarea);
 margin: 0;
 resize: none;
 white-space: pre-wrap;
 scroll-padding-block-end: var(--n-padding-vertical);
 `),s("textarea-mirror",`
 width: 100%;
 pointer-events: none;
 overflow: hidden;
 visibility: hidden;
 position: static;
 white-space: pre-wrap;
 overflow-wrap: break-word;
 `)]),A("pair",[s("input-el, placeholder","text-align: center;"),s("separator",`
 display: flex;
 align-items: center;
 transition: color .3s var(--n-bezier);
 color: var(--n-text-color);
 white-space: nowrap;
 `,[m("icon",`
 color: var(--n-icon-color);
 `),m("base-icon",`
 color: var(--n-icon-color);
 `)])]),A("disabled",`
 cursor: not-allowed;
 background-color: var(--n-color-disabled);
 `,[s("border","border: var(--n-border-disabled);"),s("input-el, textarea-el",`
 cursor: not-allowed;
 color: var(--n-text-color-disabled);
 text-decoration-color: var(--n-text-color-disabled);
 `),s("placeholder","color: var(--n-placeholder-color-disabled);"),s("separator","color: var(--n-text-color-disabled);",[m("icon",`
 color: var(--n-icon-color-disabled);
 `),m("base-icon",`
 color: var(--n-icon-color-disabled);
 `)]),m("input-word-count",`
 color: var(--n-count-text-color-disabled);
 `),s("suffix, prefix","color: var(--n-text-color-disabled);",[m("icon",`
 color: var(--n-icon-color-disabled);
 `),m("internal-icon",`
 color: var(--n-icon-color-disabled);
 `)])]),Z("disabled",[s("eye",`
 color: var(--n-icon-color);
 cursor: pointer;
 `,[C("&:hover",`
 color: var(--n-icon-color-hover);
 `),C("&:active",`
 color: var(--n-icon-color-pressed);
 `)]),C("&:hover",[s("state-border","border: var(--n-border-hover);")]),A("focus","background-color: var(--n-color-focus);",[s("state-border",`
 border: var(--n-border-focus);
 box-shadow: var(--n-box-shadow-focus);
 `)])]),s("border, state-border",`
 box-sizing: border-box;
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 pointer-events: none;
 border-radius: inherit;
 border: var(--n-border);
 transition:
 box-shadow .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 `),s("state-border",`
 border-color: #0000;
 z-index: 1;
 `),s("prefix","margin-right: 4px;"),s("suffix",`
 margin-left: 4px;
 `),s("suffix, prefix",`
 transition: color .3s var(--n-bezier);
 flex-wrap: nowrap;
 flex-shrink: 0;
 line-height: var(--n-height);
 white-space: nowrap;
 display: inline-flex;
 align-items: center;
 justify-content: center;
 color: var(--n-suffix-text-color);
 `,[m("base-loading",`
 font-size: var(--n-icon-size);
 margin: 0 2px;
 color: var(--n-loading-color);
 `),m("base-clear",`
 font-size: var(--n-icon-size);
 `,[s("placeholder",[m("base-icon",`
 transition: color .3s var(--n-bezier);
 color: var(--n-icon-color);
 font-size: var(--n-icon-size);
 `)])]),C(">",[m("icon",`
 transition: color .3s var(--n-bezier);
 color: var(--n-icon-color);
 font-size: var(--n-icon-size);
 `)]),m("base-icon",`
 font-size: var(--n-icon-size);
 `)]),m("input-word-count",`
 pointer-events: none;
 line-height: 1.5;
 font-size: .85em;
 color: var(--n-count-text-color);
 transition: color .3s var(--n-bezier);
 margin-left: 4px;
 font-variant: tabular-nums;
 `),["warning","error"].map(r=>A(`${r}-status`,[Z("disabled",[m("base-loading",`
 color: var(--n-loading-color-${r})
 `),s("input-el, textarea-el",`
 caret-color: var(--n-caret-color-${r});
 `),s("state-border",`
 border: var(--n-border-${r});
 `),C("&:hover",[s("state-border",`
 border: var(--n-border-hover-${r});
 `)]),C("&:focus",`
 background-color: var(--n-color-focus-${r});
 `,[s("state-border",`
 box-shadow: var(--n-box-shadow-focus-${r});
 border: var(--n-border-focus-${r});
 `)]),A("focus",`
 background-color: var(--n-color-focus-${r});
 `,[s("state-border",`
 box-shadow: var(--n-box-shadow-focus-${r});
 border: var(--n-border-focus-${r});
 `)])])]))]),Hr=m("input",[A("disabled",[s("input-el, textarea-el",`
 -webkit-text-fill-color: var(--n-text-color-disabled);
 `)])]);function Or(r){let h=0;for(const t of r)h++;return h}function ae(r){return r===""||r==null}function Nr(r){const h=w(null);function t(){const{value:b}=r;if(!(b!=null&&b.focus)){z();return}const{selectionStart:d,selectionEnd:c,value:u}=b;if(d==null||c==null){z();return}h.value={start:d,end:c,beforeText:u.slice(0,d),afterText:u.slice(c)}}function $(){var b;const{value:d}=h,{value:c}=r;if(!d||!c)return;const{value:u}=c,{start:i,beforeText:l,afterText:v}=d;let g=u.length;if(u.endsWith(v))g=u.length-v.length;else if(u.startsWith(l))g=l.length;else{const B=l[i-1],S=u.indexOf(B,i-1);S!==-1&&(g=S+1)}(b=c.setSelectionRange)===null||b===void 0||b.call(c,g,g)}function z(){h.value=null}return me(r,z),{recordCursor:t,restoreCursor:$}}const Pe=D({name:"InputWordCount",setup(r,{slots:h}){const{mergedValueRef:t,maxlengthRef:$,mergedClsPrefixRef:z,countGraphemesRef:b}=vr(ke),d=R(()=>{const{value:c}=t;return c===null||Array.isArray(c)?0:(b.value||Or)(c)});return()=>{const{value:c}=$,{value:u}=t;return n("span",{class:`${z.value}-input-word-count`},pr(h.default,{value:u===null||Array.isArray(u)?"":u},()=>[c===void 0?d.value:`${d.value} / ${c}`]))}}}),jr=Object.assign(Object.assign({},Te.props),{bordered:{type:Boolean,default:void 0},type:{type:String,default:"text"},placeholder:[Array,String],defaultValue:{type:[String,Array],default:null},value:[String,Array],disabled:{type:Boolean,default:void 0},size:String,rows:{type:[Number,String],default:3},round:Boolean,minlength:[String,Number],maxlength:[String,Number],clearable:Boolean,autosize:{type:[Boolean,Object],default:!1},pair:Boolean,separator:String,readonly:{type:[String,Boolean],default:!1},passivelyActivated:Boolean,showPasswordOn:String,stateful:{type:Boolean,default:!0},autofocus:Boolean,inputProps:Object,resizable:{type:Boolean,default:!0},showCount:Boolean,loading:{type:Boolean,default:void 0},allowInput:Function,renderCount:Function,onMousedown:Function,onKeydown:Function,onKeyup:[Function,Array],onInput:[Function,Array],onFocus:[Function,Array],onBlur:[Function,Array],onClick:[Function,Array],onChange:[Function,Array],onClear:[Function,Array],countGraphemes:Function,status:String,"onUpdate:value":[Function,Array],onUpdateValue:[Function,Array],textDecoration:[String,Array],attrSize:{type:Number,default:20},onInputBlur:[Function,Array],onInputFocus:[Function,Array],onDeactivate:[Function,Array],onActivate:[Function,Array],onWrapperFocus:[Function,Array],onWrapperBlur:[Function,Array],internalDeactivateOnEnter:Boolean,internalForceFocus:Boolean,internalLoadingBeforeSuffix:{type:Boolean,default:!0},showPasswordToggle:Boolean}),Gr=D({name:"Input",props:jr,slots:Object,setup(r){const{mergedClsPrefixRef:h,mergedBorderedRef:t,inlineThemeDisabled:$,mergedRtlRef:z}=gr(r),b=Te("Input","-input",Vr,Dr,r,h);br&&Ee("-input-safari",Hr,h);const d=w(null),c=w(null),u=w(null),i=w(null),l=w(null),v=w(null),g=w(null),B=Nr(g),S=w(null),{localeRef:ie}=Rr("Input"),V=w(r.defaultValue),se=xe(r,"value"),F=xr(se,V),T=mr(r),{mergedSizeRef:O,mergedDisabledRef:P,mergedStatusRef:ce}=T,E=w(!1),k=w(!1),_=w(!1),I=w(!1);let N=null;const j=R(()=>{const{placeholder:e,pair:o}=r;return o?Array.isArray(e)?e:e===void 0?["",""]:[e,e]:e===void 0?[ie.value.placeholder]:[e]}),ue=R(()=>{const{value:e}=_,{value:o}=F,{value:a}=j;return!e&&(ae(o)||Array.isArray(o)&&ae(o[0]))&&a[0]}),de=R(()=>{const{value:e}=_,{value:o}=F,{value:a}=j;return!e&&a[1]&&(ae(o)||Array.isArray(o)&&ae(o[1]))}),K=Fe(()=>r.internalForceFocus||E.value),he=Fe(()=>{if(P.value||r.readonly||!r.clearable||!K.value&&!k.value)return!1;const{value:e}=F,{value:o}=K;return r.pair?!!(Array.isArray(e)&&(e[0]||e[1]))&&(k.value||o):!!e&&(k.value||o)}),U=R(()=>{const{showPasswordOn:e}=r;if(e)return e;if(r.showPasswordToggle)return"click"}),G=w(!1),Ie=R(()=>{const{textDecoration:e}=r;return e?Array.isArray(e)?e.map(o=>({textDecoration:o})):[{textDecoration:e}]:["",""]}),ye=w(void 0),Me=()=>{var e,o;if(r.type==="textarea"){const{autosize:a}=r;if(a&&(ye.value=(o=(e=S.value)===null||e===void 0?void 0:e.$el)===null||o===void 0?void 0:o.offsetWidth),!c.value||typeof a=="boolean")return;const{paddingTop:p,paddingBottom:x,lineHeight:f}=window.getComputedStyle(c.value),M=Number(p.slice(0,-2)),L=Number(x.slice(0,-2)),W=Number(f.slice(0,-2)),{value:X}=u;if(!X)return;if(a.minRows){const Y=Math.max(a.minRows,1),pe=`${M+L+W*Y}px`;X.style.minHeight=pe}if(a.maxRows){const Y=`${M+L+W*a.maxRows}px`;X.style.maxHeight=Y}}},Le=R(()=>{const{maxlength:e}=r;return e===void 0?void 0:Number(e)});wr(()=>{const{value:e}=F;Array.isArray(e)||ve(e)});const We=yr().proxy;function J(e,o){const{onUpdateValue:a,"onUpdate:value":p,onInput:x}=r,{nTriggerFormInput:f}=T;a&&y(a,e,o),p&&y(p,e,o),x&&y(x,e,o),V.value=e,f()}function Q(e,o){const{onChange:a}=r,{nTriggerFormChange:p}=T;a&&y(a,e,o),V.value=e,p()}function De(e){const{onBlur:o}=r,{nTriggerFormBlur:a}=T;o&&y(o,e),a()}function Ve(e){const{onFocus:o}=r,{nTriggerFormFocus:a}=T;o&&y(o,e),a()}function He(e){const{onClear:o}=r;o&&y(o,e)}function Oe(e){const{onInputBlur:o}=r;o&&y(o,e)}function Ne(e){const{onInputFocus:o}=r;o&&y(o,e)}function je(){const{onDeactivate:e}=r;e&&y(e)}function Ke(){const{onActivate:e}=r;e&&y(e)}function Ue(e){const{onClick:o}=r;o&&y(o,e)}function Ge(e){const{onWrapperFocus:o}=r;o&&y(o,e)}function Xe(e){const{onWrapperBlur:o}=r;o&&y(o,e)}function Ye(){_.value=!0}function Ze(e){_.value=!1,e.target===v.value?ee(e,1):ee(e,0)}function ee(e,o=0,a="input"){const p=e.target.value;if(ve(p),e instanceof InputEvent&&!e.isComposing&&(_.value=!1),r.type==="textarea"){const{value:f}=S;f&&f.syncUnifiedContainer()}if(N=p,_.value)return;B.recordCursor();const x=qe(p);if(x)if(!r.pair)a==="input"?J(p,{source:o}):Q(p,{source:o});else{let{value:f}=F;Array.isArray(f)?f=[f[0],f[1]]:f=["",""],f[o]=p,a==="input"?J(f,{source:o}):Q(f,{source:o})}We.$forceUpdate(),x||_e(B.restoreCursor)}function qe(e){const{countGraphemes:o,maxlength:a,minlength:p}=r;if(o){let f;if(a!==void 0&&(f===void 0&&(f=o(e)),f>Number(a))||p!==void 0&&(f===void 0&&(f=o(e)),f<Number(a)))return!1}const{allowInput:x}=r;return typeof x=="function"?x(e):!0}function Je(e){Oe(e),e.relatedTarget===d.value&&je(),e.relatedTarget!==null&&(e.relatedTarget===l.value||e.relatedTarget===v.value||e.relatedTarget===c.value)||(I.value=!1),oe(e,"blur"),g.value=null}function Qe(e,o){Ne(e),E.value=!0,I.value=!0,Ke(),oe(e,"focus"),o===0?g.value=l.value:o===1?g.value=v.value:o===2&&(g.value=c.value)}function eo(e){r.passivelyActivated&&(Xe(e),oe(e,"blur"))}function oo(e){r.passivelyActivated&&(E.value=!0,Ge(e),oe(e,"focus"))}function oe(e,o){e.relatedTarget!==null&&(e.relatedTarget===l.value||e.relatedTarget===v.value||e.relatedTarget===c.value||e.relatedTarget===d.value)||(o==="focus"?(Ve(e),E.value=!0):o==="blur"&&(De(e),E.value=!1))}function ro(e,o){ee(e,o,"change")}function no(e){Ue(e)}function to(e){He(e),Ce()}function Ce(){r.pair?(J(["",""],{source:"clear"}),Q(["",""],{source:"clear"})):(J("",{source:"clear"}),Q("",{source:"clear"}))}function ao(e){const{onMousedown:o}=r;o&&o(e);const{tagName:a}=e.target;if(a!=="INPUT"&&a!=="TEXTAREA"){if(r.resizable){const{value:p}=d;if(p){const{left:x,top:f,width:M,height:L}=p.getBoundingClientRect(),W=14;if(x+M-W<e.clientX&&e.clientX<x+M&&f+L-W<e.clientY&&e.clientY<f+L)return}}e.preventDefault(),E.value||ze()}}function lo(){var e;k.value=!0,r.type==="textarea"&&((e=S.value)===null||e===void 0||e.handleMouseEnterWrapper())}function io(){var e;k.value=!1,r.type==="textarea"&&((e=S.value)===null||e===void 0||e.handleMouseLeaveWrapper())}function so(){P.value||U.value==="click"&&(G.value=!G.value)}function co(e){if(P.value)return;e.preventDefault();const o=p=>{p.preventDefault(),Be("mouseup",document,o)};if(Re("mouseup",document,o),U.value!=="mousedown")return;G.value=!0;const a=()=>{G.value=!1,Be("mouseup",document,a)};Re("mouseup",document,a)}function uo(e){r.onKeyup&&y(r.onKeyup,e)}function ho(e){switch(r.onKeydown&&y(r.onKeydown,e),e.key){case"Escape":fe();break;case"Enter":fo(e);break}}function fo(e){var o,a;if(r.passivelyActivated){const{value:p}=I;if(p){r.internalDeactivateOnEnter&&fe();return}e.preventDefault(),r.type==="textarea"?(o=c.value)===null||o===void 0||o.focus():(a=l.value)===null||a===void 0||a.focus()}}function fe(){r.passivelyActivated&&(I.value=!1,_e(()=>{var e;(e=d.value)===null||e===void 0||e.focus()}))}function ze(){var e,o,a;P.value||(r.passivelyActivated?(e=d.value)===null||e===void 0||e.focus():((o=c.value)===null||o===void 0||o.focus(),(a=l.value)===null||a===void 0||a.focus()))}function vo(){var e;!((e=d.value)===null||e===void 0)&&e.contains(document.activeElement)&&document.activeElement.blur()}function po(){var e,o;(e=c.value)===null||e===void 0||e.select(),(o=l.value)===null||o===void 0||o.select()}function go(){P.value||(c.value?c.value.focus():l.value&&l.value.focus())}function bo(){const{value:e}=d;e!=null&&e.contains(document.activeElement)&&e!==document.activeElement&&fe()}function xo(e){if(r.type==="textarea"){const{value:o}=c;o==null||o.scrollTo(e)}else{const{value:o}=l;o==null||o.scrollTo(e)}}function ve(e){const{type:o,pair:a,autosize:p}=r;if(!a&&p)if(o==="textarea"){const{value:x}=u;x&&(x.textContent=`${e??""}\r
`)}else{const{value:x}=i;x&&(e?x.textContent=e:x.innerHTML="&nbsp;")}}function mo(){Me()}const Se=w({top:"0"});function wo(e){var o;const{scrollTop:a}=e.target;Se.value.top=`${-a}px`,(o=S.value)===null||o===void 0||o.syncUnifiedContainer()}let re=null;Ae(()=>{const{autosize:e,type:o}=r;e&&o==="textarea"?re=me(F,a=>{!Array.isArray(a)&&a!==N&&ve(a)}):re==null||re()});let ne=null;Ae(()=>{r.type==="textarea"?ne=me(F,e=>{var o;!Array.isArray(e)&&e!==N&&((o=S.value)===null||o===void 0||o.syncUnifiedContainer())}):ne==null||ne()}),Cr(ke,{mergedValueRef:F,maxlengthRef:Le,mergedClsPrefixRef:h,countGraphemesRef:xe(r,"countGraphemes")});const yo={wrapperElRef:d,inputElRef:l,textareaElRef:c,isCompositing:_,clear:Ce,focus:ze,blur:vo,select:po,deactivate:bo,activate:go,scrollTo:xo},Co=zr("Input",z,h),$e=R(()=>{const{value:e}=O,{common:{cubicBezierEaseInOut:o},self:{color:a,borderRadius:p,textColor:x,caretColor:f,caretColorError:M,caretColorWarning:L,textDecorationColor:W,border:X,borderDisabled:Y,borderHover:pe,borderFocus:zo,placeholderColor:So,placeholderColorDisabled:$o,lineHeightTextarea:Fo,colorDisabled:Ao,colorFocus:_o,textColorDisabled:Ro,boxShadowFocus:Bo,iconSize:Po,colorFocusWarning:Eo,boxShadowFocusWarning:To,borderWarning:ko,borderFocusWarning:Io,borderHoverWarning:Mo,colorFocusError:Lo,boxShadowFocusError:Wo,borderError:Do,borderFocusError:Vo,borderHoverError:Ho,clearSize:Oo,clearColor:No,clearColorHover:jo,clearColorPressed:Ko,iconColor:Uo,iconColorDisabled:Go,suffixTextColor:Xo,countTextColor:Yo,countTextColorDisabled:Zo,iconColorHover:qo,iconColorPressed:Jo,loadingColor:Qo,loadingColorError:er,loadingColorWarning:or,fontWeight:rr,[be("padding",e)]:nr,[be("fontSize",e)]:tr,[be("height",e)]:ar}}=b.value,{left:lr,right:ir}=_r(nr);return{"--n-bezier":o,"--n-count-text-color":Yo,"--n-count-text-color-disabled":Zo,"--n-color":a,"--n-font-size":tr,"--n-font-weight":rr,"--n-border-radius":p,"--n-height":ar,"--n-padding-left":lr,"--n-padding-right":ir,"--n-text-color":x,"--n-caret-color":f,"--n-text-decoration-color":W,"--n-border":X,"--n-border-disabled":Y,"--n-border-hover":pe,"--n-border-focus":zo,"--n-placeholder-color":So,"--n-placeholder-color-disabled":$o,"--n-icon-size":Po,"--n-line-height-textarea":Fo,"--n-color-disabled":Ao,"--n-color-focus":_o,"--n-text-color-disabled":Ro,"--n-box-shadow-focus":Bo,"--n-loading-color":Qo,"--n-caret-color-warning":L,"--n-color-focus-warning":Eo,"--n-box-shadow-focus-warning":To,"--n-border-warning":ko,"--n-border-focus-warning":Io,"--n-border-hover-warning":Mo,"--n-loading-color-warning":or,"--n-caret-color-error":M,"--n-color-focus-error":Lo,"--n-box-shadow-focus-error":Wo,"--n-border-error":Do,"--n-border-focus-error":Vo,"--n-border-hover-error":Ho,"--n-loading-color-error":er,"--n-clear-color":No,"--n-clear-size":Oo,"--n-clear-color-hover":jo,"--n-clear-color-pressed":Ko,"--n-icon-color":Uo,"--n-icon-color-hover":qo,"--n-icon-color-pressed":Jo,"--n-icon-color-disabled":Go,"--n-suffix-text-color":Xo}}),H=$?Sr("input",R(()=>{const{value:e}=O;return e[0]}),$e,r):void 0;return Object.assign(Object.assign({},yo),{wrapperElRef:d,inputElRef:l,inputMirrorElRef:i,inputEl2Ref:v,textareaElRef:c,textareaMirrorElRef:u,textareaScrollbarInstRef:S,rtlEnabled:Co,uncontrolledValue:V,mergedValue:F,passwordVisible:G,mergedPlaceholder:j,showPlaceholder1:ue,showPlaceholder2:de,mergedFocus:K,isComposing:_,activated:I,showClearButton:he,mergedSize:O,mergedDisabled:P,textDecorationStyle:Ie,mergedClsPrefix:h,mergedBordered:t,mergedShowPasswordOn:U,placeholderStyle:Se,mergedStatus:ce,textAreaScrollContainerWidth:ye,handleTextAreaScroll:wo,handleCompositionStart:Ye,handleCompositionEnd:Ze,handleInput:ee,handleInputBlur:Je,handleInputFocus:Qe,handleWrapperBlur:eo,handleWrapperFocus:oo,handleMouseEnter:lo,handleMouseLeave:io,handleMouseDown:ao,handleChange:ro,handleClick:no,handleClear:to,handlePasswordToggleClick:so,handlePasswordToggleMousedown:co,handleWrapperKeydown:ho,handleWrapperKeyup:uo,handleTextAreaMirrorResize:mo,getTextareaScrollContainer:()=>c.value,mergedTheme:b,cssVars:$?void 0:$e,themeClass:H==null?void 0:H.themeClass,onRender:H==null?void 0:H.onRender})},render(){var r,h;const{mergedClsPrefix:t,mergedStatus:$,themeClass:z,type:b,countGraphemes:d,onRender:c}=this,u=this.$slots;return c==null||c(),n("div",{ref:"wrapperElRef",class:[`${t}-input`,z,$&&`${t}-input--${$}-status`,{[`${t}-input--rtl`]:this.rtlEnabled,[`${t}-input--disabled`]:this.mergedDisabled,[`${t}-input--textarea`]:b==="textarea",[`${t}-input--resizable`]:this.resizable&&!this.autosize,[`${t}-input--autosize`]:this.autosize,[`${t}-input--round`]:this.round&&b!=="textarea",[`${t}-input--pair`]:this.pair,[`${t}-input--focus`]:this.mergedFocus,[`${t}-input--stateful`]:this.stateful}],style:this.cssVars,tabindex:!this.mergedDisabled&&this.passivelyActivated&&!this.activated?0:void 0,onFocus:this.handleWrapperFocus,onBlur:this.handleWrapperBlur,onClick:this.handleClick,onMousedown:this.handleMouseDown,onMouseenter:this.handleMouseEnter,onMouseleave:this.handleMouseLeave,onCompositionstart:this.handleCompositionStart,onCompositionend:this.handleCompositionEnd,onKeyup:this.handleWrapperKeyup,onKeydown:this.handleWrapperKeydown},n("div",{class:`${t}-input-wrapper`},te(u.prefix,i=>i&&n("div",{class:`${t}-input__prefix`},i)),b==="textarea"?n($r,{ref:"textareaScrollbarInstRef",class:`${t}-input__textarea`,container:this.getTextareaScrollContainer,triggerDisplayManually:!0,useUnifiedContainer:!0,internalHoistYRail:!0},{default:()=>{var i,l;const{textAreaScrollContainerWidth:v}=this,g={width:this.autosize&&v&&`${v}px`};return n(Fr,null,n("textarea",Object.assign({},this.inputProps,{ref:"textareaElRef",class:[`${t}-input__textarea-el`,(i=this.inputProps)===null||i===void 0?void 0:i.class],autofocus:this.autofocus,rows:Number(this.rows),placeholder:this.placeholder,value:this.mergedValue,disabled:this.mergedDisabled,maxlength:d?void 0:this.maxlength,minlength:d?void 0:this.minlength,readonly:this.readonly,tabindex:this.passivelyActivated&&!this.activated?-1:void 0,style:[this.textDecorationStyle[0],(l=this.inputProps)===null||l===void 0?void 0:l.style,g],onBlur:this.handleInputBlur,onFocus:B=>{this.handleInputFocus(B,2)},onInput:this.handleInput,onChange:this.handleChange,onScroll:this.handleTextAreaScroll})),this.showPlaceholder1?n("div",{class:`${t}-input__placeholder`,style:[this.placeholderStyle,g],key:"placeholder"},this.mergedPlaceholder[0]):null,this.autosize?n(Ar,{onResize:this.handleTextAreaMirrorResize},{default:()=>n("div",{ref:"textareaMirrorElRef",class:`${t}-input__textarea-mirror`,key:"mirror"})}):null)}}):n("div",{class:`${t}-input__input`},n("input",Object.assign({type:b==="password"&&this.mergedShowPasswordOn&&this.passwordVisible?"text":b},this.inputProps,{ref:"inputElRef",class:[`${t}-input__input-el`,(r=this.inputProps)===null||r===void 0?void 0:r.class],style:[this.textDecorationStyle[0],(h=this.inputProps)===null||h===void 0?void 0:h.style],tabindex:this.passivelyActivated&&!this.activated?-1:void 0,placeholder:this.mergedPlaceholder[0],disabled:this.mergedDisabled,maxlength:d?void 0:this.maxlength,minlength:d?void 0:this.minlength,value:Array.isArray(this.mergedValue)?this.mergedValue[0]:this.mergedValue,readonly:this.readonly,autofocus:this.autofocus,size:this.attrSize,onBlur:this.handleInputBlur,onFocus:i=>{this.handleInputFocus(i,0)},onInput:i=>{this.handleInput(i,0)},onChange:i=>{this.handleChange(i,0)}})),this.showPlaceholder1?n("div",{class:`${t}-input__placeholder`},n("span",null,this.mergedPlaceholder[0])):null,this.autosize?n("div",{class:`${t}-input__input-mirror`,key:"mirror",ref:"inputMirrorElRef"}," "):null),!this.pair&&te(u.suffix,i=>i||this.clearable||this.showCount||this.mergedShowPasswordOn||this.loading!==void 0?n("div",{class:`${t}-input__suffix`},[te(u["clear-icon-placeholder"],l=>(this.clearable||l)&&n(we,{clsPrefix:t,show:this.showClearButton,onClear:this.handleClear},{placeholder:()=>l,icon:()=>{var v,g;return(g=(v=this.$slots)["clear-icon"])===null||g===void 0?void 0:g.call(v)}})),this.internalLoadingBeforeSuffix?null:i,this.loading!==void 0?n(Ir,{clsPrefix:t,loading:this.loading,showArrow:!1,showClear:!1,style:this.cssVars}):null,this.internalLoadingBeforeSuffix?i:null,this.showCount&&this.type!=="textarea"?n(Pe,null,{default:l=>{var v;const{renderCount:g}=this;return g?g(l):(v=u.count)===null||v===void 0?void 0:v.call(u,l)}}):null,this.mergedShowPasswordOn&&this.type==="password"?n("div",{class:`${t}-input__eye`,onMousedown:this.handlePasswordToggleMousedown,onClick:this.handlePasswordToggleClick},this.passwordVisible?q(u["password-visible-icon"],()=>[n(le,{clsPrefix:t},{default:()=>n(Er,null)})]):q(u["password-invisible-icon"],()=>[n(le,{clsPrefix:t},{default:()=>n(Tr,null)})])):null]):null)),this.pair?n("span",{class:`${t}-input__separator`},q(u.separator,()=>[this.separator])):null,this.pair?n("div",{class:`${t}-input-wrapper`},n("div",{class:`${t}-input__input`},n("input",{ref:"inputEl2Ref",type:this.type,class:`${t}-input__input-el`,tabindex:this.passivelyActivated&&!this.activated?-1:void 0,placeholder:this.mergedPlaceholder[1],disabled:this.mergedDisabled,maxlength:d?void 0:this.maxlength,minlength:d?void 0:this.minlength,value:Array.isArray(this.mergedValue)?this.mergedValue[1]:void 0,readonly:this.readonly,style:this.textDecorationStyle[1],onBlur:this.handleInputBlur,onFocus:i=>{this.handleInputFocus(i,1)},onInput:i=>{this.handleInput(i,1)},onChange:i=>{this.handleChange(i,1)}}),this.showPlaceholder2?n("div",{class:`${t}-input__placeholder`},n("span",null,this.mergedPlaceholder[1])):null),te(u.suffix,i=>(this.clearable||i)&&n("div",{class:`${t}-input__suffix`},[this.clearable&&n(we,{clsPrefix:t,show:this.showClearButton,onClear:this.handleClear},{icon:()=>{var l;return(l=u["clear-icon"])===null||l===void 0?void 0:l.call(u)},placeholder:()=>{var l;return(l=u["clear-icon-placeholder"])===null||l===void 0?void 0:l.call(u)}}),i]))):null,this.mergedBordered?n("div",{class:`${t}-input__border`}):null,this.mergedBordered?n("div",{class:`${t}-input__state-border`}):null,this.showCount&&b==="textarea"?n(Pe,null,{default:i=>{var l;const{renderCount:v}=this;return v?v(i):(l=u.count)===null||l===void 0?void 0:l.call(u,i)}}):null)}});export{Br as C,Gr as N,Ir as a,Dr as i};
