var It=Object.defineProperty;var Ot=(s,t,i)=>t in s?It(s,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):s[t]=i;var h=(s,t,i)=>(Ot(s,typeof t!="symbol"?t+"":t,i),i),ut=(s,t,i)=>{if(!t.has(s))throw TypeError("Cannot "+i)};var O=(s,t,i)=>(ut(s,t,"read from private field"),i?i.call(s):t.get(s)),R=(s,t,i)=>{if(t.has(s))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(s):t.set(s,i)},Y=(s,t,i,n)=>(ut(s,t,"write to private field"),n?n.call(s,i):t.set(s,i),i);var M=(s,t,i)=>(ut(s,t,"access private method"),i);import{d as defineComponent,o as openBlock,c as createElementBlock,a as createBaseVNode,h as http,r as ref,b as computed,u as useMessage,w as watch,e as onMounted,al as onBeforeUnmount,k as createVNode,i as withCtx,j as unref,C as createCommentVNode,a5 as NTooltip,N as NButton,l as NIcon,am as SettingsOutline,p as NSpace,m as createTextVNode,a6 as NModal,B as useRoute,y as useRouter,_ as _export_sfc}from"./index.js";import{u as updateWorkflow,c as createWorkflow,g as getWorkflow}from"./workflow.js";import{u as useLoadingBar}from"./use-loading-bar.js";import{S as SaveOutline}from"./SaveOutline.js";import{R as RefreshOutline}from"./RefreshOutline.js";import{N as NForm,a as NFormItem}from"./FormItem.js";import{N as NInput}from"./Input.js";import{N as NSpin}from"./Spin.js";import"./cryptojs.js";import"./use-locale.js";import"./en-US.js";const _hoisted_1$2={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},_hoisted_2$1=createBaseVNode("path",{d:"M336 176h40a40 40 0 0 1 40 40v208a40 40 0 0 1-40 40H136a40 40 0 0 1-40-40V216a40 40 0 0 1 40-40h40",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),_hoisted_3$1=createBaseVNode("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M176 272l80 80l80-80"},null,-1),_hoisted_4=createBaseVNode("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M256 48v288"},null,-1),_hoisted_5=[_hoisted_2$1,_hoisted_3$1,_hoisted_4],DownloadOutline=defineComponent({name:"DownloadOutline",render:function(t,i){return openBlock(),createElementBlock("svg",_hoisted_1$2,_hoisted_5)}});async function listBlockTypes(){return http.get("/block/types")}async function getTypeCompatibility(){return http.get("/block/types/compatibility")}var Oe;const Se=class{constructor(t,i,n,o,r,a,l){h(this,"id");h(this,"parentId");h(this,"type");h(this,"origin_id");h(this,"origin_slot");h(this,"target_id");h(this,"target_slot");h(this,"data");h(this,"_data");h(this,"_pos");h(this,"_last_time");h(this,"path");h(this,"_centreAngle");R(this,Oe,void 0);this.id=t,this.type=i,this.origin_id=n,this.origin_slot=o,this.target_id=r,this.target_slot=a,this.parentId=l,this._data=null,this._pos=new Float32Array(2)}get color(){return O(this,Oe)}set color(t){Y(this,Oe,t===""?null:t)}static createFromArray(t){return new Se(t[0],t[5],t[1],t[2],t[3],t[4])}static create(t){return new Se(t.id,t.type,t.origin_id,t.origin_slot,t.target_id,t.target_slot,t.parentId)}static getReroutes(t,i){var n;return((n=t.reroutes.get(i.parentId))==null?void 0:n.getReroutes())??[]}static findNextReroute(t,i,n){var o;return(o=t.reroutes.get(i.parentId))==null?void 0:o.findNextReroute(n)}configure(t){Array.isArray(t)?(this.id=t[0],this.origin_id=t[1],this.origin_slot=t[2],this.target_id=t[3],this.target_slot=t[4],this.type=t[5]):(this.id=t.id,this.type=t.type,this.origin_id=t.origin_id,this.origin_slot=t.origin_slot,this.target_id=t.target_id,this.target_slot=t.target_slot,this.parentId=t.parentId)}disconnect(t,i){const n=Se.getReroutes(t,this);for(const o of n)o.linkIds.delete(this.id),!i&&!o.linkIds.size&&t.reroutes.delete(o.id);t.links.delete(this.id)}serialize(){return[this.id,this.origin_id,this.origin_slot,this.target_id,this.target_slot,this.type]}asSerialisable(){const t={id:this.id,origin_id:this.origin_id,origin_slot:this.origin_slot,target_id:this.target_id,target_slot:this.target_slot,type:this.type};return this.parentId&&(t.parentId=this.parentId),t}};let LLink=Se;Oe=new WeakMap;var NodeSlotType=(s=>(s[s.INPUT=1]="INPUT",s[s.OUTPUT=2]="OUTPUT",s))(NodeSlotType||{}),RenderShape=(s=>(s[s.BOX=1]="BOX",s[s.ROUND=2]="ROUND",s[s.CIRCLE=3]="CIRCLE",s[s.CARD=4]="CARD",s[s.ARROW=5]="ARROW",s[s.GRID=6]="GRID",s[s.HollowCircle=7]="HollowCircle",s))(RenderShape||{}),CanvasItem=(s=>(s[s.Nothing=0]="Nothing",s[s.Node=1]="Node",s[s.Group=2]="Group",s[s.Reroute=4]="Reroute",s[s.Link=8]="Link",s[s.ResizeSe=16]="ResizeSe",s))(CanvasItem||{}),LinkDirection=(s=>(s[s.NONE=0]="NONE",s[s.UP=1]="UP",s[s.DOWN=2]="DOWN",s[s.LEFT=3]="LEFT",s[s.RIGHT=4]="RIGHT",s[s.CENTER=5]="CENTER",s))(LinkDirection||{}),LinkRenderType=(s=>(s[s.HIDDEN_LINK=-1]="HIDDEN_LINK",s[s.STRAIGHT_LINK=0]="STRAIGHT_LINK",s[s.LINEAR_LINK=1]="LINEAR_LINK",s[s.SPLINE_LINK=2]="SPLINE_LINK",s))(LinkRenderType||{}),LinkMarkerShape=(s=>(s[s.None=0]="None",s[s.Circle=1]="Circle",s[s.Arrow=2]="Arrow",s))(LinkMarkerShape||{}),TitleMode=(s=>(s[s.NORMAL_TITLE=0]="NORMAL_TITLE",s[s.NO_TITLE=1]="NO_TITLE",s[s.TRANSPARENT_TITLE=2]="TRANSPARENT_TITLE",s[s.AUTOHIDE_TITLE=3]="AUTOHIDE_TITLE",s))(TitleMode||{}),LGraphEventMode=(s=>(s[s.ALWAYS=0]="ALWAYS",s[s.ON_EVENT=1]="ON_EVENT",s[s.NEVER=2]="NEVER",s[s.ON_TRIGGER=3]="ON_TRIGGER",s[s.BYPASS=4]="BYPASS",s))(LGraphEventMode||{}),EaseFunction=(s=>(s.LINEAR="linear",s.EASE_IN_QUAD="easeInQuad",s.EASE_OUT_QUAD="easeOutQuad",s.EASE_IN_OUT_QUAD="easeInOutQuad",s))(EaseFunction||{});function distance(s,t){return Math.sqrt((t[0]-s[0])*(t[0]-s[0])+(t[1]-s[1])*(t[1]-s[1]))}function dist2(s,t,i,n){return(i-s)*(i-s)+(n-t)*(n-t)}function isInRectangle(s,t,i,n,o,r){return s>=i&&s<i+o&&t>=n&&t<n+r}function isPointInRect(s,t){return s[0]>=t[0]&&s[0]<t[0]+t[2]&&s[1]>=t[1]&&s[1]<t[1]+t[3]}function isInRect(s,t,i){return s>=i[0]&&s<i[0]+i[2]&&t>=i[1]&&t<i[1]+i[3]}function isInsideRectangle(s,t,i,n,o,r){return i<s&&i+o>s&&n<t&&n+r>t}function isSortaInsideOctagon(s,t,i){return Math.min(i,Math.abs(s))+Math.min(i,Math.abs(t))<i*.75}function overlapBounding(s,t){const i=s[0]+s[2],n=s[1]+s[3],o=t[0]+t[2],r=t[1]+t[3];return!(s[0]>o||s[1]>r||i<t[0]||n<t[1])}function containsCentre(s,t){const i=t[0]+t[2]*.5,n=t[1]+t[3]*.5;return isInRect(i,n,s)}function containsRect(s,t){const i=s[0]+s[2],n=s[1]+s[3],o=t[0]+t[2],r=t[1]+t[3];return!(s[0]===t[0]&&s[1]===t[1]&&i===o&&n===r)&&s[0]<=t[0]&&s[1]<=t[1]&&i>=o&&n>=r}function findPointOnCurve(s,t,i,n,o,r=.5){const a=1-r,l=a*a*a,u=3*(a*a)*r,p=3*a*(r*r),d=r*r*r;s[0]=l*t[0]+u*n[0]+p*o[0]+d*i[0],s[1]=l*t[1]+u*n[1]+p*o[1]+d*i[1]}function createBounds(s,t=10){const i=new Float32Array([1/0,1/0,-1/0,-1/0]);for(const n of s){const o=n.boundingRect;i[0]=Math.min(i[0],o[0]),i[1]=Math.min(i[1],o[1]),i[2]=Math.max(i[2],o[0]+o[2]),i[3]=Math.max(i[3],o[1]+o[3])}return i.every(n=>isFinite(n))?[i[0]-t,i[1]-t,i[2]-i[0]+2*t,i[3]-i[1]+2*t]:null}function snapPoint(s,t){return t?(s[0]=t*Math.round(s[0]/t),s[1]=t*Math.round(s[1]/t),!0):!1}var de,oe,J,j,Ge,Ve;const ue=class{constructor(t,i,n,o,r){R(this,de,new Float32Array(8));R(this,oe,void 0);R(this,J,void 0);R(this,j,O(this,de).subarray(0,2));h(this,"selected");h(this,"linkIds");h(this,"otherAngle",0);h(this,"cos",0);h(this,"sin",0);h(this,"controlPoint",O(this,de).subarray(4,6));h(this,"path");h(this,"_centreAngle");h(this,"_pos",O(this,de).subarray(6,8));h(this,"_colour");R(this,Ge,-1/0);R(this,Ve,O(this,de).subarray(2,4));this.id=t,Y(this,oe,new WeakRef(i)),this.update(o,n,r),this.linkIds??(this.linkIds=new Set)}get parentId(){return O(this,J)}set parentId(t){t!==this.id&&this.getReroutes()!==null&&Y(this,J,t)}get pos(){return O(this,j)}set pos(t){if(!((t==null?void 0:t.length)>=2))throw new TypeError("Reroute.pos is an x,y point, and expects an indexable with at least two values.");O(this,j)[0]=t[0],O(this,j)[1]=t[1]}get boundingRect(){const{radius:t}=ue,[i,n]=O(this,j);return[i-t,n-t,2*t,2*t]}get origin_id(){var t,i;return(i=(t=O(this,oe).deref())==null?void 0:t.links.get(this.linkIds.values().next().value))==null?void 0:i.origin_id}get origin_slot(){var t,i;return(i=(t=O(this,oe).deref())==null?void 0:t.links.get(this.linkIds.values().next().value))==null?void 0:i.origin_slot}update(t,i,n){this.parentId=t,i&&(this.pos=i),n&&(this.linkIds=new Set(n))}validateLinks(t){const{linkIds:i}=this;for(const n of i)t.get(n)||i.delete(n);return i.size>0}getReroutes(t=new Set){var o;if(O(this,J)===void 0)return[this];if(t.has(this))return null;t.add(this);const i=(o=O(this,oe).deref())==null?void 0:o.reroutes.get(O(this,J));if(!i)return Y(this,J,void 0),[this];const n=i.getReroutes(t);return n==null||n.push(this),n}findNextReroute(t,i=new Set){var n,o;return O(this,J)===t?this:i.has(this)?null:(i.add(this),(o=(n=O(this,oe).deref())==null?void 0:n.reroutes.get(O(this,J)))==null?void 0:o.findNextReroute(t,i))}move(t,i){O(this,j)[0]+=t,O(this,j)[1]+=i}snapToGrid(t){if(!t)return!1;const{pos:i}=this;return i[0]=t*Math.round(i[0]/t),i[1]=t*Math.round(i[1]/t),!0}calculateAngle(t,i,n){var m,w;if(!(t>O(this,Ge)))return;Y(this,Ge,t);const{links:o}=i,{linkIds:r,id:a}=this,l=[];let u=0;for(const b of r){const k=o.get(b);if(!k)continue;const L=((m=LLink.findNextReroute(i,k,a))==null?void 0:m.pos)??((w=i.getNodeById(k.target_id))==null?void 0:w.getConnectionPos(!0,k.target_slot,O(this,Ve)));if(!L)continue;const T=Math.atan2(L[1]-O(this,j)[1],L[0]-O(this,j)[0]);l.push(T),u+=T}if(!l.length)return;u/=l.length;const p=Math.atan2(O(this,j)[1]-n[1],O(this,j)[0]-n[0]);let d=(p-u)*.5;Math.abs(d)>Math.PI*.5&&(d+=Math.PI);const c=Math.min(80,distance(n,O(this,j))*.25),_=p-d,g=Math.cos(_),f=Math.sin(_);this.otherAngle=_,this.cos=g,this.sin=f,this.controlPoint[0]=c*-g,this.controlPoint[1]=c*-f}draw(t){const{pos:i}=this;t.fillStyle=this._colour,t.beginPath(),t.arc(i[0],i[1],ue.radius,0,2*Math.PI),t.fill(),t.lineWidth=ue.radius*.1,t.strokeStyle="rgb(0,0,0,0.5)",t.stroke(),t.fillStyle="#ffffff55",t.strokeStyle="rgb(0,0,0,0.3)",t.beginPath(),t.arc(i[0],i[1],ue.radius*.8,0,2*Math.PI),t.fill(),t.stroke(),this.selected&&(t.strokeStyle="#fff",t.beginPath(),t.arc(i[0],i[1],ue.radius*1.2,0,2*Math.PI),t.stroke())}asSerialisable(){return{id:this.id,parentId:this.parentId,pos:[this.pos[0],this.pos[1]],linkIds:[...this.linkIds]}}};let Reroute=ue;de=new WeakMap,oe=new WeakMap,J=new WeakMap,j=new WeakMap,Ge=new WeakMap,Ve=new WeakMap,h(Reroute,"radius",10);var BadgePosition=(s=>(s.TopLeft="top-left",s.TopRight="top-right",s))(BadgePosition||{});class LGraphBadge{constructor({text:t,fgColor:i="white",bgColor:n="#0F1F0F",fontSize:o=12,padding:r=6,height:a=20,cornerRadius:l=5}){h(this,"text");h(this,"fgColor");h(this,"bgColor");h(this,"fontSize");h(this,"padding");h(this,"height");h(this,"cornerRadius");this.text=t,this.fgColor=i,this.bgColor=n,this.fontSize=o,this.padding=r,this.height=a,this.cornerRadius=l}get visible(){return this.text.length>0}getWidth(t){if(!this.visible)return 0;const{font:i}=t;t.font=`${this.fontSize}px sans-serif`;const n=t.measureText(this.text).width;return t.font=i,n+this.padding*2}draw(t,i,n){if(!this.visible)return;const{fillStyle:o}=t;t.font=`${this.fontSize}px sans-serif`;const r=this.getWidth(t),a=0;t.fillStyle=this.bgColor,t.beginPath(),t.roundRect?t.roundRect(i+a,n,r,this.height,this.cornerRadius):t.rect(i+a,n,r,this.height),t.fill(),t.fillStyle=this.fgColor,t.fillText(this.text,i+a+this.padding,n+this.height-this.padding),t.fillStyle=o}}class BaseWidget{constructor(t){h(this,"linkedWidgets");h(this,"options");h(this,"marker");h(this,"label");h(this,"clicked");h(this,"name");h(this,"type");h(this,"value");h(this,"y");h(this,"last_y");h(this,"width");h(this,"disabled");h(this,"hidden");h(this,"advanced");h(this,"tooltip");h(this,"element");Object.assign(this,t),this.options=t.options}get outline_color(){return this.advanced?LiteGraph.WIDGET_ADVANCED_OUTLINE_COLOR:LiteGraph.WIDGET_OUTLINE_COLOR}get background_color(){return LiteGraph.WIDGET_BGCOLOR}get height(){return LiteGraph.NODE_WIDGET_HEIGHT}get text_color(){return LiteGraph.WIDGET_TEXT_COLOR}get secondary_text_color(){return LiteGraph.WIDGET_SECONDARY_TEXT_COLOR}}class BooleanWidget extends BaseWidget{constructor(t){super(t),this.type="toggle",this.value=t.value}drawWidget(t,i){const{y:n,width:o,show_text:r=!0,margin:a=15}=i,l=o,u=this.height;if(t.textAlign="left",t.strokeStyle=this.outline_color,t.fillStyle=this.background_color,t.beginPath(),r?t.roundRect(a,n,l-a*2,u,[u*.5]):t.rect(a,n,l-a*2,u),t.fill(),r&&!this.disabled&&t.stroke(),t.fillStyle=this.value?"#89A":"#333",t.beginPath(),t.arc(l-a*2,n+u*.5,u*.36,0,Math.PI*2),t.fill(),r){t.fillStyle=this.secondary_text_color;const p=this.label||this.name;p!=null&&t.fillText(p,a*2,n+u*.7),t.fillStyle=this.value?this.text_color:this.secondary_text_color,t.textAlign="right",t.fillText(this.value?this.options.on||"true":this.options.off||"false",l-40,n+u*.7)}}}var SlotType=(s=>(s.Array="array",s[s.Event=-1]="Event",s))(SlotType||{}),SlotShape=(s=>(s[s.Box=RenderShape.BOX]="Box",s[s.Arrow=RenderShape.ARROW]="Arrow",s[s.Grid=RenderShape.GRID]="Grid",s[s.Circle=RenderShape.CIRCLE]="Circle",s[s.HollowCircle=RenderShape.HollowCircle]="HollowCircle",s))(SlotShape||{}),SlotDirection=(s=>(s[s.Up=LinkDirection.UP]="Up",s[s.Right=LinkDirection.RIGHT]="Right",s[s.Down=LinkDirection.DOWN]="Down",s[s.Left=LinkDirection.LEFT]="Left",s))(SlotDirection||{}),LabelPosition=(s=>(s.Left="left",s.Right="right",s))(LabelPosition||{});function strokeShape(s,t,i={}){const{shape:n=RenderShape.BOX,round_radius:o=LiteGraph.ROUND_RADIUS,title_height:r=LiteGraph.NODE_TITLE_HEIGHT,title_mode:a=TitleMode.NORMAL_TITLE,colour:l=LiteGraph.NODE_BOX_OUTLINE_COLOR,padding:u=6,collapsed:p=!1,thickness:d=1}=i;a===TitleMode.TRANSPARENT_TITLE&&(t[1]-=r,t[3]+=r);const{lineWidth:c,strokeStyle:_}=s;s.lineWidth=d,s.globalAlpha=.8,s.strokeStyle=l,s.beginPath();const[g,f,m,w]=t;switch(n){case RenderShape.BOX:{s.rect(g-u,f-u,m+2*u,w+2*u);break}case RenderShape.ROUND:case RenderShape.CARD:{const b=o+u,L=n===RenderShape.CARD&&p||n===RenderShape.ROUND?[b]:[b,2,b,2];s.roundRect(g-u,f-u,m+2*u,w+2*u,L);break}case RenderShape.CIRCLE:{const b=g+m/2,k=f+w/2,L=Math.max(m,w)/2+u;s.arc(b,k,L,0,Math.PI*2);break}}s.stroke(),s.lineWidth=c,s.strokeStyle=_,s.globalAlpha=1}class NodeSlot{constructor(t){h(this,"name");h(this,"localized_name");h(this,"label");h(this,"type");h(this,"dir");h(this,"removable");h(this,"shape");h(this,"color_off");h(this,"color_on");h(this,"locked");h(this,"nameLocked");h(this,"pos");h(this,"widget");Object.assign(this,t),this.name=t.name,this.type=t.type}get renderingLabel(){return this.label||this.localized_name||this.name||""}connectedColor(t){return this.color_on||t.default_connection_color_byType[this.type]||t.default_connection_color.output_on}disconnectedColor(t){return this.color_off||t.default_connection_color_byTypeOff[this.type]||t.default_connection_color_byType[this.type]||t.default_connection_color.output_off}renderingColor(t){return this.isConnected()?this.connectedColor(t):this.disconnectedColor(t)}draw(t,i){const{pos:n,colorContext:o,labelColor:r="#AAA",labelPosition:a=LabelPosition.Right,horizontal:l=!1,lowQuality:u=!1,renderText:p=!0,highlight:d=!1,doStroke:c=!1}=i,_=t.fillStyle,g=t.strokeStyle,f=t.lineWidth,m=this.type,w=m===SlotType.Array?SlotShape.Grid:this.shape;t.beginPath();let b=c,k=!0;if(t.fillStyle=this.renderingColor(o),t.lineWidth=1,m===SlotType.Event||w===SlotShape.Box)l?t.rect(n[0]-5+.5,n[1]-8+.5,10,14):t.rect(n[0]-6+.5,n[1]-5+.5,14,10);else if(w===SlotShape.Arrow)t.moveTo(n[0]+8,n[1]+.5),t.lineTo(n[0]-4,n[1]+6+.5),t.lineTo(n[0]-4,n[1]-6+.5),t.closePath();else if(w===SlotShape.Grid){for(let E=0;E<3;E++)for(let C=0;C<3;C++)t.rect(n[0]-4+E*3,n[1]-4+C*3,2,2);b=!1}else if(u)t.rect(n[0]-4,n[1]-4,8,8);else{let L;w===SlotShape.HollowCircle?(k=!1,b=!0,t.lineWidth=3,t.strokeStyle=t.fillStyle,L=d?4:3):L=d?5:4,t.arc(n[0],n[1],L,0,Math.PI*2)}if(k&&t.fill(),!u&&b&&t.stroke(),p){const L=this.renderingLabel;L&&(t.fillStyle=r,a===LabelPosition.Right?l||this.dir==LinkDirection.UP?t.fillText(L,n[0],n[1]-10):t.fillText(L,n[0]+10,n[1]+5):l||this.dir==LinkDirection.DOWN?t.fillText(L,n[0],n[1]-8):t.fillText(L,n[0]-10,n[1]+5))}t.fillStyle=_,t.strokeStyle=g,t.lineWidth=f}}class NodeInputSlot extends NodeSlot{constructor(i){super(i);h(this,"link");this.link=i.link}isConnected(){return this.link!=null}draw(i,n){const o=i.textAlign;i.textAlign=n.horizontal?"center":"left",super.draw(i,{...n,doStroke:!1}),i.textAlign=o}}class NodeOutputSlot extends NodeSlot{constructor(i){super(i);h(this,"links");h(this,"_data");h(this,"slot_index");this.links=i.links,this._data=i._data,this.slot_index=i.slot_index}isConnected(){return this.links!=null&&this.links.length>0}draw(i,n){const o=i.textAlign,r=i.strokeStyle;i.textAlign=n.horizontal?"center":"right",i.strokeStyle="black",super.draw(i,{...n,doStroke:!0}),i.textAlign=o,i.strokeStyle=r}}var De,Ae,Re,ct,pe,Ee;const Ie=class{constructor(t){R(this,Re);R(this,pe);h(this,"title");h(this,"graph",null);h(this,"id");h(this,"type",null);h(this,"inputs",[]);h(this,"outputs",[]);h(this,"connections",[]);h(this,"properties",{});h(this,"properties_info",[]);h(this,"flags",{});h(this,"widgets");h(this,"locked");h(this,"order");h(this,"mode");h(this,"last_serialization");h(this,"serialize_widgets");h(this,"color");h(this,"bgcolor");h(this,"boxcolor");h(this,"exec_version");h(this,"action_call");h(this,"execute_triggered");h(this,"action_triggered");h(this,"widgets_up");h(this,"widgets_start_y");h(this,"lostFocusAt");h(this,"gotFocusAt");h(this,"badges",[]);h(this,"badgePosition",BadgePosition.TopLeft);h(this,"_collapsed_width");h(this,"horizontal");h(this,"console");h(this,"_level");h(this,"_shape");h(this,"mouseOver");h(this,"redraw_on_mouse");h(this,"optional_inputs");h(this,"optional_outputs");h(this,"resizable");h(this,"clonable");h(this,"_relative_id");h(this,"clip_area");h(this,"ignore_remove");h(this,"has_errors");h(this,"removable");h(this,"block_delete");h(this,"selected");h(this,"showAdvanced");R(this,De,new Float32Array(4));R(this,Ae,new Float32Array(4));h(this,"_posSize",new Float32Array(4));h(this,"_pos",this._posSize.subarray(0,2));h(this,"_size",this._posSize.subarray(2,4));this.id=LiteGraph.use_uuids?LiteGraph.uuidv4():-1,this.title=t||"Unnamed",this.size=[LiteGraph.NODE_WIDTH,60],this.pos=[10,10]}get titleFontStyle(){return`${LiteGraph.NODE_TEXT_SIZE}px Arial`}get renderingColor(){return this.color||this.constructor.color||LiteGraph.NODE_DEFAULT_COLOR}get renderingBgColor(){return this.bgcolor||this.constructor.bgcolor||LiteGraph.NODE_DEFAULT_BGCOLOR}get renderingBoxColor(){let t=LiteGraph.node_box_coloured_by_mode&&LiteGraph.NODE_MODES_COLORS[this.mode]?LiteGraph.NODE_MODES_COLORS[this.mode]:void 0;return LiteGraph.node_box_coloured_when_on&&(t=this.action_triggered?"#FFF":this.execute_triggered?"#AAA":t),this.boxcolor||t||LiteGraph.NODE_DEFAULT_BOXCOLOR}get renderArea(){return O(this,De)}get boundingRect(){return O(this,Ae)}get pos(){return this._pos}set pos(t){!t||t.length<2||(this._pos[0]=t[0],this._pos[1]=t[1])}get size(){return this._size}set size(t){!t||t.length<2||(this._size[0]=t[0],this._size[1]=t[1])}get renderingSize(){return this.flags.collapsed?[this._collapsed_width,0]:this._size}get shape(){return this._shape}set shape(t){switch(t){case"default":delete this._shape;break;case"box":this._shape=RenderShape.BOX;break;case"round":this._shape=RenderShape.ROUND;break;case"circle":this._shape=RenderShape.CIRCLE;break;case"card":this._shape=RenderShape.CARD;break;default:this._shape=t}}get renderingShape(){return this._shape||this.constructor.shape||LiteGraph.NODE_DEFAULT_SHAPE}get is_selected(){return this.selected}set is_selected(t){this.selected=t}get title_mode(){return this.constructor.title_mode??TitleMode.NORMAL_TITLE}configure(t){var i,n,o,r,a,l,u,p,d;this.graph&&this.graph._version++;for(const c in t){if(c=="properties"){for(const _ in t.properties)this.properties[_]=t.properties[_],(i=this.onPropertyChanged)==null||i.call(this,_,t.properties[_]);continue}t[c]!=null&&(typeof t[c]=="object"?(n=this[c])!=null&&n.configure?(o=this[c])==null||o.configure(t[c]):this[c]=LiteGraph.cloneObject(t[c],this[c]):this[c]=t[c])}if(t.title||(this.title=this.constructor.title),this.inputs)for(let c=0;c<this.inputs.length;++c){const _=this.inputs[c],g=this.graph?this.graph._links.get(_.link):null;(r=this.onConnectionsChange)==null||r.call(this,NodeSlotType.INPUT,c,!0,g,_),(a=this.onInputAdded)==null||a.call(this,_)}if(this.outputs)for(let c=0;c<this.outputs.length;++c){const _=this.outputs[c];if(_.links){for(let g=0;g<_.links.length;++g){const f=this.graph?this.graph._links.get(_.links[g]):null;(l=this.onConnectionsChange)==null||l.call(this,NodeSlotType.OUTPUT,c,!0,f,_)}(u=this.onOutputAdded)==null||u.call(this,_)}}if(this.widgets){for(let c=0;c<this.widgets.length;++c){const _=this.widgets[c];_&&(p=_.options)!=null&&p.property&&this.properties[_.options.property]!=null&&(_.value=JSON.parse(JSON.stringify(this.properties[_.options.property])))}if(t.widgets_values)for(let c=0;c<t.widgets_values.length;++c)this.widgets[c]&&(this.widgets[c].value=t.widgets_values[c])}this.pinned&&this.pin(!0),(d=this.onConfigure)==null||d.call(this,t)}serialize(){var i;const t={id:this.id,type:this.type,pos:[this.pos[0],this.pos[1]],size:[this.size[0],this.size[1]],flags:LiteGraph.cloneObject(this.flags),order:this.order,mode:this.mode,showAdvanced:this.showAdvanced};if(this.constructor===Ie&&this.last_serialization)return this.last_serialization;if(this.inputs&&(t.inputs=this.inputs),this.outputs){for(let n=0;n<this.outputs.length;n++)delete this.outputs[n]._data;t.outputs=this.outputs}if(this.title&&this.title!=this.constructor.title&&(t.title=this.title),this.properties&&(t.properties=LiteGraph.cloneObject(this.properties)),this.widgets&&this.serialize_widgets){t.widgets_values=[];for(let n=0;n<this.widgets.length;++n)this.widgets[n]?t.widgets_values[n]=this.widgets[n].value:t.widgets_values[n]=null}return t.type||(t.type=this.constructor.type),this.color&&(t.color=this.color),this.bgcolor&&(t.bgcolor=this.bgcolor),this.boxcolor&&(t.boxcolor=this.boxcolor),this.shape&&(t.shape=this.shape),(i=this.onSerialize)!=null&&i.call(this,t)&&console.warn("node onSerialize shouldnt return anything, data should be stored in the object pass in the first parameter"),t}clone(){const t=LiteGraph.createNode(this.type);if(!t)return null;const i=LiteGraph.cloneObject(this.serialize());if(i.inputs)for(let n=0;n<i.inputs.length;++n)i.inputs[n].link=null;if(i.outputs)for(let n=0;n<i.outputs.length;++n)i.outputs[n].links&&(i.outputs[n].links.length=0);return delete i.id,LiteGraph.use_uuids&&(i.id=LiteGraph.uuidv4()),t.configure(i),t}toString(){return JSON.stringify(this.serialize())}getTitle(){return this.title||this.constructor.title}setProperty(t,i){var o;if(this.properties||(this.properties={}),i===this.properties[t])return;const n=this.properties[t];if(this.properties[t]=i,((o=this.onPropertyChanged)==null?void 0:o.call(this,t,i,n))===!1&&(this.properties[t]=n),this.widgets)for(let r=0;r<this.widgets.length;++r){const a=this.widgets[r];if(a&&a.options.property==t){a.value=i;break}}}setOutputData(t,i){if(!this.outputs||t==-1||t>=this.outputs.length)return;const n=this.outputs[t];if(n&&(n._data=i,this.outputs[t].links))for(let o=0;o<this.outputs[t].links.length;o++){const r=this.outputs[t].links[o],a=this.graph._links.get(r);a&&(a.data=i)}}setOutputDataType(t,i){if(!this.outputs||t==-1||t>=this.outputs.length)return;const n=this.outputs[t];if(n&&(n.type=i,this.outputs[t].links))for(let o=0;o<this.outputs[t].links.length;o++){const r=this.outputs[t].links[o];this.graph._links.get(r).type=i}}getInputData(t,i){var a;if(!this.inputs||t>=this.inputs.length||this.inputs[t].link==null)return;const n=this.inputs[t].link,o=this.graph._links.get(n);if(!o)return null;if(!i)return o.data;const r=this.graph.getNodeById(o.origin_id);return r&&(r.updateOutputData?r.updateOutputData(o.origin_slot):(a=r.onExecute)==null||a.call(r)),o.data}getInputDataType(t){if(!this.inputs||t>=this.inputs.length||this.inputs[t].link==null)return null;const i=this.inputs[t].link,n=this.graph._links.get(i);if(!n)return null;const o=this.graph.getNodeById(n.origin_id);if(!o)return n.type;const r=o.outputs[n.origin_slot];return r?r.type:null}getInputDataByName(t,i){const n=this.findInputSlot(t);return n==-1?null:this.getInputData(n,i)}isInputConnected(t){return this.inputs?t<this.inputs.length&&this.inputs[t].link!=null:!1}getInputInfo(t){return!this.inputs||!(t<this.inputs.length)?null:this.inputs[t]}getInputLink(t){if(!this.inputs)return null;if(t<this.inputs.length){const i=this.inputs[t];return this.graph._links.get(i.link)}return null}getInputNode(t){if(!this.inputs||t>=this.inputs.length)return null;const i=this.inputs[t];if(!i||i.link===null)return null;const n=this.graph._links.get(i.link);return n?this.graph.getNodeById(n.origin_id):null}getInputOrProperty(t){if(!this.inputs||!this.inputs.length)return this.properties?this.properties[t]:null;for(let i=0,n=this.inputs.length;i<n;++i){const o=this.inputs[i];if(t==o.name&&o.link!=null){const r=this.graph._links.get(o.link);if(r)return r.data}}return this.properties[t]}getOutputData(t){return!this.outputs||t>=this.outputs.length?null:this.outputs[t]._data}getOutputInfo(t){return!this.outputs||!(t<this.outputs.length)?null:this.outputs[t]}isOutputConnected(t){var i;return this.outputs?t<this.outputs.length&&((i=this.outputs[t].links)==null?void 0:i.length)>0:!1}isAnyOutputConnected(){if(!this.outputs)return!1;for(let t=0;t<this.outputs.length;++t)if(this.outputs[t].links&&this.outputs[t].links.length)return!0;return!1}getOutputNodes(t){if(!this.outputs||this.outputs.length==0||t>=this.outputs.length)return null;const i=this.outputs[t];if(!i.links||i.links.length==0)return null;const n=[];for(let o=0;o<i.links.length;o++){const r=i.links[o],a=this.graph._links.get(r);if(a){const l=this.graph.getNodeById(a.target_id);l&&n.push(l)}}return n}addOnTriggerInput(){const t=this.findInputSlot("onTrigger");return t==-1?(this.addInput("onTrigger",LiteGraph.EVENT,{optional:!0,nameLocked:!0}),this.findInputSlot("onTrigger")):t}addOnExecutedOutput(){const t=this.findOutputSlot("onExecuted");return t==-1?(this.addOutput("onExecuted",LiteGraph.ACTION,{optional:!0,nameLocked:!0}),this.findOutputSlot("onExecuted")):t}onAfterExecuteNode(t,i){const n=this.findOutputSlot("onExecuted");n!=-1&&this.triggerSlot(n,t,null,i)}changeMode(t){switch(t){case LGraphEventMode.ON_EVENT:break;case LGraphEventMode.ON_TRIGGER:this.addOnTriggerInput(),this.addOnExecutedOutput();break;case LGraphEventMode.NEVER:break;case LGraphEventMode.ALWAYS:break;case LiteGraph.ON_REQUEST:break;default:return!1}return this.mode=t,!0}doExecute(t,i){var n;i=i||{},this.onExecute&&(i.action_call||(i.action_call=this.id+"_exec_"+Math.floor(Math.random()*9999)),this.graph.nodes_executing[this.id]=!0,this.onExecute(t,i),this.graph.nodes_executing[this.id]=!1,this.exec_version=this.graph.iteration,i!=null&&i.action_call&&(this.action_call=i.action_call,this.graph.nodes_executedAction[this.id]=i.action_call)),this.execute_triggered=2,(n=this.onAfterExecuteNode)==null||n.call(this,t,i)}actionDo(t,i,n){var o;n=n||{},this.onAction&&(n.action_call||(n.action_call=this.id+"_"+(t||"action")+"_"+Math.floor(Math.random()*9999)),this.graph.nodes_actioning[this.id]=t||"actioning",this.onAction(t,i,n),this.graph.nodes_actioning[this.id]=!1,n!=null&&n.action_call&&(this.action_call=n.action_call,this.graph.nodes_executedAction[this.id]=n.action_call)),this.action_triggered=2,(o=this.onAfterExecuteNode)==null||o.call(this,i,n)}trigger(t,i,n){if(!(!this.outputs||!this.outputs.length)){this.graph&&(this.graph._last_trigger_time=LiteGraph.getTime());for(let o=0;o<this.outputs.length;++o){const r=this.outputs[o];!r||r.type!==LiteGraph.EVENT||t&&r.name!=t||this.triggerSlot(o,i,null,n)}}}triggerSlot(t,i,n,o){var l;if(o=o||{},!this.outputs)return;if(t==null){console.error("slot must be a number");return}typeof t!="number"&&console.warn("slot must be a number, use node.trigger('name') if you want to use a string");const r=this.outputs[t];if(!r)return;const a=r.links;if(!(!a||!a.length)){this.graph&&(this.graph._last_trigger_time=LiteGraph.getTime());for(let u=0;u<a.length;++u){const p=a[u];if(n!=null&&n!=p)continue;const d=this.graph._links.get(p);if(!d)continue;d._last_time=LiteGraph.getTime();const c=this.graph.getNodeById(d.target_id);if(c){if(c.mode===LGraphEventMode.ON_TRIGGER)o.action_call||(o.action_call=this.id+"_trigg_"+Math.floor(Math.random()*9999)),(l=c.doExecute)==null||l.call(c,i,o);else if(c.onAction){o.action_call||(o.action_call=this.id+"_act_"+Math.floor(Math.random()*9999));const _=c.inputs[d.target_slot];c.actionDo(_.name,i,o)}}}}}clearTriggeredSlot(t,i){if(!this.outputs)return;const n=this.outputs[t];if(!n)return;const o=n.links;if(!(!o||!o.length))for(let r=0;r<o.length;++r){const a=o[r];if(i!=null&&i!=a)continue;const l=this.graph._links.get(a);l&&(l._last_time=0)}}setSize(t){var i;this.size=t,(i=this.onResize)==null||i.call(this,this.size)}addProperty(t,i,n,o){const r={name:t,type:n,default_value:i};if(o)for(const a in o)r[a]=o[a];return this.properties_info||(this.properties_info=[]),this.properties_info.push(r),this.properties||(this.properties={}),this.properties[t]=i,r}addOutput(t,i,n){var r;const o=new NodeOutputSlot({name:t,type:i,links:null});if(n)for(const a in n)o[a]=n[a];return this.outputs||(this.outputs=[]),this.outputs.push(o),(r=this.onOutputAdded)==null||r.call(this,o),LiteGraph.auto_load_slot_types&&LiteGraph.registerNodeAndSlotType(this,i,!0),this.setSize(this.computeSize()),this.setDirtyCanvas(!0,!0),o}addOutputs(t){var i;for(let n=0;n<t.length;++n){const o=t[n],r=new NodeOutputSlot({name:o[0],type:o[1],links:null});if(t[2])for(const a in o[2])r[a]=o[2][a];this.outputs||(this.outputs=[]),this.outputs.push(r),(i=this.onOutputAdded)==null||i.call(this,r),LiteGraph.auto_load_slot_types&&LiteGraph.registerNodeAndSlotType(this,o[1],!0)}this.setSize(this.computeSize()),this.setDirtyCanvas(!0,!0)}removeOutput(t){var i;this.disconnectOutput(t),this.outputs.splice(t,1);for(let n=t;n<this.outputs.length;++n){if(!this.outputs[n]||!this.outputs[n].links)continue;const o=this.outputs[n].links;for(let r=0;r<o.length;++r){const a=this.graph._links.get(o[r]);a&&(a.origin_slot-=1)}}this.setSize(this.computeSize()),(i=this.onOutputRemoved)==null||i.call(this,t),this.setDirtyCanvas(!0,!0)}addInput(t,i,n){var r;i=i||0;const o=new NodeInputSlot({name:t,type:i,link:null});if(n)for(const a in n)o[a]=n[a];return this.inputs||(this.inputs=[]),this.inputs.push(o),this.setSize(this.computeSize()),(r=this.onInputAdded)==null||r.call(this,o),LiteGraph.registerNodeAndSlotType(this,i),this.setDirtyCanvas(!0,!0),o}addInputs(t){var i;for(let n=0;n<t.length;++n){const o=t[n],r=new NodeInputSlot({name:o[0],type:o[1],link:null});if(t[2])for(const a in o[2])r[a]=o[2][a];this.inputs||(this.inputs=[]),this.inputs.push(r),(i=this.onInputAdded)==null||i.call(this,r),LiteGraph.registerNodeAndSlotType(this,o[1])}this.setSize(this.computeSize()),this.setDirtyCanvas(!0,!0)}removeInput(t){var n;this.disconnectInput(t);const i=this.inputs.splice(t,1);for(let o=t;o<this.inputs.length;++o){if(!this.inputs[o])continue;const r=this.graph._links.get(this.inputs[o].link);r&&(r.target_slot-=1)}this.setSize(this.computeSize()),(n=this.onInputRemoved)==null||n.call(this,t,i[0]),this.setDirtyCanvas(!0,!0)}addConnection(t,i,n,o){const r={name:t,type:i,pos:n,direction:o,links:null};return this.connections.push(r),r}computeSize(t){var c,_;const i=this.constructor.size;if(i)return[i[0],i[1]];let n=Math.max(this.inputs?this.inputs.length:1,this.outputs?this.outputs.length:1);const o=t||new Float32Array([0,0]);n=Math.max(n,1);const r=LiteGraph.NODE_TEXT_SIZE,a=d(this.title);let l=0,u=0;if(this.inputs)for(let g=0,f=this.inputs.length;g<f;++g){const m=this.inputs[g],w=m.label||m.localized_name||m.name||"",b=d(w);l<b&&(l=b)}if(this.outputs)for(let g=0,f=this.outputs.length;g<f;++g){const m=this.outputs[g],w=m.label||m.localized_name||m.name||"",b=d(w);u<b&&(u=b)}o[0]=Math.max(l+u+10,a),o[0]=Math.max(o[0],LiteGraph.NODE_WIDTH),(c=this.widgets)!=null&&c.length&&(o[0]=Math.max(o[0],LiteGraph.NODE_WIDTH*1.5)),o[1]=(this.constructor.slot_start_y||0)+n*LiteGraph.NODE_SLOT_HEIGHT;let p=0;if((_=this.widgets)!=null&&_.length){for(let g=0,f=this.widgets.length;g<f;++g){const m=this.widgets[g];m.hidden||m.advanced&&!this.showAdvanced||(p+=m.computeSize?m.computeSize(o[0])[1]+4:LiteGraph.NODE_WIDGET_HEIGHT+4)}p+=8}this.widgets_up?o[1]=Math.max(o[1],p):this.widgets_start_y!=null?o[1]=Math.max(o[1],p+this.widgets_start_y):o[1]+=p;function d(g){return g?r*g.length*.6:0}return this.constructor.min_height&&o[1]<this.constructor.min_height&&(o[1]=this.constructor.min_height),o[1]+=6,o}inResizeCorner(t,i){const n=this.outputs?this.outputs.length:1,o=(this.constructor.slot_start_y||0)+n*LiteGraph.NODE_SLOT_HEIGHT;return isInRectangle(t,i,this.pos[0]+this.size[0]-15,this.pos[1]+Math.max(this.size[1]-15,o),20,20)}getPropertyInfo(t){var n;let i=null;if(this.properties_info){for(let o=0;o<this.properties_info.length;++o)if(this.properties_info[o].name==t){i=this.properties_info[o];break}}return this.constructor["@"+t]&&(i=this.constructor["@"+t]),(n=this.constructor.widgets_info)!=null&&n[t]&&(i=this.constructor.widgets_info[t]),!i&&this.onGetPropertyInfo&&(i=this.onGetPropertyInfo(t)),i||(i={}),i.type||(i.type=typeof this.properties[t]),i.widget=="combo"&&(i.type="enum"),i}addWidget(t,i,n,o,r){this.widgets||(this.widgets=[]),!r&&o&&typeof o=="object"&&(r=o,o=null),r&&typeof r=="string"&&(r={property:r}),o&&typeof o=="string"&&(r||(r={}),r.property=o,o=null),o&&typeof o!="function"&&(console.warn("addWidget: callback must be a function"),o=null);const a={type:t.toLowerCase(),name:i,value:n,callback:o,options:r||{}};if(a.options.y!==void 0&&(a.y=a.options.y),!o&&!a.options.callback&&!a.options.property&&console.warn("LiteGraph addWidget(...) without a callback or property assigned"),t=="combo"&&!a.options.values)throw"LiteGraph addWidget('combo',...) requires to pass values in options: { values:['red','blue'] }";const l=this.addCustomWidget(a);return this.setSize(this.computeSize()),l}addCustomWidget(t){this.widgets||(this.widgets=[]);let i;switch(t.type){case"toggle":i=new BooleanWidget(t);break;default:i=t}return this.widgets.push(i),i}move(t,i){this.pinned||(this.pos[0]+=t,this.pos[1]+=i)}measure(t,i=0){var a;const n=this.title_mode,r=n!=TitleMode.TRANSPARENT_TITLE&&n!=TitleMode.NO_TITLE?LiteGraph.NODE_TITLE_HEIGHT:0;t[0]=this.pos[0]-i,t[1]=this.pos[1]+-r-i,(a=this.flags)!=null&&a.collapsed?(t[2]=(this._collapsed_width||LiteGraph.NODE_COLLAPSED_WIDTH)+2*i,t[3]=LiteGraph.NODE_TITLE_HEIGHT+2*i):(t[2]=this.size[0]+2*i,t[3]=this.size[1]+r+2*i)}getBounding(t,i){t||(t=new Float32Array(4));const n=i?this.renderArea:this.boundingRect;return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t}updateArea(){var n;const t=O(this,Ae);this.measure(t),(n=this.onBounding)==null||n.call(this,t);const i=O(this,De);i.set(t),i[0]-=4,i[1]-=4,i[2]+=6+4,i[3]+=5+4}isPointInside(t,i){return isInRect(t,i,this.boundingRect)}isPointInCollapse(t,i){const n=LiteGraph.NODE_TITLE_HEIGHT;return isInRectangle(t,i,this.pos[0],this.pos[1]-n,n,n)}getSlotInPosition(t,i){const n=new Float32Array(2);if(this.inputs)for(let o=0,r=this.inputs.length;o<r;++o){const a=this.inputs[o];if(this.getConnectionPos(!0,o,n),isInRectangle(t,i,n[0]-10,n[1]-5,20,10))return{input:a,slot:o,link_pos:n}}if(this.outputs)for(let o=0,r=this.outputs.length;o<r;++o){const a=this.outputs[o];if(this.getConnectionPos(!1,o,n),isInRectangle(t,i,n[0]-10,n[1]-5,20,10))return{output:a,slot:o,link_pos:n}}return null}getWidgetOnPos(t,i,n=!1){const{widgets:o,pos:r,size:a}=this;if(!(o!=null&&o.length))return null;const l=t-r[0],u=i-r[1],p=a[0];for(const d of o){if(!d||d.disabled&&!n||d.hidden||d.advanced&&!this.showAdvanced)continue;const c=d.computeSize?d.computeSize(p)[1]:LiteGraph.NODE_WIDGET_HEIGHT,_=d.width||p;if(d.last_y!==void 0&&isInRectangle(l,u,6,d.last_y,_-12,c))return d}return null}findInputSlot(t,i=!1){if(!this.inputs)return-1;for(let n=0,o=this.inputs.length;n<o;++n)if(t==this.inputs[n].name)return i?this.inputs[n]:n;return-1}findOutputSlot(t,i=!1){if(!this.outputs)return-1;for(let n=0,o=this.outputs.length;n<o;++n)if(t==this.outputs[n].name)return i?this.outputs[n]:n;return-1}findInputSlotFree(t){return M(this,Re,ct).call(this,this.inputs,t)}findOutputSlotFree(t){return M(this,Re,ct).call(this,this.outputs,t)}findInputSlotByType(t,i,n,o){return M(this,pe,Ee).call(this,this.inputs,t,i,n,o)}findOutputSlotByType(t,i,n,o){return M(this,pe,Ee).call(this,this.outputs,t,i,n,o)}findSlotByType(t,i,n,o,r){return t?M(this,pe,Ee).call(this,this.inputs,i,n,o,r):M(this,pe,Ee).call(this,this.outputs,i,n,o,r)}findConnectByTypeSlot(t,i,n,o){o&&typeof o=="object"&&("firstFreeIfInputGeneralInCase"in o&&(o.wildcardToTyped=!!o.firstFreeIfInputGeneralInCase),"firstFreeIfOutputGeneralInCase"in o&&(o.wildcardToTyped=!!o.firstFreeIfOutputGeneralInCase),"generalTypeInCase"in o&&(o.typedToWildcard=!!o.generalTypeInCase));const a=Object.assign({createEventInCase:!0,wildcardToTyped:!0,typedToWildcard:!0},o);i&&typeof i=="number"&&(i=this.graph.getNodeById(i));const l=i.findSlotByType(t,n,!1,!0);if(l>=0&&l!==null)return l;if(a.createEventInCase&&n==LiteGraph.EVENT){if(t)return-1;if(LiteGraph.do_add_triggers_slots)return i.addOnExecutedOutput()}if(a.typedToWildcard){const u=i.findSlotByType(t,0,!1,!0,!0);if(u>=0)return u}if(a.wildcardToTyped&&(n==0||n=="*"||n=="")){const u={typesNotAccepted:[LiteGraph.EVENT]},p=t?i.findInputSlotFree(u):i.findOutputSlotFree(u);if(p>=0)return p}return null}connectByType(t,i,n,o){const r=this.findConnectByTypeSlot(!0,i,n,o);return r!==null?this.connect(t,i,r,o==null?void 0:o.afterRerouteId):(console.debug("[connectByType]: no way to connect type: ",n," to node: ",i),null)}connectByTypeOutput(t,i,n,o){typeof o=="object"&&("firstFreeIfInputGeneralInCase"in o&&(o.wildcardToTyped=!!o.firstFreeIfInputGeneralInCase),"generalTypeInCase"in o&&(o.typedToWildcard=!!o.generalTypeInCase));const r=this.findConnectByTypeSlot(!1,i,n,o);return r!==null?i.connect(r,this,t,o==null?void 0:o.afterRerouteId):(console.debug("[connectByType]: no way to connect type: ",n," to node: ",i),null)}connect(t,i,n,o){var _,g,f,m,w,b,k,L;let r;const a=this.graph;if(!a)return console.log("Connect: Error, node doesn't belong to any graph. Nodes must be added first to a graph before connecting them."),null;if(typeof t=="string"){if(t=this.findOutputSlot(t),t==-1)return LiteGraph.debug&&console.log("Connect: Error, no slot of name "+t),null}else if(!this.outputs||t>=this.outputs.length)return LiteGraph.debug&&console.log("Connect: Error, slot number not found"),null;if(i&&typeof i=="number"&&(i=a.getNodeById(i)),!i)throw"target node is null";if(i==this)return null;if(typeof n=="string"){if(r=i.findInputSlot(n),r==-1)return LiteGraph.debug&&console.log("Connect: Error, no slot of name "+r),null}else if(n===LiteGraph.EVENT)if(LiteGraph.do_add_triggers_slots)i.changeMode(LGraphEventMode.ON_TRIGGER),r=i.findInputSlot("onTrigger");else return null;else typeof n=="number"?r=n:r=0;if(i.onBeforeConnectInput){const T=i.onBeforeConnectInput(r,n);r=typeof T=="number"?T:null}if(r===null||!i.inputs||r>=i.inputs.length)return LiteGraph.debug&&console.log("Connect: Error, slot number not found"),null;let l=!1;const u=i.inputs[r];let p=null;const d=this.outputs[t];if(!this.outputs[t])return null;if(!LiteGraph.isValidConnection(d.type,u.type))return this.setDirtyCanvas(!1,!0),l&&a.connectionChange(this,p),null;if(((_=i.onConnectInput)==null?void 0:_.call(i,r,d.type,d,this,t))===!1||((g=this.onConnectOutput)==null?void 0:g.call(this,t,u.type,u,i,r))===!1)return null;((f=i.inputs[r])==null?void 0:f.link)!=null&&(a.beforeChange(),i.disconnectInput(r,!0),l=!0),(m=d.links)!=null&&m.length&&d.type===LiteGraph.EVENT&&!LiteGraph.allow_multi_output_for_events&&(a.beforeChange(),this.disconnectOutput(t,!1,{doProcessChange:!1}),l=!0);const c=++a.state.lastLinkId;return p=new LLink(c,u.type||d.type,this.id,t,i.id,r,o),a._links.set(p.id,p),d.links??(d.links=[]),d.links.push(p.id),i.inputs[r].link=p.id,LLink.getReroutes(a,p).forEach(T=>T==null?void 0:T.linkIds.add(c)),a._version++,(w=this.onConnectionsChange)==null||w.call(this,NodeSlotType.OUTPUT,t,!0,p,d),(b=i.onConnectionsChange)==null||b.call(i,NodeSlotType.INPUT,r,!0,p,u),(k=a.onNodeConnectionChange)==null||k.call(a,NodeSlotType.INPUT,i,r,this,t),(L=a.onNodeConnectionChange)==null||L.call(a,NodeSlotType.OUTPUT,this,t,i,r),this.setDirtyCanvas(!1,!0),a.afterChange(),a.connectionChange(this),p}disconnectOutput(t,i){var r,a,l,u,p,d,c,_;if(typeof t=="string"){if(t=this.findOutputSlot(t),t==-1)return LiteGraph.debug&&console.log("Connect: Error, no slot of name "+t),!1}else if(!this.outputs||t>=this.outputs.length)return LiteGraph.debug&&console.log("Connect: Error, slot number not found"),!1;const n=this.outputs[t];if(!n||!n.links||n.links.length==0)return!1;const o=this.graph;if(i){if(typeof i=="number"&&(i=o.getNodeById(i)),!i)throw"Target Node not found";for(let g=0,f=n.links.length;g<f;g++){const m=n.links[g],w=o._links.get(m);if(w.target_id==i.id){n.links.splice(g,1);const b=i.inputs[w.target_slot];b.link=null,o._links.delete(m),o._version++,(r=i.onConnectionsChange)==null||r.call(i,NodeSlotType.INPUT,w.target_slot,!1,w,b),(a=this.onConnectionsChange)==null||a.call(this,NodeSlotType.OUTPUT,t,!1,w,n),(l=o.onNodeConnectionChange)==null||l.call(o,NodeSlotType.OUTPUT,this,t),(u=o.onNodeConnectionChange)==null||u.call(o,NodeSlotType.INPUT,i,w.target_slot);break}}}else{for(let g=0,f=n.links.length;g<f;g++){const m=n.links[g],w=o._links.get(m);if(w){if(i=o.getNodeById(w.target_id),o._version++,i){const b=i.inputs[w.target_slot];b.link=null,(p=i.onConnectionsChange)==null||p.call(i,NodeSlotType.INPUT,w.target_slot,!1,w,b)}o._links.delete(m),(d=this.onConnectionsChange)==null||d.call(this,NodeSlotType.OUTPUT,t,!1,w,n),(c=o.onNodeConnectionChange)==null||c.call(o,NodeSlotType.OUTPUT,this,t),(_=o.onNodeConnectionChange)==null||_.call(o,NodeSlotType.INPUT,i,w.target_slot)}}n.links=null}return this.setDirtyCanvas(!1,!0),o.connectionChange(this),!0}disconnectInput(t,i){var r,a,l,u,p,d,c,_;if(typeof t=="string"){if(t=this.findInputSlot(t),t==-1)return LiteGraph.debug&&console.log("Connect: Error, no slot of name "+t),!1}else if(!this.inputs||t>=this.inputs.length)return LiteGraph.debug&&console.log("Connect: Error, slot number not found"),!1;const n=this.inputs[t];if(!n)return!1;const o=this.inputs[t].link;if(o!=null){this.inputs[t].link=null;const g=this.graph._links.get(o);if(g){const f=this.graph.getNodeById(g.origin_id);if(!f)return!1;const m=f.outputs[g.origin_slot];if(!(((r=m==null?void 0:m.links)==null?void 0:r.length)>0))return!1;let w=0;for(const b=m.links.length;w<b;w++)if(m.links[w]==o){m.links.splice(w,1);break}g.disconnect(this.graph,i),this.graph&&this.graph._version++,(a=this.onConnectionsChange)==null||a.call(this,NodeSlotType.INPUT,t,!1,g,n),(l=f.onConnectionsChange)==null||l.call(f,NodeSlotType.OUTPUT,w,!1,g,m),(p=(u=this.graph)==null?void 0:u.onNodeConnectionChange)==null||p.call(u,NodeSlotType.OUTPUT,f,w),(c=(d=this.graph)==null?void 0:d.onNodeConnectionChange)==null||c.call(d,NodeSlotType.INPUT,this,t)}}return this.setDirtyCanvas(!1,!0),(_=this.graph)==null||_.connectionChange(this),!0}getConnectionPos(t,i,n){var a,l;n||(n=new Float32Array(2));const o=t?((a=this.inputs)==null?void 0:a.length)??0:((l=this.outputs)==null?void 0:l.length)??0,r=LiteGraph.NODE_SLOT_HEIGHT*.5;if(this.flags.collapsed){const u=this._collapsed_width||LiteGraph.NODE_COLLAPSED_WIDTH;return this.horizontal?(n[0]=this.pos[0]+u*.5,n[1]=t?this.pos[1]-LiteGraph.NODE_TITLE_HEIGHT:this.pos[1]):(n[0]=t?this.pos[0]:this.pos[0]+u,n[1]=this.pos[1]-LiteGraph.NODE_TITLE_HEIGHT*.5),n}return t&&i==-1?(n[0]=this.pos[0]+LiteGraph.NODE_TITLE_HEIGHT*.5,n[1]=this.pos[1]+LiteGraph.NODE_TITLE_HEIGHT*.5,n):t&&o>i&&this.inputs[i].pos?(n[0]=this.pos[0]+this.inputs[i].pos[0],n[1]=this.pos[1]+this.inputs[i].pos[1],n):!t&&o>i&&this.outputs[i].pos?(n[0]=this.pos[0]+this.outputs[i].pos[0],n[1]=this.pos[1]+this.outputs[i].pos[1],n):this.horizontal?(n[0]=this.pos[0]+(i+.5)*(this.size[0]/o),n[1]=t?this.pos[1]-LiteGraph.NODE_TITLE_HEIGHT:this.pos[1]+this.size[1],n):(n[0]=t?this.pos[0]+r:this.pos[0]+this.size[0]+1-r,n[1]=this.pos[1]+(i+.7)*LiteGraph.NODE_SLOT_HEIGHT+(this.constructor.slot_start_y||0),n)}snapToGrid(t){return this.pinned?!1:snapPoint(this.pos,t)}alignToGrid(){this.snapToGrid(LiteGraph.CANVAS_GRID_SIZE)}trace(t){var i,n;this.console||(this.console=[]),this.console.push(t),this.console.length>Ie.MAX_CONSOLE&&this.console.shift(),(n=(i=this.graph).onNodeTrace)==null||n.call(i,this,t)}setDirtyCanvas(t,i){var n;(n=this.graph)==null||n.canvasAction(o=>o.setDirty(t,i))}loadImage(t){const i=new Image;i.src=LiteGraph.node_images_path+t,i.ready=!1;const n=this;return i.onload=function(){this.ready=!0,n.setDirtyCanvas(!0)},i}captureInput(t){if(!this.graph||!this.graph.list_of_graphcanvas)return;const i=this.graph.list_of_graphcanvas;for(let n=0;n<i.length;++n){const o=i[n];!t&&o.node_capturing_input!=this||(o.node_capturing_input=t?this:null)}}get collapsed(){return!!this.flags.collapsed}get collapsible(){return!this.pinned&&this.constructor.collapsable!==!1}collapse(t){!this.collapsible&&!t||(this.graph._version++,this.flags.collapsed=!this.flags.collapsed,this.setDirtyCanvas(!0,!0))}toggleAdvanced(){var i;if(!((i=this.widgets)!=null&&i.some(n=>n.advanced)))return;this.graph._version++,this.showAdvanced=!this.showAdvanced;const t=this.computeSize();(this.size[0]<t[0]||this.size[1]<t[1])&&this.setSize([Math.max(this.size[0],t[0]),Math.max(this.size[1],t[1])]),this.setDirtyCanvas(!0,!0)}get pinned(){return!!this.flags.pinned}pin(t){this.graph&&this.graph._version++,this.flags.pinned=t??!this.flags.pinned,this.resizable=!this.pinned,this.pinned||delete this.flags.pinned}unpin(){this.pin(!1)}localToScreen(t,i,n){return[(t+this.pos[0])*n.scale+n.offset[0],(i+this.pos[1])*n.scale+n.offset[1]]}get width(){return this.collapsed?this._collapsed_width||LiteGraph.NODE_COLLAPSED_WIDTH:this.size[0]}get height(){const t=this.collapsed?0:this.size[1];return LiteGraph.NODE_TITLE_HEIGHT+t}drawBadges(t,{gap:i=2}={}){const n=this.badges.map(l=>l instanceof LGraphBadge?l:l());let r=this.badgePosition===BadgePosition.TopLeft?0:this.width-n.reduce((l,u)=>l+u.getWidth(t)+i,0);const a=-(LiteGraph.NODE_TITLE_HEIGHT+i);for(const l of n)l.draw(t,r,a-l.height),r+=l.getWidth(t)+i}drawTitleBarBackground(t,i){const{scale:n,title_height:o=LiteGraph.NODE_TITLE_HEIGHT,low_quality:r=!1}=i,a=this.renderingColor,l=this.renderingShape,u=this.renderingSize;if(this.onDrawTitleBar){this.onDrawTitleBar(t,o,u,n,a);return}this.title_mode!==TitleMode.TRANSPARENT_TITLE&&(this.collapsed&&(t.shadowColor=LiteGraph.DEFAULT_SHADOW_COLOR),t.fillStyle=this.constructor.title_color||a,t.beginPath(),l==RenderShape.BOX||r?t.rect(0,-o,u[0],o):(l==RenderShape.ROUND||l==RenderShape.CARD)&&t.roundRect(0,-o,u[0],o,this.collapsed?[LiteGraph.ROUND_RADIUS]:[LiteGraph.ROUND_RADIUS,LiteGraph.ROUND_RADIUS,0,0]),t.fill(),t.shadowColor="transparent")}drawTitleBox(t,i){const{scale:n,low_quality:o=!1,title_height:r=LiteGraph.NODE_TITLE_HEIGHT,box_size:a=10}=i,l=this.renderingSize,u=this.renderingShape;if(this.onDrawTitleBox){this.onDrawTitleBox(t,r,l,n);return}[RenderShape.ROUND,RenderShape.CIRCLE,RenderShape.CARD].includes(u)?(o&&(t.fillStyle="black",t.beginPath(),t.arc(r*.5,r*-.5,a*.5+1,0,Math.PI*2),t.fill()),t.fillStyle=this.renderingBoxColor,o?t.fillRect(r*.5-a*.5,r*-.5-a*.5,a,a):(t.beginPath(),t.arc(r*.5,r*-.5,a*.5,0,Math.PI*2),t.fill())):(o&&(t.fillStyle="black",t.fillRect((r-a)*.5-1,(r+a)*-.5-1,a+2,a+2)),t.fillStyle=this.renderingBoxColor,t.fillRect((r-a)*.5,(r+a)*-.5,a,a))}drawTitleText(t,i){const{scale:n,default_title_color:o,low_quality:r=!1,title_height:a=LiteGraph.NODE_TITLE_HEIGHT}=i,l=this.renderingSize,u=this.selected;if(this.onDrawTitleText){this.onDrawTitleText(t,a,l,n,this.titleFontStyle,u);return}if(r)return;t.font=this.titleFontStyle;const p=this.getTitle()??`❌ ${this.type}`,d=String(p)+(this.pinned?"📌":"");d&&(u?t.fillStyle=LiteGraph.NODE_SELECTED_TITLE_COLOR:t.fillStyle=this.constructor.title_text_color||o,this.collapsed?(t.textAlign="left",t.fillText(d.substr(0,20),a,LiteGraph.NODE_TITLE_TEXT_Y-a),t.textAlign="left"):(t.textAlign="left",t.fillText(d,a,LiteGraph.NODE_TITLE_TEXT_Y-a)))}connectInputToOutput(){const{inputs:t,outputs:i,graph:n}=this;if(!t||!i)return;const{_links:o}=n;let r=!1;for(const[l,u]of t.entries()){const p=i[l];if(!p||!LiteGraph.isValidConnection(u.type,p.type))continue;const d=o.get(u.link),c=n.getNodeById(d==null?void 0:d.origin_id);c&&a(p,c,d)}if(!(this.flags.keepAllLinksOnBypass??Ie.keepAllLinksOnBypass))return r;for(const l of t){const u=o.get(l.link),p=n.getNodeById(u==null?void 0:u.origin_id);if(p){for(const d of i)if(LiteGraph.isValidConnection(l.type,d.type)){a(d,p,u);break}}}return r;function a(l,u,p){var c;const d=(c=l.links)==null?void 0:c.map(_=>o.get(_)).filter(_=>!!_);if(d!=null&&d.length)for(const _ of d){const g=n.getNodeById(_.target_id);if(!g)return;const f=u.connect(p.origin_slot,g,_.target_slot,p.parentId);r||(r=!!f)}}}};let LGraphNode=Ie;De=new WeakMap,Ae=new WeakMap,Re=new WeakSet,ct=function(t,i){var a,l,u;const o=Object.assign({returnObj:!1,typesNotAccepted:[]},i||{}),r=t==null?void 0:t.length;if(!(r>0))return-1;for(let p=0;p<r;++p){const d=t[p];if(!(!d||d.link||(a=d.links)!=null&&a.length)&&!((u=(l=o.typesNotAccepted)==null?void 0:l.includes)!=null&&u.call(l,d.type)))return o.returnObj?d:p}return-1},pe=new WeakSet,Ee=function(t,i,n,o,r){var p;const a=t==null?void 0:t.length;if(!a)return-1;(i==""||i=="*")&&(i=0);const l=String(i).toLowerCase().split(",");let u=null;for(let d=0;d<a;++d){const c=t[d],_=c.type=="0"||c.type=="*"?["0"]:String(c.type).toLowerCase().split(",");for(const g of l){const f=g=="_event_"?LiteGraph.EVENT:g;for(const m of _){const w=m=="_event_"?LiteGraph.EVENT:m;if(f==w||f==="*"||w==="*"){if(o&&((p=c.links)!=null&&p.length||c.link!=null)){u??(u=n?c:d);continue}return n?c:d}}}}return r?-1:u??-1},h(LGraphNode,"title"),h(LGraphNode,"MAX_CONSOLE"),h(LGraphNode,"type"),h(LGraphNode,"category"),h(LGraphNode,"filter"),h(LGraphNode,"skip_list"),h(LGraphNode,"keepAllLinksOnBypass",!1);const Z=class{constructor(t,i){h(this,"id");h(this,"color");h(this,"title");h(this,"font");h(this,"font_size",LiteGraph.DEFAULT_GROUP_FONT||24);h(this,"_bounding",new Float32Array([10,10,Z.minWidth,Z.minHeight]));h(this,"_pos",this._bounding.subarray(0,2));h(this,"_size",this._bounding.subarray(2,4));h(this,"_nodes",[]);h(this,"_children",new Set);h(this,"graph",null);h(this,"flags",{});h(this,"selected");h(this,"isPointInside",LGraphNode.prototype.isPointInside);h(this,"setDirtyCanvas",LGraphNode.prototype.setDirtyCanvas);this.id=i??-1,this.title=t||"Group",this.color=LGraphCanvas.node_colors.pale_blue?LGraphCanvas.node_colors.pale_blue.groupcolor:"#AAA"}get pos(){return this._pos}set pos(t){!t||t.length<2||(this._pos[0]=t[0],this._pos[1]=t[1])}get size(){return this._size}set size(t){!t||t.length<2||(this._size[0]=Math.max(Z.minWidth,t[0]),this._size[1]=Math.max(Z.minHeight,t[1]))}get boundingRect(){return this._bounding}get nodes(){return this._nodes}get titleHeight(){return this.font_size*1.4}get children(){return this._children}get pinned(){return!!this.flags.pinned}pin(t){(t===void 0?!this.pinned:t)?this.flags.pinned=!0:delete this.flags.pinned}unpin(){this.pin(!1)}configure(t){this.id=t.id,this.title=t.title,this._bounding.set(t.bounding),this.color=t.color,this.flags=t.flags||this.flags,t.font_size&&(this.font_size=t.font_size)}serialize(){const t=this._bounding;return{id:this.id,title:this.title,bounding:[...t],color:this.color,font_size:this.font_size,flags:this.flags}}draw(t,i){const{padding:n,resizeLength:o,defaultColour:r}=Z,a=this.font_size||LiteGraph.DEFAULT_GROUP_FONT_SIZE,[l,u]=this._pos,[p,d]=this._size;i.globalAlpha=.25*t.editor_alpha,i.fillStyle=this.color||r,i.strokeStyle=this.color||r,i.beginPath(),i.rect(l+.5,u+.5,p,a*1.4),i.fill(),i.fillStyle=this.color,i.strokeStyle=this.color,i.beginPath(),i.rect(l+.5,u+.5,p,d),i.fill(),i.globalAlpha=t.editor_alpha,i.stroke(),i.beginPath(),i.moveTo(l+p,u+d),i.lineTo(l+p-o,u+d),i.lineTo(l+p,u+d-o),i.fill(),i.font=a+"px Arial",i.textAlign="left",i.fillText(this.title+(this.pinned?"📌":""),l+n,u+a),LiteGraph.highlight_selected_group&&this.selected&&strokeShape(i,this._bounding,{title_height:this.titleHeight,padding:n})}resize(t,i){return this.pinned?!1:(this._size[0]=Math.max(Z.minWidth,t),this._size[1]=Math.max(Z.minHeight,i),!0)}move(t,i,n=!1){if(!this.pinned&&(this._pos[0]+=t,this._pos[1]+=i,n!==!0))for(const o of this._children)o.move(t,i)}snapToGrid(t){return this.pinned?!1:snapPoint(this.pos,t)}recomputeInsideNodes(){const{nodes:t,reroutes:i,groups:n}=this.graph,o=this._children;this._nodes.length=0,o.clear();for(const r of t)containsCentre(this._bounding,r.boundingRect)&&(this._nodes.push(r),o.add(r));for(const r of i.values())isPointInRect(r.pos,this._bounding)&&o.add(r);for(const r of n)containsRect(this._bounding,r._bounding)&&o.add(r);n.sort((r,a)=>{if(r===this)return o.has(a)?-1:0;if(a===this)return o.has(r)?1:0})}resizeTo(t,i=10){const n=createBounds(t,i);n!==null&&(this.pos[0]=n[0],this.pos[1]=n[1]-this.titleHeight,this.size[0]=n[2],this.size[1]=n[3]+this.titleHeight)}addNodes(t,i=10){!this._nodes&&t.length===0||this.resizeTo([...this.children,...this._nodes,...t],i)}getMenuOptions(){return[{content:this.pinned?"Unpin":"Pin",callback:()=>{this.pinned?this.unpin():this.pin(),this.setDirtyCanvas(!1,!0)}},null,{content:"Title",callback:LGraphCanvas.onShowPropertyEditor},{content:"Color",has_submenu:!0,callback:LGraphCanvas.onMenuNodeColors},{content:"Font size",property:"font_size",type:"Number",callback:LGraphCanvas.onShowPropertyEditor},null,{content:"Remove",callback:LGraphCanvas.onMenuNodeRemove}]}isPointInTitlebar(t,i){const n=this.boundingRect;return isInRectangle(t,i,n[0],n[1],n[2],this.titleHeight)}isInResize(t,i){const n=this.boundingRect,o=n[0]+n[2],r=n[1]+n[3];return t<o&&i<r&&t-o+(i-r)>-Z.resizeLength}};let LGraphGroup=Z;h(LGraphGroup,"minWidth",140),h(LGraphGroup,"minHeight",80),h(LGraphGroup,"resizeLength",10),h(LGraphGroup,"padding",4),h(LGraphGroup,"defaultColour","#335");class DragAndScale{constructor(t,i){h(this,"state");h(this,"max_scale");h(this,"min_scale");h(this,"enabled");h(this,"last_mouse");h(this,"element");h(this,"visible_area");h(this,"_binded_mouse_callback");h(this,"dragging");h(this,"viewport");this.state={offset:new Float32Array([0,0]),scale:1},this.max_scale=10,this.min_scale=.1,this.onredraw=null,this.enabled=!0,this.last_mouse=[0,0],this.element=null,this.visible_area=new Float32Array(4),t&&(this.element=t,i||this.bindEvents(t))}get offset(){return this.state.offset}set offset(t){this.state.offset=t}get scale(){return this.state.scale}set scale(t){this.state.scale=t}bindEvents(t){this.last_mouse=new Float32Array(2),this._binded_mouse_callback=this.onMouse.bind(this),LiteGraph.pointerListenerAdd(t,"down",this._binded_mouse_callback),LiteGraph.pointerListenerAdd(t,"move",this._binded_mouse_callback),LiteGraph.pointerListenerAdd(t,"up",this._binded_mouse_callback),t.addEventListener("mousewheel",this._binded_mouse_callback,!1),t.addEventListener("wheel",this._binded_mouse_callback,!1)}computeVisibleArea(t){if(!this.element){this.visible_area[0]=this.visible_area[1]=this.visible_area[2]=this.visible_area[3]=0;return}let i=this.element.width,n=this.element.height,o=-this.offset[0],r=-this.offset[1];t&&(o+=t[0]/this.scale,r+=t[1]/this.scale,i=t[2],n=t[3]);const a=o+i/this.scale,l=r+n/this.scale;this.visible_area[0]=o,this.visible_area[1]=r,this.visible_area[2]=a-o,this.visible_area[3]=l-r}onMouse(t){if(!this.enabled)return;const i=this.element,n=i.getBoundingClientRect(),o=t.clientX-n.left,r=t.clientY-n.top;t.canvasx=o,t.canvasy=r,t.dragging=this.dragging;const a=!this.viewport||isInRect(o,r,this.viewport);let l=!1;if(this.onmouse&&(l=this.onmouse(t)),t.type==LiteGraph.pointerevents_method+"down"&&a)this.dragging=!0,LiteGraph.pointerListenerRemove(i,"move",this._binded_mouse_callback),LiteGraph.pointerListenerAdd(document,"move",this._binded_mouse_callback),LiteGraph.pointerListenerAdd(document,"up",this._binded_mouse_callback);else if(t.type==LiteGraph.pointerevents_method+"move"){if(!l){const u=o-this.last_mouse[0],p=r-this.last_mouse[1];this.dragging&&this.mouseDrag(u,p)}}else t.type==LiteGraph.pointerevents_method+"up"?(this.dragging=!1,LiteGraph.pointerListenerRemove(document,"move",this._binded_mouse_callback),LiteGraph.pointerListenerRemove(document,"up",this._binded_mouse_callback),LiteGraph.pointerListenerAdd(i,"move",this._binded_mouse_callback)):a&&(t.type=="mousewheel"||t.type=="wheel"||t.type=="DOMMouseScroll")&&(t.eventType="mousewheel",t.type=="wheel"?t.wheel=-t.deltaY:t.wheel=t.wheelDeltaY!=null?t.wheelDeltaY:t.detail*-60,t.delta=t.wheelDelta?t.wheelDelta/40:t.deltaY?-t.deltaY/3:0,this.changeDeltaScale(1+t.delta*.05));if(this.last_mouse[0]=o,this.last_mouse[1]=r,a)return t.preventDefault(),t.stopPropagation(),!1}toCanvasContext(t){t.scale(this.scale,this.scale),t.translate(this.offset[0],this.offset[1])}convertOffsetToCanvas(t){return[(t[0]+this.offset[0])*this.scale,(t[1]+this.offset[1])*this.scale]}convertCanvasToOffset(t,i){return i=i||[0,0],i[0]=t[0]/this.scale-this.offset[0],i[1]=t[1]/this.scale-this.offset[1],i}mouseDrag(t,i){var n;this.offset[0]+=t/this.scale,this.offset[1]+=i/this.scale,(n=this.onredraw)==null||n.call(this,this)}changeScale(t,i){var l;if(t<this.min_scale?t=this.min_scale:t>this.max_scale&&(t=this.max_scale),t==this.scale||!this.element)return;const n=this.element.getBoundingClientRect();if(!n)return;i=i||[n.width*.5,n.height*.5],i[0]-=n.x,i[1]-=n.y;const o=this.convertCanvasToOffset(i);this.scale=t,Math.abs(this.scale-1)<.01&&(this.scale=1);const r=this.convertCanvasToOffset(i),a=[r[0]-o[0],r[1]-o[1]];this.offset[0]+=a[0],this.offset[1]+=a[1],(l=this.onredraw)==null||l.call(this,this)}changeDeltaScale(t,i){this.changeScale(this.scale*t,i)}reset(){this.scale=1,this.offset[0]=0,this.offset[1]=0}}function stringOrNull(s){return s==null?null:String(s)}function stringOrEmpty(s){return s==null?"":String(s)}function getBoundaryNodes(s){const t=s==null?void 0:s.find(a=>a);if(!t)return null;let i=t,n=t,o=t,r=t;for(const a of s){if(!a)continue;const[l,u]=a.pos,[p,d]=a.size;u<i.pos[1]&&(i=a),l+p>n.pos[0]+n.size[0]&&(n=a),u+d>o.pos[1]+o.size[1]&&(o=a),l<r.pos[0]&&(r=a)}return{top:i,right:n,bottom:o,left:r}}function distributeNodes(s,t){const i=s==null?void 0:s.length;if(!(i>1))return;const n=t?0:1;let o=0,r=-1/0;for(const d of s){o+=d.size[n];const c=d.pos[n]+d.size[n];c>r&&(r=c)}const a=[...s].sort((d,c)=>d.pos[n]-c.pos[n]),l=a[0].pos[n],u=(r-l-o)/(i-1);let p=l;for(let d=0;d<i;d++){const c=a[d];c.pos[n]=p+u*d,p+=c.size[n]}}function alignNodes(s,t,i){if(!s)return;const n=i===void 0?getBoundaryNodes(s):{top:i,right:i,bottom:i,left:i};if(n!==null)for(const o of s)switch(t){case"right":o.pos[0]=n.right.pos[0]+n.right.size[0]-o.size[0];break;case"left":o.pos[0]=n.left.pos[0];break;case"top":o.pos[1]=n.top.pos[1];break;case"bottom":o.pos[1]=n.bottom.pos[1]+n.bottom.size[1]-o.size[1];break}}function getAllNestedItems(s){const t=new Set;return s==null||s.forEach(n=>i(n,t)),t;function i(n,o){var r;o.has(n)||n.pinned||(o.add(n),(r=n.children)==null||r.forEach(a=>i(a,o)))}}function findFirstNode(s){for(const t of s)if(t instanceof LGraphNode)return t}var fe,Me,ye,Pe,dt,we,xe,Xe,vt,Be,pt;const ce=class{constructor(t){R(this,Pe);R(this,we);R(this,Xe);R(this,Be);h(this,"element");h(this,"pointerId");h(this,"dragStarted",!1);h(this,"eLastDown");h(this,"isDouble",!1);h(this,"isDown",!1);h(this,"clearEventsOnReset",!0);h(this,"eDown",null);h(this,"eMove",null);h(this,"eUp",null);R(this,ye,void 0);this.element=t}static get maxClickDrift(){return O(this,fe)}static set maxClickDrift(t){Y(this,fe,t),Y(this,Me,t*t)}get finally(){return O(this,ye)}set finally(t){var i;try{(i=O(this,ye))==null||i.call(this)}finally{Y(this,ye,t)}}down(t){this.reset(),this.eDown=t,this.pointerId=t.pointerId,this.element.setPointerCapture(t.pointerId)}move(t){var o;const{eDown:i}=this;if(!i)return;if(!t.buttons){this.reset();return}if(!(t.buttons&i.buttons)){M(this,Pe,dt).call(this,t),this.reset();return}if(this.eMove=t,(o=this.onDrag)==null||o.call(this,t),this.dragStarted)return;(t.timeStamp-i.timeStamp>ce.bufferTime||!M(this,we,xe).call(this,t,i))&&M(this,Be,pt).call(this)}up(t){var n;if(t.button!==((n=this.eDown)==null?void 0:n.button))return!1;M(this,Pe,dt).call(this,t);const{dragStarted:i}=this;return this.reset(),!i}reset(){this.finally=void 0,delete this.onClick,delete this.onDoubleClick,delete this.onDragStart,delete this.onDrag,delete this.onDragEnd,this.isDown=!1,this.isDouble=!1,this.dragStarted=!1,this.clearEventsOnReset&&(this.eDown=null,this.eMove=null,this.eUp=null);const{element:t,pointerId:i}=this;t.hasPointerCapture(i)&&t.releasePointerCapture(i)}};let CanvasPointer=ce;fe=new WeakMap,Me=new WeakMap,ye=new WeakMap,Pe=new WeakSet,dt=function(t){var n,o,r;const{eDown:i}=this;i&&(this.eUp=t,this.dragStarted?(n=this.onDragEnd)==null||n.call(this,t):M(this,we,xe).call(this,t,i)?this.onDoubleClick&&M(this,Xe,vt).call(this)?(this.onDoubleClick(t),this.eLastDown=void 0):((r=this.onClick)==null||r.call(this,t),this.eLastDown=i):(M(this,Be,pt).call(this),(o=this.onDragEnd)==null||o.call(this,t)))},we=new WeakSet,xe=function(t,i,n=O(ce,Me)){return dist2(t.clientX,t.clientY,i.clientX,i.clientY)<=n},Xe=new WeakSet,vt=function(){const{eDown:t,eLastDown:i}=this;if(!t||!i)return!1;const n=(3*O(ce,fe))**2,o=t.timeStamp-i.timeStamp;return o>0&&o<ce.doubleClickTime&&M(this,we,xe).call(this,t,i,n)},Be=new WeakSet,pt=function(){var t;this.dragStarted=!0,(t=this.onDragStart)==null||t.call(this,this),delete this.onDragStart},h(CanvasPointer,"bufferTime",150),h(CanvasPointer,"doubleClickTime",300),R(CanvasPointer,fe,6),R(CanvasPointer,Me,O(ce,fe)**2);function toClass(s,t){return t instanceof s?t:new s(t)}var Ye,ze,je,re,Q,Ke,qe,$e,Ze,Je,be,Ue,ae,ee,Fe,se,Qe,mt,te,ne,et,yt,tt,wt,it,bt,st,kt,nt,Lt,le,ve,he,me,ot,Tt,rt,Ct,at,Et,lt,Nt,_e,Ne;const _LGraphCanvas=class{constructor(s,t,i){R(this,be);R(this,Qe);R(this,te);R(this,et);R(this,tt);R(this,it);R(this,st);R(this,nt);R(this,le);R(this,he);R(this,ot);R(this,rt);R(this,at);R(this,lt);R(this,_e);h(this,"state",{draggingItems:!1,draggingCanvas:!1,readOnly:!1,hoveringOver:CanvasItem.Nothing,shouldSetCursor:!0});h(this,"_previously_dragging_canvas",null);R(this,ae,0);h(this,"options");h(this,"background_image");h(this,"ds");h(this,"pointer");h(this,"zoom_modify_alpha");h(this,"zoom_speed");h(this,"node_title_color");h(this,"default_link_color");h(this,"default_connection_color");h(this,"default_connection_color_byType");h(this,"default_connection_color_byTypeOff");h(this,"highquality_render");h(this,"use_gradients");h(this,"editor_alpha");h(this,"pause_rendering");h(this,"clear_background");h(this,"clear_background_color");h(this,"render_only_selected");h(this,"show_info");h(this,"allow_dragcanvas");h(this,"allow_dragnodes");h(this,"allow_interaction");h(this,"multi_select");h(this,"allow_searchbox");h(this,"allow_reconnect_links");h(this,"align_to_grid");h(this,"drag_mode");h(this,"dragging_rectangle");h(this,"filter");h(this,"set_canvas_dirty_on_mouse_event");h(this,"always_render_background");h(this,"render_shadows");h(this,"render_canvas_border");h(this,"render_connections_shadows");h(this,"render_connections_border");h(this,"render_curved_connections");h(this,"render_connection_arrows");h(this,"render_collapsed_slots");h(this,"render_execution_order");h(this,"render_link_tooltip");h(this,"reroutesEnabled",!1);h(this,"linkMarkerShape",LinkMarkerShape.Circle);h(this,"links_render_mode");h(this,"low_quality_zoom_threshold",.6);h(this,"mouse");h(this,"graph_mouse");h(this,"canvas_mouse");h(this,"onSearchBox");h(this,"onSearchBoxSelection");h(this,"onMouse");h(this,"onDrawBackground");h(this,"onDrawForeground");h(this,"connections_width");h(this,"current_node");h(this,"node_widget");h(this,"over_link_center");h(this,"last_mouse_position");h(this,"visible_area");h(this,"renderedPaths",new Set);h(this,"visible_links");h(this,"connecting_links");h(this,"viewport");h(this,"autoresize");h(this,"frame",0);h(this,"last_draw_time",0);h(this,"render_time",0);h(this,"fps",0);h(this,"selected_nodes",{});h(this,"selectedItems",new Set);h(this,"resizingGroup",null);h(this,"selected_group",null);h(this,"visible_nodes",[]);h(this,"node_over");h(this,"node_capturing_input");h(this,"highlighted_links",{});h(this,"link_over_widget");h(this,"link_over_widget_type");h(this,"dirty_canvas",!0);h(this,"dirty_bgcanvas",!0);h(this,"dirty_nodes",new Map);h(this,"dirty_area");h(this,"node_in_panel");h(this,"last_mouse",[0,0]);h(this,"last_mouseclick",0);h(this,"graph");h(this,"canvas");h(this,"bgcanvas");h(this,"ctx");h(this,"_events_binded");h(this,"gl");h(this,"bgctx");h(this,"is_rendering");h(this,"block_click");h(this,"last_click_position");h(this,"resizing_node");h(this,"selected_group_resizing");h(this,"last_mouse_dragging");h(this,"onMouseDown");h(this,"_highlight_pos");h(this,"_highlight_input");h(this,"node_panel");h(this,"options_panel");h(this,"onDropItem");h(this,"_bg_img");h(this,"_pattern");h(this,"_pattern_img");h(this,"prompt_box");h(this,"search_box");h(this,"SELECTED_NODE");h(this,"NODEPANEL_IS_OPEN");R(this,ee,void 0);R(this,Fe,!1);h(this,"dragZoomEnabled",!1);R(this,se,null);h(this,"onClear");h(this,"onNodeMoved");h(this,"onSelectionChange");h(this,"onDrawLinkTooltip");h(this,"onDrawOverlay");h(this,"onRenderBackground");h(this,"onNodeDblClicked");h(this,"onShowNodePanel");h(this,"onNodeSelected");h(this,"onNodeDeselected");h(this,"onRender");h(this,"getWidgetLinkType");i||(i={}),this.options=i,this.background_image=_LGraphCanvas.DEFAULT_BACKGROUND_IMAGE,this.ds=new DragAndScale,this.pointer=new CanvasPointer(this.canvas),this.zoom_modify_alpha=!0,this.zoom_speed=1.1,this.node_title_color=LiteGraph.NODE_TITLE_COLOR,this.default_link_color=LiteGraph.LINK_COLOR,this.default_connection_color={input_off:"#778",input_on:"#7F7",output_off:"#778",output_on:"#7F7"},this.default_connection_color_byType={},this.default_connection_color_byTypeOff={},this.highquality_render=!0,this.use_gradients=!1,this.editor_alpha=1,this.pause_rendering=!1,this.clear_background=!0,this.clear_background_color="#222",this.render_only_selected=!0,this.show_info=!0,this.allow_dragcanvas=!0,this.allow_dragnodes=!0,this.allow_interaction=!0,this.multi_select=!1,this.allow_searchbox=!0,this.allow_reconnect_links=!0,this.align_to_grid=!1,this.drag_mode=!1,this.dragging_rectangle=null,this.filter=null,this.set_canvas_dirty_on_mouse_event=!0,this.always_render_background=!1,this.render_shadows=!0,this.render_canvas_border=!0,this.render_connections_shadows=!1,this.render_connections_border=!0,this.render_curved_connections=!1,this.render_connection_arrows=!1,this.render_collapsed_slots=!0,this.render_execution_order=!1,this.render_link_tooltip=!0,this.links_render_mode=LinkRenderType.SPLINE_LINK,this.mouse=[0,0],this.graph_mouse=[0,0],this.canvas_mouse=this.graph_mouse,this.onSearchBox=null,this.onSearchBoxSelection=null,this.onMouse=null,this.onDrawBackground=null,this.onDrawForeground=null,this.onDrawOverlay=null,this.onDrawLinkTooltip=null,this.onNodeMoved=null,this.onSelectionChange=null,this.onBeforeChange=null,this.onAfterChange=null,this.connections_width=3,this.current_node=null,this.node_widget=null,this.over_link_center=null,this.last_mouse_position=[0,0],this.visible_area=this.ds.visible_area,this.visible_links=[],this.connecting_links=null,this.viewport=i.viewport||null,t==null||t.attachCanvas(this),this.setCanvas(s,i.skip_events),this.clear(),i.skip_render||this.startRendering(),this.autoresize=i.autoresize}get read_only(){return this.state.readOnly}set read_only(s){this.state.readOnly=s,M(this,be,Ue).call(this)}get isDragging(){return this.state.draggingItems}set isDragging(s){this.state.draggingItems=s}get hoveringOver(){return this.state.hoveringOver}set hoveringOver(s){this.state.hoveringOver=s,M(this,be,Ue).call(this)}get pointer_is_down(){return this.pointer.isDown}get pointer_is_double(){return this.pointer.isDouble}get dragging_canvas(){return this.state.draggingCanvas}set dragging_canvas(s){this.state.draggingCanvas=s,M(this,be,Ue).call(this)}get title_text_font(){return`${LiteGraph.NODE_TEXT_SIZE}px Arial`}get inner_text_font(){return`normal ${LiteGraph.NODE_SUBTEXT_SIZE}px Arial`}get maximumFps(){return O(this,ae)>Number.EPSILON?O(this,ae)/1e3:0}set maximumFps(s){Y(this,ae,s>Number.EPSILON?1e3/s:0)}get round_radius(){return LiteGraph.ROUND_RADIUS}set round_radius(s){LiteGraph.ROUND_RADIUS=s}get low_quality(){return this.ds.scale<this.low_quality_zoom_threshold}static getFileExtension(s){const t=s.indexOf("?");t!==-1&&(s=s.substring(0,t));const i=s.lastIndexOf(".");return i===-1?"":s.substring(i+1).toLowerCase()}static onGroupAdd(s,t,i){const n=_LGraphCanvas.active_canvas,o=new LiteGraph.LGraphGroup;o.pos=n.convertEventToCanvasOffset(i),n.graph.add(o)}static getBoundaryNodes(s){const t=Array.isArray(s)?s:Object.values(s);return getBoundaryNodes(t)??{top:null,right:null,bottom:null,left:null}}static alignNodes(s,t,i){alignNodes(Object.values(s),t,i),_LGraphCanvas.active_canvas.setDirty(!0,!0)}static onNodeAlign(s,t,i,n,o){new LiteGraph.ContextMenu(["Top","Bottom","Left","Right"],{event:i,callback:r,parentMenu:n});function r(a){alignNodes(Object.values(_LGraphCanvas.active_canvas.selected_nodes),a.toLowerCase(),o),_LGraphCanvas.active_canvas.setDirty(!0,!0)}}static onGroupAlign(s,t,i,n){new LiteGraph.ContextMenu(["Top","Bottom","Left","Right"],{event:i,callback:o,parentMenu:n});function o(r){alignNodes(Object.values(_LGraphCanvas.active_canvas.selected_nodes),r.toLowerCase()),_LGraphCanvas.active_canvas.setDirty(!0,!0)}}static createDistributeMenu(s,t,i,n,o){new LiteGraph.ContextMenu(["Vertically","Horizontally"],{event:i,callback:r,parentMenu:n});function r(a){const l=_LGraphCanvas.active_canvas;distributeNodes(Object.values(l.selected_nodes),a==="Horizontally"),l.setDirty(!0,!0)}}static onMenuAdd(s,t,i,n,o){const r=_LGraphCanvas.active_canvas,a=r.getCanvasWindow(),l=r.graph;if(!l)return;function u(p,d){const c=LiteGraph.getNodeTypesCategories(r.filter||l.filter).filter(function(f){return f.startsWith(p)}),_=[];c.map(function(f){if(!f)return;const m=new RegExp("^("+p+")"),w=f.replace(m,"").split("/")[0],b=p===""?w+"/":p+w+"/";let k=w;k.indexOf("::")!=-1&&(k=k.split("::")[1]),_.findIndex(function(T){return T.value===b})===-1&&_.push({value:b,content:k,has_submenu:!0,callback:function(T,A,E,C){u(T.value,C)}})}),LiteGraph.getNodeTypesInCategory(p.slice(0,-1),r.filter||l.filter).map(function(f){if(f.skip_list)return;const m={value:f.type,content:f.title,has_submenu:!1,callback:function(w,b,k,L){const T=L.getFirstEvent();r.graph.beforeChange();const A=LiteGraph.createNode(w.value);A&&(A.pos=r.convertEventToCanvasOffset(T),r.graph.add(A)),o==null||o(A),r.graph.afterChange()}};_.push(m)}),new LiteGraph.ContextMenu(_,{event:i,parentMenu:d},a)}return u("",n),!1}static onMenuCollapseAll(){}static onMenuNodeEdit(){}static showMenuNodeOptionalInputs(s,t,i,n,o){var c;if(!o)return;const r=this,a=_LGraphCanvas.active_canvas,l=a.getCanvasWindow();t=o.onGetInputs?o.onGetInputs():o.optional_inputs;let u=[];if(t)for(let _=0;_<t.length;_++){const g=t[_];if(!g){u.push(null);continue}let f=g[0];g[2]||(g[2]={}),g[2].label&&(f=g[2].label),g[2].removable=!0;const m={content:f,value:g};g[1]==LiteGraph.ACTION&&(m.className="event"),u.push(m)}const p=(c=o.onMenuNodeInputs)==null?void 0:c.call(o,u);if(p&&(u=p),!u.length){console.log("no input entries");return}new LiteGraph.ContextMenu(u,{event:i,callback:d,parentMenu:n,node:o},l);function d(_,g,f){var m,w;o&&((m=_.callback)==null||m.call(r,o,_,g,f),_.value&&(o.graph.beforeChange(),o.addInput(_.value[0],_.value[1],_.value[2]),(w=o.onNodeInputAdd)==null||w.call(o,_.value),a.setDirty(!0,!0),o.graph.afterChange()))}return!1}static showMenuNodeOptionalOutputs(s,t,i,n,o){var c;if(!o)return;const r=this,a=_LGraphCanvas.active_canvas,l=a.getCanvasWindow();t=o.onGetOutputs?o.onGetOutputs():o.optional_outputs;let u=[];if(t)for(let _=0;_<t.length;_++){const g=t[_];if(!g){u.push(null);continue}if(o.flags&&o.flags.skip_repeated_outputs&&o.findOutputSlot(g[0])!=-1)continue;let f=g[0];g[2]||(g[2]={}),g[2].label&&(f=g[2].label),g[2].removable=!0;const m={content:f,value:g};g[1]==LiteGraph.EVENT&&(m.className="event"),u.push(m)}this.onMenuNodeOutputs&&(u=this.onMenuNodeOutputs(u)),LiteGraph.do_add_triggers_slots&&o.findOutputSlot("onExecuted")==-1&&u.push({content:"On Executed",value:["onExecuted",LiteGraph.EVENT,{nameLocked:!0}],className:"event"});const p=(c=o.onMenuNodeOutputs)==null?void 0:c.call(o,u);if(p&&(u=p),!u.length)return;new LiteGraph.ContextMenu(u,{event:i,callback:d,parentMenu:n,node:o},l);function d(_,g,f){var b;if(!o||(_.callback&&_.callback.call(r,o,_,g,f),!_.value))return;const m=_.value[1];if(m&&(typeof m=="object"||Array.isArray(m))){const k=[];for(const L in m)k.push({content:L,value:m[L]});return new LiteGraph.ContextMenu(k,{event:g,callback:d,parentMenu:n,node:o}),!1}const w=o.graph;w.beforeChange(),o.addOutput(_.value[0],_.value[1],_.value[2]),(b=o.onNodeOutputAdd)==null||b.call(o,_.value),a.setDirty(!0,!0),w.afterChange()}return!1}static onShowMenuNodeProperties(s,t,i,n,o){if(!o||!o.properties)return;const r=_LGraphCanvas.active_canvas,a=r.getCanvasWindow(),l=[];for(const p in o.properties){s=o.properties[p]!==void 0?o.properties[p]:" ",typeof s=="object"&&(s=JSON.stringify(s));const d=o.getPropertyInfo(p);(d.type=="enum"||d.type=="combo")&&(s=_LGraphCanvas.getPropertyPrintableValue(s,d.values)),s=_LGraphCanvas.decodeHTML(stringOrNull(s)),l.push({content:"<span class='property_name'>"+(d.label||p)+"</span><span class='property_value'>"+s+"</span>",value:p})}if(!l.length)return;new LiteGraph.ContextMenu(l,{event:i,callback:u,parentMenu:n,allow_html:!0,node:o},a);function u(p){if(!o)return;const d=this.getBoundingClientRect();r.showEditPropertyValue(o,p.value,{position:[d.left,d.top]})}return!1}static decodeHTML(s){const t=document.createElement("div");return t.innerText=s,t.innerHTML}static onMenuResizeNode(s,t,i,n,o){if(!o)return;const r=function(l){var u;l.size=l.computeSize(),(u=l.onResize)==null||u.call(l,l.size)},a=_LGraphCanvas.active_canvas;if(!a.selected_nodes||Object.keys(a.selected_nodes).length<=1)r(o);else for(const l in a.selected_nodes)r(a.selected_nodes[l]);a.setDirty(!0,!0)}static onShowPropertyEditor(s,t,i,n,o){const r=s.property||"title",a=o[r],l=document.createElement("div");l.is_modified=!1,l.className="graphdialog",l.innerHTML="<span class='name'></span><input autofocus type='text' class='value'/><button>OK</button>",l.close=function(){var L;(L=l.parentNode)==null||L.removeChild(l)};const u=l.querySelector(".name");u.innerText=r;const p=l.querySelector(".value");p&&(p.value=a,p.addEventListener("blur",function(){this.focus()}),p.addEventListener("keydown",function(L){if(l.is_modified=!0,L.keyCode==27)l.close();else if(L.keyCode==13)b();else if(L.keyCode!=13&&L.target.localName!="textarea")return;L.preventDefault(),L.stopPropagation()}));const d=_LGraphCanvas.active_canvas,c=d.canvas,_=c.getBoundingClientRect();let g=-20,f=-20;_&&(g-=_.left,f-=_.top),i?(l.style.left=i.clientX+g+"px",l.style.top=i.clientY+f+"px"):(l.style.left=c.width*.5+g+"px",l.style.top=c.height*.5+f+"px"),l.querySelector("button").addEventListener("click",b),c.parentNode.appendChild(l),p==null||p.focus();let w=null;l.addEventListener("mouseleave",function(){LiteGraph.dialog_close_on_mouse_leave&&!l.is_modified&&LiteGraph.dialog_close_on_mouse_leave&&(w=setTimeout(l.close,LiteGraph.dialog_close_on_mouse_leave_delay))}),l.addEventListener("mouseenter",function(){LiteGraph.dialog_close_on_mouse_leave&&w&&clearTimeout(w)});function b(){p&&k(p.value)}function k(L){var T;s.type=="Number"?L=Number(L):s.type=="Boolean"&&(L=!!L),o[r]=L,(T=l.parentNode)==null||T.removeChild(l),d.setDirty(!0,!0)}}static getPropertyPrintableValue(s,t){if(!t||Array.isArray(t))return String(s);if(typeof t=="object"){let i="";for(const n in t)if(t[n]==s){i=n;break}return String(s)+" ("+i+")"}}static onMenuNodeCollapse(s,t,i,n,o){o.graph.beforeChange();const r=function(l){l.collapse()},a=_LGraphCanvas.active_canvas;if(!a.selected_nodes||Object.keys(a.selected_nodes).length<=1)r(o);else for(const l in a.selected_nodes)r(a.selected_nodes[l]);o.graph.afterChange()}static onMenuToggleAdvanced(s,t,i,n,o){o.graph.beforeChange();const r=function(l){l.toggleAdvanced()},a=_LGraphCanvas.active_canvas;if(!a.selected_nodes||Object.keys(a.selected_nodes).length<=1)r(o);else for(const l in a.selected_nodes)r(a.selected_nodes[l]);o.graph.afterChange()}static onMenuNodePin(s,t,i,n,o){}static onMenuNodeMode(s,t,i,n,o){new LiteGraph.ContextMenu(LiteGraph.NODE_MODES,{event:i,callback:r,parentMenu:n,node:o});function r(a){if(!o)return;const l=Object.values(LiteGraph.NODE_MODES).indexOf(a),u=function(d){l>=0&&LiteGraph.NODE_MODES[l]?d.changeMode(l):(console.warn("unexpected mode: "+a),d.changeMode(LGraphEventMode.ALWAYS))},p=_LGraphCanvas.active_canvas;if(!p.selected_nodes||Object.keys(p.selected_nodes).length<=1)u(o);else for(const d in p.selected_nodes)u(p.selected_nodes[d])}return!1}static onMenuNodeColors(s,t,i,n,o){if(!o)throw"no node for color";const r=[];r.push({value:null,content:"<span style='display: block; padding-left: 4px;'>No color</span>"});for(const l in _LGraphCanvas.node_colors){const u=_LGraphCanvas.node_colors[l];s={value:l,content:"<span style='display: block; color: #999; padding-left: 4px; border-left: 8px solid "+u.color+"; background-color:"+u.bgcolor+"'>"+l+"</span>"},r.push(s)}new LiteGraph.ContextMenu(r,{event:i,callback:a,parentMenu:n,node:o});function a(l){if(!o)return;const u=l.value?_LGraphCanvas.node_colors[l.value]:null,p=function(c){u?c instanceof LGraphGroup?c.color=u.groupcolor:(c.color=u.color,c.bgcolor=u.bgcolor):(delete c.color,delete c.bgcolor)},d=_LGraphCanvas.active_canvas;if(!d.selected_nodes||Object.keys(d.selected_nodes).length<=1)p(o);else for(const c in d.selected_nodes)p(d.selected_nodes[c]);d.setDirty(!0,!0)}return!1}static onMenuNodeShapes(s,t,i,n,o){if(!o)throw"no node passed";new LiteGraph.ContextMenu(LiteGraph.VALID_SHAPES,{event:i,callback:r,parentMenu:n,node:o});function r(a){if(!o)return;o.graph.beforeChange();const l=function(p){p.shape=a},u=_LGraphCanvas.active_canvas;if(!u.selected_nodes||Object.keys(u.selected_nodes).length<=1)l(o);else for(const p in u.selected_nodes)l(u.selected_nodes[p]);o.graph.afterChange(),u.setDirty(!0)}return!1}static onMenuNodeRemove(s,t,i,n,o){if(!o)throw"no node passed";const r=o.graph;r.beforeChange();const a=function(u){u.removable!==!1&&r.remove(u)},l=_LGraphCanvas.active_canvas;if(!l.selected_nodes||Object.keys(l.selected_nodes).length<=1)a(o);else for(const u in l.selected_nodes)a(l.selected_nodes[u]);r.afterChange(),l.setDirty(!0,!0)}static onMenuNodeClone(s,t,i,n,o){const r=o.graph;r.beforeChange();const a=new Set,l=function(p,d){if(p.clonable===!1)return;const c=p.clone();c&&(c.pos=[p.pos[0]+5,p.pos[1]+5],p.graph.add(c),d.add(c))},u=_LGraphCanvas.active_canvas;if(!u.selected_nodes||Object.keys(u.selected_nodes).length<=1)l(o,a);else for(const p in u.selected_nodes)l(u.selected_nodes[p],a);a.size&&u.selectNodes([...a]),r.afterChange(),u.setDirty(!0,!0)}clear(){var s;this.frame=0,this.last_draw_time=0,this.render_time=0,this.fps=0,this.dragging_rectangle=null,this.selected_nodes={},this.selected_group=null,this.visible_nodes=[],this.node_over=null,this.node_capturing_input=null,this.connecting_links=null,this.highlighted_links={},this.dragging_canvas=!1,M(this,te,ne).call(this),this.dirty_area=null,this.node_in_panel=null,this.node_widget=null,this.last_mouse=[0,0],this.last_mouseclick=0,this.pointer.reset(),this.visible_area.set([0,0,0,0]),(s=this.onClear)==null||s.call(this)}setGraph(s,t){if(this.graph!=s){if(t||this.clear(),!s&&this.graph){this.graph.detachCanvas(this);return}s.attachCanvas(this),this.setDirty(!0,!0)}}getCurrentGraph(){return this.graph}setCanvas(s,t){const i=M(this,Qe,mt).call(this,s);if(i===this.canvas||(!i&&this.canvas&&!t&&this.unbindEvents(),this.canvas=i,this.ds.element=i,this.pointer.element=i,!i))return;if(i.className+=" lgraphcanvas",i.data=this,i.tabindex="1",this.bgcanvas=null,this.bgcanvas||(this.bgcanvas=document.createElement("canvas"),this.bgcanvas.width=this.canvas.width,this.bgcanvas.height=this.canvas.height),i.getContext==null)throw i.localName!="canvas"?"Element supplied for LGraphCanvas must be a <canvas> element, you passed a "+i.localName:"This browser doesn't support Canvas";(this.ctx=i.getContext("2d"))==null&&(i.webgl_enabled||console.warn("This canvas seems to be WebGL, enabling WebGL renderer"),this.enableWebGL()),t||this.bindEvents()}_doNothing(s){return s.preventDefault(),!1}_doReturnTrue(s){return s.preventDefault(),!0}bindEvents(){if(this._events_binded){console.warn("LGraphCanvas: events already binded");return}const s=this.canvas,i=this.getCanvasWindow().document;this._mousedown_callback=this.processMouseDown.bind(this),this._mousewheel_callback=this.processMouseWheel.bind(this),this._mousemove_callback=this.processMouseMove.bind(this),this._mouseup_callback=this.processMouseUp.bind(this),this._mouseout_callback=this.processMouseOut.bind(this),this._mousecancel_callback=this.processMouseCancel.bind(this),LiteGraph.pointerListenerAdd(s,"down",this._mousedown_callback,!0),s.addEventListener("mousewheel",this._mousewheel_callback,!1),LiteGraph.pointerListenerAdd(s,"up",this._mouseup_callback,!0),LiteGraph.pointerListenerAdd(s,"move",this._mousemove_callback),s.addEventListener("pointerout",this._mouseout_callback),s.addEventListener("pointercancel",this._mousecancel_callback,!0),s.addEventListener("contextmenu",this._doNothing),s.addEventListener("DOMMouseScroll",this._mousewheel_callback,!1),this._key_callback=this.processKey.bind(this),s.addEventListener("keydown",this._key_callback,!0),i.addEventListener("keyup",this._key_callback,!0),this._ondrop_callback=this.processDrop.bind(this),s.addEventListener("dragover",this._doNothing,!1),s.addEventListener("dragend",this._doNothing,!1),s.addEventListener("drop",this._ondrop_callback,!1),s.addEventListener("dragenter",this._doReturnTrue,!1),this._events_binded=!0}unbindEvents(){if(!this._events_binded){console.warn("LGraphCanvas: no events binded");return}const t=this.getCanvasWindow().document;this.canvas.removeEventListener("pointercancel",this._mousecancel_callback),this.canvas.removeEventListener("pointerout",this._mouseout_callback),LiteGraph.pointerListenerRemove(this.canvas,"move",this._mousemove_callback),LiteGraph.pointerListenerRemove(this.canvas,"up",this._mouseup_callback),LiteGraph.pointerListenerRemove(this.canvas,"down",this._mousedown_callback),this.canvas.removeEventListener("mousewheel",this._mousewheel_callback),this.canvas.removeEventListener("DOMMouseScroll",this._mousewheel_callback),this.canvas.removeEventListener("keydown",this._key_callback),t.removeEventListener("keyup",this._key_callback),this.canvas.removeEventListener("contextmenu",this._doNothing),this.canvas.removeEventListener("drop",this._ondrop_callback),this.canvas.removeEventListener("dragenter",this._doReturnTrue),this._mousedown_callback=null,this._mousewheel_callback=null,this._key_callback=null,this._ondrop_callback=null,this._events_binded=!1}enableWebGL(){if(typeof GL>"u")throw"litegl.js must be included to use a WebGL canvas";if(typeof enableWebGLCanvas>"u")throw"webglCanvas.js must be included to use this feature";this.gl=this.ctx=enableWebGLCanvas(this.canvas),this.ctx.webgl=!0,this.bgcanvas=this.canvas,this.bgctx=this.gl,this.canvas.webgl_enabled=!0}setDirty(s,t){s&&(this.dirty_canvas=!0),t&&(this.dirty_bgcanvas=!0)}getCanvasWindow(){if(!this.canvas)return window;const s=this.canvas.ownerDocument;return s.defaultView||s.parentWindow}startRendering(){if(this.is_rendering)return;this.is_rendering=!0,s.call(this);function s(){this.pause_rendering||this.draw();const t=this.getCanvasWindow();if(this.is_rendering)if(O(this,ae)>0){const i=O(this,ae)-(LiteGraph.getTime()-this.last_draw_time);setTimeout(s.bind(this),Math.max(1,i))}else t.requestAnimationFrame(s.bind(this))}}stopRendering(){this.is_rendering=!1}blockClick(){this.block_click=!0,this.last_mouseclick=0}getWidgetAtCursor(s){if(s??(s=this.node_over),!s.widgets)return null;const t=this.graph_mouse,i=t[0]-s.pos[0],n=t[1]-s.pos[1];for(const o of s.widgets){if(o.hidden||o.advanced&&!s.showAdvanced)continue;let r,a;if(o.computeSize?[r,a]=o.computeSize(s.size[0]):(r=o.width||s.size[0],a=LiteGraph.NODE_WIDGET_HEIGHT),o.last_y!==void 0&&i>=6&&i<=r-12&&n>=o.last_y&&n<=o.last_y+a)return o}return null}updateMouseOverNodes(s,t){var o,r;const i=this.graph._nodes,n=i.length;for(let a=0;a<n;++a)i[a].mouseOver&&s!=i[a]&&(i[a].mouseOver=null,this._highlight_input=null,this._highlight_pos=null,this.link_over_widget=null,i[a].lostFocusAt=LiteGraph.getTime(),(r=(o=this.node_over)==null?void 0:o.onMouseLeave)==null||r.call(o,t),this.node_over=null,this.dirty_canvas=!0)}processMouseDown(s){var u,p;if(this.dragZoomEnabled&&s.ctrlKey&&s.shiftKey&&!s.altKey&&s.buttons){Y(this,se,{pos:[s.x,s.y],scale:this.ds.scale});return}const{graph:t,pointer:i}=this;if(this.adjustMouseEvent(s),s.isPrimary&&i.down(s),this.set_canvas_dirty_on_mouse_event&&(this.dirty_canvas=!0),!t)return;const n=this.getCanvasWindow();_LGraphCanvas.active_canvas=this;const o=s.clientX,r=s.clientY;if(this.ds.viewport=this.viewport,!(!this.viewport||isInRect(o,r,this.viewport)))return;const l=t.getNodeOnPos(s.canvasX,s.canvasY,this.visible_nodes);this.mouse[0]=o,this.mouse[1]=r,this.graph_mouse[0]=s.canvasX,this.graph_mouse[1]=s.canvasY,this.last_click_position=[this.mouse[0],this.mouse[1]],i.isDouble=i.isDown&&s.isPrimary,i.isDown=!0,this.canvas.focus(),LiteGraph.closeAllContextMenus(n),((u=this.onMouse)==null?void 0:u.call(this,s))!=!0&&(s.button===0&&!i.isDouble?M(this,et,yt).call(this,s,l):s.button===1?M(this,st,kt).call(this,s,l):(s.button===2||i.isDouble)&&this.allow_interaction&&!this.read_only&&(l&&this.processSelect(l,s,!0),this.processContextMenu(l,s)),this.last_mouse=[o,r],this.last_mouseclick=LiteGraph.getTime(),this.last_mouse_dragging=!0,t.change(),(!n.document.activeElement||n.document.activeElement.nodeName.toLowerCase()!="input"&&n.document.activeElement.nodeName.toLowerCase()!="textarea")&&s.preventDefault(),s.stopPropagation(),(p=this.onMouseDown)==null||p.call(this,s))}processMouseMove(s){var l,u,p,d,c,_,g;if(this.dragZoomEnabled&&s.ctrlKey&&s.shiftKey&&O(this,se)){M(this,nt,Lt).call(this,s);return}if(this.autoresize&&this.resize(),this.set_canvas_dirty_on_mouse_event&&(this.dirty_canvas=!0),!this.graph)return;_LGraphCanvas.active_canvas=this,this.adjustMouseEvent(s);const t=[s.clientX,s.clientY];this.mouse[0]=t[0],this.mouse[1]=t[1];const i=[t[0]-this.last_mouse[0],t[1]-this.last_mouse[1]];if(this.last_mouse=t,this.graph_mouse[0]=s.canvasX,this.graph_mouse[1]=s.canvasY,s.isPrimary&&this.pointer.move(s),this.block_click){s.preventDefault();return}if(s.dragging=this.last_mouse_dragging,this.node_widget){const[f,m]=this.node_widget;if(m!=null&&m.mouse){const w=s.canvasX-f.pos[0],b=s.canvasY-f.pos[1],k=m.mouse(s,[w,b],f);k!=null&&(this.dirty_canvas=k)}}let n=CanvasItem.Nothing;const o=this.graph.getNodeOnPos(s.canvasX,s.canvasY,this.visible_nodes),{resizingGroup:r}=this,a=this.dragging_rectangle;if(a)a[2]=s.canvasX-a[0],a[3]=s.canvasY-a[1],this.dirty_canvas=!0;else if(r)n|=CanvasItem.ResizeSe|CanvasItem.Group;else if(this.dragging_canvas)this.ds.offset[0]+=i[0]/this.ds.scale,this.ds.offset[1]+=i[1]/this.ds.scale,M(this,te,ne).call(this);else if((this.allow_interaction||o&&o.flags.allow_interaction)&&!this.read_only){if(this.connecting_links&&(this.dirty_canvas=!0),this.updateMouseOverNodes(o,s),o){n|=CanvasItem.Node,o.redraw_on_mouse&&(this.dirty_canvas=!0);const f=[0,0],m=this.isOverNodeInput(o,s.canvasX,s.canvasY,f),w=this.isOverNodeOutput(o,s.canvasX,s.canvasY,f),b=this.getWidgetAtCursor(o);if(o.mouseOver||(o.mouseOver={inputId:null,outputId:null,overWidget:null},this.node_over=o,this.dirty_canvas=!0,(l=o.onMouseEnter)==null||l.call(o,s)),(u=o.onMouseMove)==null||u.call(o,s,[s.canvasX-o.pos[0],s.canvasY-o.pos[1]],this),o.mouseOver.inputId!==m||o.mouseOver.outputId!==w||o.mouseOver.overWidget!==b){if(o.mouseOver.inputId=m,o.mouseOver.outputId=w,o.mouseOver.overWidget=b,(p=this.connecting_links)!=null&&p.length){const k=this.connecting_links[0];let L=null,T=null,A=null;if(k.node!==o){if(k.output)if(m===-1&&w===-1){if(this.getWidgetLinkType&&b){const E=this.getWidgetLinkType(b,o);E&&LiteGraph.isValidConnection(k.output.type,E)&&((c=(d=k.node).isValidWidgetLink)==null?void 0:c.call(d,k.output.slot_index,o,b))!==!1&&(A=b,this.link_over_widget_type=E)}if(!A){const E=k.node.findConnectByTypeSlot(!0,o,k.output.type);E!==null&&E>=0&&(o.getConnectionPos(!0,E,f),L=f,T=o.inputs[E])}}else m!=-1&&o.inputs[m]&&LiteGraph.isValidConnection(k.output.type,o.inputs[m].type)&&m!=-1&&o.inputs[m]&&LiteGraph.isValidConnection(k.output.type,o.inputs[m].type)&&(L=f,T=o.inputs[m]);else if(k.input)if(m===-1&&w===-1){const E=k.node.findConnectByTypeSlot(!1,o,k.input.type);E!==null&&E>=0&&(o.getConnectionPos(!1,E,f),L=f)}else w!=-1&&o.outputs[w]&&LiteGraph.isValidConnection(k.input.type,o.outputs[w].type)&&(L=f)}this._highlight_pos=L,this._highlight_input=T,this.link_over_widget=A}this.dirty_canvas=!0}o.inResizeCorner(s.canvasX,s.canvasY)&&(n|=CanvasItem.ResizeSe)}else{const f=M(this,rt,Ct).call(this,s);if(this.over_link_center!==f&&(n|=CanvasItem.Link,this.over_link_center=f,this.dirty_bgcanvas=!0),this.canvas){const m=this.graph.getGroupOnPos(s.canvasX,s.canvasY);m&&!s.ctrlKey&&!this.read_only&&m.isInResize(s.canvasX,s.canvasY)&&(n|=CanvasItem.ResizeSe)}}if(this.node_capturing_input&&this.node_capturing_input!=o&&((g=(_=this.node_capturing_input).onMouseMove)==null||g.call(_,s,[s.canvasX-this.node_capturing_input.pos[0],s.canvasY-this.node_capturing_input.pos[1]],this)),this.isDragging){const f=this.selectedItems,m=s.ctrlKey?f:getAllNestedItems(f),w=i[0]/this.ds.scale,b=i[1]/this.ds.scale;for(const k of m)k.move(w,b,!0);M(this,te,ne).call(this)}this.resizing_node&&(n|=CanvasItem.ResizeSe)}this.hoveringOver=n,s.preventDefault()}processMouseUp(s){var r,a,l,u,p;if(s.isPrimary===!1)return;const{graph:t,pointer:i}=this;if(!t)return;_LGraphCanvas.active_canvas=this,this.adjustMouseEvent(s);const n=LiteGraph.getTime();if(s.click_time=n-this.last_mouseclick,i.up(s)===!0){i.isDown=!1,i.isDouble=!1,this.connecting_links=null,this.dragging_canvas=!1,t.change(),s.stopPropagation(),s.preventDefault();return}if(this.last_mouse_dragging=!1,this.last_click_position=null,this.block_click&&(this.block_click=!1),s.button===0){this.selected_group=null,this.isDragging=!1;const d=s.canvasX,c=s.canvasY,_=t.getNodeOnPos(d,c,this.visible_nodes);if((r=this.connecting_links)!=null&&r.length){const g=this.connecting_links[0];if(_){for(const f of this.connecting_links)if(M(this,te,ne).call(this),f.output){const m=this.isOverNodeInput(_,d,c);m!=-1?f.node.connect(f.slot,_,m,f.afterRerouteId):this.link_over_widget?(this.emitEvent({subType:"connectingWidgetLink",link:f,node:_,widget:this.link_over_widget}),this.link_over_widget=null):f.node.connectByType(f.slot,_,f.output.type,{afterRerouteId:f.afterRerouteId})}else if(f.input){const m=this.isOverNodeOutput(_,d,c);m!=-1?_.connect(m,f.node,f.slot,f.afterRerouteId):f.node.connectByTypeOutput(f.slot,_,f.input.type,{afterRerouteId:f.afterRerouteId})}}else if(g.input||g.output){const f=g.output?{node_from:g.node,slot_from:g.output,type_filter_in:g.output.type}:{node_to:g.node,slot_from:g.input,type_filter_out:g.input.type},m={links:this.connecting_links};this.emitEvent({subType:"empty-release",originalEvent:s,linkReleaseContext:m}),LiteGraph.release_link_on_empty_shows_menu&&(s.shiftKey?this.allow_searchbox&&this.showSearchBox(s,f):g.output?this.showConnectionMenu({nodeFrom:g.node,slotFrom:g.output,e:s}):g.input&&this.showConnectionMenu({nodeTo:g.node,slotTo:g.input,e:s}))}}else this.dirty_canvas=!0,(l=(a=this.node_over)==null?void 0:a.onMouseUp)==null||l.call(a,s,[d-this.node_over.pos[0],c-this.node_over.pos[1]],this),(p=(u=this.node_capturing_input)==null?void 0:u.onMouseUp)==null||p.call(u,s,[d-this.node_capturing_input.pos[0],c-this.node_capturing_input.pos[1]]);this.connecting_links=null}else s.button===1?(this.dirty_canvas=!0,this.dragging_canvas=!1):s.button===2&&(this.dirty_canvas=!0);i.isDown=!1,i.isDouble=!1,t.change(),s.stopPropagation(),s.preventDefault()}processMouseOut(s){this.adjustMouseEvent(s),this.updateMouseOverNodes(null,s)}processMouseCancel(s){console.warn("Pointer cancel!"),this.pointer.reset()}processMouseWheel(s){if(!this.graph||!this.allow_dragcanvas)return;const t=s.wheelDeltaY??s.detail*-60;this.adjustMouseEvent(s);const i=[s.clientX,s.clientY];if(this.viewport&&!isPointInRect(i,this.viewport))return;let n=this.ds.scale;t>0?n*=this.zoom_speed:t<0&&(n*=1/this.zoom_speed),this.ds.changeScale(n,[s.clientX,s.clientY]),this.graph.change(),s.preventDefault()}isOverNodeInput(s,t,i,n){var o,r,a;if(s.inputs)for(let l=0,u=s.inputs.length;l<u;++l){const p=s.inputs[l],d=s.getConnectionPos(!0,l);let c=!1;if(s.horizontal)c=isInRectangle(t,i,d[0]-5,d[1]-10,10,20);else{const _=20+((((o=p.label)==null?void 0:o.length)??((r=p.localized_name)==null?void 0:r.length)??((a=p.name)==null?void 0:a.length))||3)*7;c=isInRectangle(t,i,d[0]-10,d[1]-10,_,20)}if(c)return n&&(n[0]=d[0],n[1]=d[1]),l}return-1}isOverNodeOutput(s,t,i,n){if(s.outputs)for(let o=0,r=s.outputs.length;o<r;++o){const a=s.getConnectionPos(!1,o);let l=!1;if(s.horizontal?l=isInRectangle(t,i,a[0]-5,a[1]-10,10,20):l=isInRectangle(t,i,a[0]-10,a[1]-10,40,20),l)return n&&(n[0]=a[0],n[1]=a[1]),o}return-1}processKey(s){var i,n,o,r,a,l;if(Y(this,Fe,s.shiftKey),!this.graph)return;let t=!1;if(s.target.localName!="input"){if(s.type=="keydown"){if(s.keyCode==32?(this.read_only=!0,this._previously_dragging_canvas===null&&(this._previously_dragging_canvas=this.dragging_canvas),this.dragging_canvas=this.pointer.isDown,t=!0):s.keyCode==27?((i=this.node_panel)==null||i.close(),(n=this.options_panel)==null||n.close(),t=!0):s.keyCode==65&&s.ctrlKey?(this.selectItems(),t=!0):s.keyCode===67&&(s.metaKey||s.ctrlKey)&&!s.shiftKey?this.selected_nodes&&(this.copyToClipboard(),t=!0):s.keyCode===86&&(s.metaKey||s.ctrlKey)?this.pasteFromClipboard({connectInputs:s.shiftKey}):(s.keyCode==46||s.keyCode==8)&&s.target.localName!="input"&&s.target.localName!="textarea"&&(this.deleteSelected(),t=!0),this.selected_nodes)for(const u in this.selected_nodes)(r=(o=this.selected_nodes[u]).onKeyDown)==null||r.call(o,s)}else if(s.type=="keyup"&&(s.keyCode==32&&(this.read_only=!1,this.dragging_canvas=(this._previously_dragging_canvas??!1)&&this.pointer.isDown,this._previously_dragging_canvas=null),this.selected_nodes))for(const u in this.selected_nodes)(l=(a=this.selected_nodes[u]).onKeyUp)==null||l.call(a,s);if(this.graph.change(),t)return s.preventDefault(),s.stopImmediatePropagation(),!1}}copyToClipboard(s){var i,n;const t={nodes:[],groups:[],reroutes:[],links:[]};for(const o of s??this.selectedItems)if(o instanceof LGraphNode){if(o.clonable===!1)continue;const r=(i=o.clone())==null?void 0:i.serialize();if(!r)continue;r.id=o.id,t.nodes.push(r);const a=(n=o.inputs)==null?void 0:n.map(l=>{var u;return(u=this.graph._links.get(l==null?void 0:l.link))==null?void 0:u.asSerialisable()}).filter(l=>!!l);if(!a)continue;t.links.push(...a)}else o instanceof LGraphGroup?t.groups.push(o.serialize()):this.reroutesEnabled&&o instanceof Reroute&&t.reroutes.push(o.asSerialisable());localStorage.setItem("litegrapheditor_clipboard",JSON.stringify(t))}emitEvent(s){this.canvas.dispatchEvent(new CustomEvent("litegraph:canvas",{bubbles:!0,detail:s}))}emitBeforeChange(){this.emitEvent({subType:"before-change"})}emitAfterChange(){this.emitEvent({subType:"after-change"})}_pasteFromClipboard(s={}){var g;const{connectInputs:t=!1,position:i=this.graph_mouse}=s;if(!LiteGraph.ctrl_shift_v_paste_connect_unselected_outputs&&t)return;const n=localStorage.getItem("litegrapheditor_clipboard");if(!n)return;const{graph:o}=this;o.beforeChange();const r=JSON.parse(n);r.nodes??(r.nodes=[]),r.groups??(r.groups=[]),r.reroutes??(r.reroutes=[]),r.links??(r.links=[]);let a=1/0,l=1/0;for(const f of[...r.nodes,...r.reroutes])f.pos[0]<a&&(a=f.pos[0]),f.pos[1]<l&&(l=f.pos[1]);if(r.groups)for(const f of r.groups)f.bounding[0]<a&&(a=f.bounding[0]),f.bounding[1]<l&&(l=f.bounding[1]);const u={created:[],nodes:new Map,links:new Map,reroutes:new Map},{created:p,nodes:d,links:c,reroutes:_}=u;for(const f of r.groups){f.id=void 0;const m=new LGraphGroup;m.configure(f),o.add(m),p.push(m)}for(const f of r.nodes){const m=LiteGraph.createNode(f.type);m&&(d.set(f.id,m),f.id=void 0,m.configure(f),o.add(m),p.push(m))}for(const f of r.reroutes){const{id:m}=f;f.id=void 0;const w=o.setReroute(f);p.push(w),_.set(m,w)}for(const f of _.values()){const m=_.get(f.parentId);m&&(f.parentId=m.id)}for(const f of r.links){let m=d.get(f.origin_id),w=(g=_.get(f.parentId))==null?void 0:g.id;t&&LiteGraph.ctrl_shift_v_paste_connect_unselected_outputs&&(m??(m=o.getNodeById(f.origin_id)),w??(w=f.parentId));const b=d.get(f.target_id);if(b){const k=m==null?void 0:m.connect(f.origin_slot,b,f.target_slot,w);k&&c.set(f.id,k)}}for(const f of _.values()){const m=[...f.linkIds].map(w=>{var b;return((b=c.get(w))==null?void 0:b.id)??w});f.update(f.parentId,void 0,m),f.validateLinks(o.links)||o.removeReroute(f.id)}for(const f of p)f.pos[0]+=i[0]-a,f.pos[1]+=i[1]-l;return this.selectItems(p),o.afterChange(),u}pasteFromClipboard(s={}){this.emitBeforeChange();try{this._pasteFromClipboard(s)}finally{this.emitAfterChange()}}processDrop(s){var a,l,u;s.preventDefault(),this.adjustMouseEvent(s);const t=s.clientX,i=s.clientY;if(!(!this.viewport||isInRect(t,i,this.viewport)))return;const o=[s.canvasX,s.canvasY],r=this.graph?this.graph.getNodeOnPos(o[0],o[1]):null;if(!r){((a=this.onDropItem)==null?void 0:a.call(this,s))||this.checkDropItem(s);return}if(r.onDropFile||r.onDropData){const p=s.dataTransfer.files;if(p&&p.length)for(let d=0;d<p.length;d++){const c=s.dataTransfer.files[0],_=c.name;if((l=r.onDropFile)==null||l.call(r,c),r.onDropData){const g=new FileReader;g.onload=function(m){const w=m.target.result;r.onDropData(w,_,c)};const f=c.type.split("/")[0];f=="text"||f==""?g.readAsText(c):f=="image"?g.readAsDataURL(c):g.readAsArrayBuffer(c)}}}return(u=r.onDropItem)!=null&&u.call(r,s)?!0:this.onDropItem?this.onDropItem(s):!1}checkDropItem(s){var r;if(!s.dataTransfer.files.length)return;const t=s.dataTransfer.files[0],i=_LGraphCanvas.getFileExtension(t.name).toLowerCase(),n=LiteGraph.node_types_by_file_extension[i];if(!n)return;this.graph.beforeChange();const o=LiteGraph.createNode(n.type);o.pos=[s.canvasX,s.canvasY],this.graph.add(o),(r=o.onDropFile)==null||r.call(o,t),this.graph.afterChange()}processNodeDblClicked(s){var t,i;(t=this.onShowNodePanel)==null||t.call(this,s),(i=this.onNodeDblClicked)==null||i.call(this,s),this.setDirty(!0)}processSelect(s,t,i=!1){var l;const n=t==null?void 0:t.shiftKey,o=t!=null&&(t.metaKey||t.ctrlKey),r=n||o,a=r||this.multi_select;if(!s)(!r||this.multi_select)&&this.deselectAll();else if(!s.selected||!this.selectedItems.has(s))a||this.deselectAll(s),this.select(s);else if(a&&!i)this.deselect(s);else if(!i)this.deselectAll(s);else return;(l=this.onSelectionChange)==null||l.call(this,this.selected_nodes),this.setDirty(!0)}select(s){var t,i,n,o;s.selected&&this.selectedItems.has(s)||(s.selected=!0,this.selectedItems.add(s),s instanceof LGraphNode&&((t=s.onSelected)==null||t.call(s),this.selected_nodes[s.id]=s,(i=this.onNodeSelected)==null||i.call(this,s),(n=s.inputs)==null||n.forEach(r=>this.highlighted_links[r.link]=!0),(o=s.outputs)==null||o.flatMap(r=>r.links).forEach(r=>this.highlighted_links[r]=!0)))}deselect(s){var t,i,n,o;!s.selected&&!this.selectedItems.has(s)||(s.selected=!1,this.selectedItems.delete(s),s instanceof LGraphNode&&((t=s.onDeselected)==null||t.call(s),delete this.selected_nodes[s.id],(i=this.onNodeDeselected)==null||i.call(this,s),(n=s.inputs)==null||n.forEach(r=>delete this.highlighted_links[r.link]),(o=s.outputs)==null||o.flatMap(r=>r.links).forEach(r=>delete this.highlighted_links[r])))}processNodeSelected(s,t){this.processSelect(s,t,t&&(t.shiftKey||t.metaKey||t.ctrlKey||this.multi_select))}selectNode(s,t){s==null?this.deselectAll():this.selectNodes([s],t)}get empty(){return this.graph.empty}get positionableItems(){return this.graph.positionableItems()}selectItems(s,t){var n;const i=s??this.positionableItems;t||this.deselectAll();for(const o of i)this.select(o);(n=this.onSelectionChange)==null||n.call(this,this.selected_nodes),this.setDirty(!0)}selectNodes(s,t){this.selectItems(s,t)}deselectNode(s){this.deselect(s)}deselectAll(s){var o,r,a,l;if(!this.graph)return;const t=this.selectedItems;let i;for(const u of t){if(u===s){i=u;continue}(o=u.onDeselected)==null||o.call(u),u.selected=!1}t.clear(),i&&t.add(i),this.setDirty(!0);const n=(s==null?void 0:s.id)==null?null:this.selected_nodes[s.id];this.selected_nodes={},this.current_node=null,this.highlighted_links={},s instanceof LGraphNode&&(n&&(this.selected_nodes[n.id]=n),(r=s.inputs)==null||r.forEach(u=>this.highlighted_links[u.link]=!0),(a=s.outputs)==null||a.flatMap(u=>u.links).forEach(u=>this.highlighted_links[u]=!0)),(l=this.onSelectionChange)==null||l.call(this,this.selected_nodes)}deselectAllNodes(){this.deselectAll()}deleteSelected(){var t;const{graph:s}=this;this.emitBeforeChange(),s.beforeChange();for(const i of this.selectedItems)if(i instanceof LGraphNode){const n=i;if(n.block_delete)continue;n.connectInputToOutput(),s.remove(n),(t=this.onNodeDeselected)==null||t.call(this,n)}else i instanceof LGraphGroup?s.remove(i):i instanceof Reroute&&s.removeReroute(i.id);this.selectedItems.clear(),this.selected_nodes={},this.selectedItems.clear(),this.current_node=null,this.highlighted_links={},this.setDirty(!0),s.afterChange(),this.emitAfterChange()}deleteSelectedNodes(){this.deleteSelected()}centerOnNode(s){const t=(window==null?void 0:window.devicePixelRatio)||1;this.ds.offset[0]=-s.pos[0]-s.size[0]*.5+this.canvas.width*.5/(this.ds.scale*t),this.ds.offset[1]=-s.pos[1]-s.size[1]*.5+this.canvas.height*.5/(this.ds.scale*t),this.setDirty(!0,!0)}adjustMouseEvent(s){let t=s.clientX,i=s.clientY;if(this.canvas){const n=this.canvas.getBoundingClientRect();t-=n.left,i-=n.top}s.safeOffsetX=t,s.safeOffsetY=i,s.deltaX===void 0&&(s.deltaX=t-this.last_mouse_position[0]),s.deltaY===void 0&&(s.deltaY=i-this.last_mouse_position[1]),this.last_mouse_position[0]=t,this.last_mouse_position[1]=i,s.canvasX=t/this.ds.scale-this.ds.offset[0],s.canvasY=i/this.ds.scale-this.ds.offset[1]}setZoom(s,t){this.ds.changeScale(s,t),M(this,te,ne).call(this)}convertOffsetToCanvas(s,t){return this.ds.convertOffsetToCanvas(s,t)}convertCanvasToOffset(s,t){return this.ds.convertCanvasToOffset(s,t)}convertEventToCanvasOffset(s){const t=this.canvas.getBoundingClientRect();return this.convertCanvasToOffset([s.clientX-t.left,s.clientY-t.top])}bringToFront(s){const t=this.graph._nodes.indexOf(s);t!=-1&&(this.graph._nodes.splice(t,1),this.graph._nodes.push(s))}sendToBack(s){const t=this.graph._nodes.indexOf(s);t!=-1&&(this.graph._nodes.splice(t,1),this.graph._nodes.unshift(s))}computeVisibleNodes(s,t){const i=t||[];i.length=0;const n=s||this.graph._nodes;for(const o of n)o.updateArea(),overlapBounding(this.visible_area,o.renderArea)&&i.push(o);return i}draw(s,t){var n;if(!this.canvas||this.canvas.width==0||this.canvas.height==0)return;const i=LiteGraph.getTime();this.render_time=(i-this.last_draw_time)*.001,this.last_draw_time=i,this.graph&&this.ds.computeVisibleArea(this.viewport),(this.dirty_canvas||s)&&this.computeVisibleNodes(null,this.visible_nodes),(this.dirty_bgcanvas||t||this.always_render_background||(n=this.graph)!=null&&n._last_trigger_time&&i-this.graph._last_trigger_time<1e3)&&this.drawBackCanvas(),(this.dirty_canvas||s)&&this.drawFrontCanvas(),this.fps=this.render_time?1/this.render_time:0,this.frame++}drawFrontCanvas(){var n,o,r,a,l,u;this.dirty_canvas=!1,this.ctx||(this.ctx=this.bgcanvas.getContext("2d"));const s=this.ctx;if(!s)return;const t=this.canvas;s.start2D&&!this.viewport&&(s.start2D(),s.restore(),s.setTransform(1,0,0,1,0,0));const i=this.viewport||this.dirty_area;if(i&&(s.save(),s.beginPath(),s.rect(i[0],i[1],i[2],i[3]),s.clip()),Y(this,ee,O(this,Fe)||LiteGraph.alwaysSnapToGrid?this.graph.getSnapToGridSize():void 0),this.clear_background&&(i?s.clearRect(i[0],i[1],i[2],i[3]):s.clearRect(0,0,t.width,t.height)),this.bgcanvas==this.canvas)this.drawBackCanvas();else{const p=window.devicePixelRatio;s.drawImage(this.bgcanvas,0,0,this.bgcanvas.width/p,this.bgcanvas.height/p)}if((n=this.onRender)==null||n.call(this,t,s),this.show_info&&this.renderInfo(s,i?i[0]:0,i?i[1]:0),this.graph){s.save(),this.ds.toCanvasContext(s);const p=this.visible_nodes,d=O(this,ee)&&this.isDragging;for(let c=0;c<p.length;++c){const _=p[c];s.save(),d&&this.selectedItems.has(_)&&this.drawSnapGuide(s,_),s.translate(_.pos[0],_.pos[1]),this.drawNode(_,s),s.restore()}if(this.render_execution_order&&this.drawExecutionOrder(s),this.graph.config.links_ontop&&this.drawConnections(s),(o=this.connecting_links)!=null&&o.length)for(const c of this.connecting_links){s.lineWidth=this.connections_width;let _=null;const g=c.output||c.input,f=g==null?void 0:g.type;let m=g==null?void 0:g.dir;m==null&&(c.output?m=c.node.horizontal?LinkDirection.DOWN:LinkDirection.RIGHT:m=c.node.horizontal?LinkDirection.UP:LinkDirection.LEFT);const w=g==null?void 0:g.shape;switch(f){case LiteGraph.EVENT:_=LiteGraph.EVENT_LINK_COLOR;break;default:_=LiteGraph.CONNECTING_LINK_COLOR}const b=((r=this.graph.reroutes.get(c.afterRerouteId))==null?void 0:r.pos)??c.pos,k=M(this,at,Et).call(this);this.renderLink(s,b,k,null,!1,null,_,m,c.direction??LinkDirection.CENTER),s.beginPath(),f===LiteGraph.EVENT||w===RenderShape.BOX?(s.rect(b[0]-6+.5,b[1]-5+.5,14,10),s.fill(),s.beginPath(),s.rect(this.graph_mouse[0]-6+.5,this.graph_mouse[1]-5+.5,14,10)):w===RenderShape.ARROW?(s.moveTo(b[0]+8,b[1]+.5),s.lineTo(b[0]-4,b[1]+6+.5),s.lineTo(b[0]-4,b[1]-6+.5),s.closePath()):(s.arc(b[0],b[1],4,0,Math.PI*2),s.fill(),s.beginPath(),s.arc(this.graph_mouse[0],this.graph_mouse[1],4,0,Math.PI*2)),s.fill(),M(this,lt,Nt).call(this,s,k)}if(this.dragging_rectangle){const{eDown:c,eMove:_}=this.pointer;if(s.strokeStyle="#FFF",c&&_){const g=s.getTransform(),f=window.devicePixelRatio;s.setTransform(f,0,0,f,0,0);const m=c.safeOffsetX,w=c.safeOffsetY;s.strokeRect(m,w,_.safeOffsetX-m,_.safeOffsetY-w),s.setTransform(g)}else{const[g,f,m,w]=this.dragging_rectangle;s.strokeRect(g,f,m,w)}}this.over_link_center&&this.render_link_tooltip?this.drawLinkTooltip(s,this.over_link_center):(a=this.onDrawLinkTooltip)==null||a.call(this,s,null),(l=this.onDrawForeground)==null||l.call(this,s,this.visible_area),s.restore()}(u=this.onDrawOverlay)==null||u.call(this,s),i&&s.restore(),s.finish2D&&s.finish2D()}renderInfo(s,t,i){t=t||10,i=i||this.canvas.offsetHeight-80,s.save(),s.translate(t,i),s.font="10px Arial",s.fillStyle="#888",s.textAlign="left",this.graph?(s.fillText("T: "+this.graph.globaltime.toFixed(2)+"s",5,13*1),s.fillText("I: "+this.graph.iteration,5,13*2),s.fillText("N: "+this.graph._nodes.length+" ["+this.visible_nodes.length+"]",5,13*3),s.fillText("V: "+this.graph._version,5,13*4),s.fillText("FPS:"+this.fps.toFixed(2),5,13*5)):s.fillText("No graph selected",5,13*1),s.restore()}drawBackCanvas(){var o,r;const s=this.bgcanvas;(s.width!=this.canvas.width||s.height!=this.canvas.height)&&(s.width=this.canvas.width,s.height=this.canvas.height),this.bgctx||(this.bgctx=this.bgcanvas.getContext("2d"));const t=this.bgctx;t.start&&t.start();const i=this.viewport||[0,0,t.canvas.width,t.canvas.height];this.clear_background&&t.clearRect(i[0],i[1],i[2],i[3]);const n=this.onRenderBackground?this.onRenderBackground(s,t):!1;if(!this.viewport){const a=window.devicePixelRatio;t.restore(),t.setTransform(a,0,0,a,0,0)}if(this.visible_links.length=0,this.graph){if(t.save(),this.ds.toCanvasContext(t),this.ds.scale<1.5&&!n&&this.clear_background_color&&(t.fillStyle=this.clear_background_color,t.fillRect(this.visible_area[0],this.visible_area[1],this.visible_area[2],this.visible_area[3])),this.background_image&&this.ds.scale>.5&&!n){if(this.zoom_modify_alpha?t.globalAlpha=(1-.5/this.ds.scale)*this.editor_alpha:t.globalAlpha=this.editor_alpha,t.imageSmoothingEnabled=!1,!this._bg_img||this._bg_img.name!=this.background_image){this._bg_img=new Image,this._bg_img.name=this.background_image,this._bg_img.src=this.background_image;const l=this;this._bg_img.onload=function(){l.draw(!0,!0)}}let a=this._pattern;a==null&&this._bg_img.width>0&&(a=t.createPattern(this._bg_img,"repeat"),this._pattern_img=this._bg_img,this._pattern=a),a&&(t.fillStyle=a,t.fillRect(this.visible_area[0],this.visible_area[1],this.visible_area[2],this.visible_area[3]),t.fillStyle="transparent"),t.globalAlpha=1,t.imageSmoothingEnabled=!0}this.graph._groups.length&&this.drawGroups(s,t),(o=this.onDrawBackground)==null||o.call(this,t,this.visible_area),this.render_canvas_border&&(t.strokeStyle="#235",t.strokeRect(0,0,s.width,s.height)),this.render_connections_shadows?(t.shadowColor="#000",t.shadowOffsetX=0,t.shadowOffsetY=0,t.shadowBlur=6):t.shadowColor="rgba(0,0,0,0)",this.drawConnections(t),t.shadowColor="rgba(0,0,0,0)",t.restore()}(r=t.finish)==null||r.call(t),this.dirty_bgcanvas=!1,this.dirty_canvas=!0}drawNode(s,t){var m,w,b,k,L,T,A,E;this.current_node=s;const i=s.renderingColor,n=s.renderingBgColor,o=this.low_quality,r=this.editor_alpha;if(t.globalAlpha=r,this.render_shadows&&!o?(t.shadowColor=LiteGraph.DEFAULT_SHADOW_COLOR,t.shadowOffsetX=2*this.ds.scale,t.shadowOffsetY=2*this.ds.scale,t.shadowBlur=3*this.ds.scale):t.shadowColor="transparent",s.flags.collapsed&&((m=s.onDrawCollapsed)==null?void 0:m.call(s,t,this))==!0)return;const a=s._shape||RenderShape.BOX,l=O(_LGraphCanvas,ze);O(_LGraphCanvas,ze).set(s.size);const u=s.horizontal;if(s.flags.collapsed){t.font=this.inner_text_font;const C=s.getTitle?s.getTitle():s.title;C!=null&&(s._collapsed_width=Math.min(s.size[0],t.measureText(C).width+LiteGraph.NODE_TITLE_HEIGHT*2),l[0]=s._collapsed_width,l[1]=0)}s.clip_area&&(t.save(),t.beginPath(),a==RenderShape.BOX?t.rect(0,0,l[0],l[1]):a==RenderShape.ROUND?t.roundRect(0,0,l[0],l[1],[10]):a==RenderShape.CIRCLE&&t.arc(l[0]*.5,l[1]*.5,l[0]*.5,0,Math.PI*2),t.clip()),this.drawNodeShape(s,t,l,i,n,s.selected),o||s.drawBadges(t),t.shadowColor="transparent",t.strokeStyle=LiteGraph.NODE_BOX_OUTLINE_COLOR,(w=s.onDrawForeground)==null||w.call(s,t,this,this.canvas),t.font=this.inner_text_font;const p=!o,d=LiteGraph.NODE_TEXT_HIGHLIGHT_COLOR??LiteGraph.NODE_SELECTED_TITLE_COLOR??LiteGraph.NODE_TEXT_COLOR,c=(k=(b=this.connecting_links)==null?void 0:b[0])==null?void 0:k.output,_=(T=(L=this.connecting_links)==null?void 0:L[0])==null?void 0:T.input;let g=0;const f=new Float32Array(2);if(s.collapsed){if(this.render_collapsed_slots){let C=null,N=null,S;if(s.inputs){for(let D=0;D<s.inputs.length;D++)if(S=s.inputs[D],S.link!=null){C=S;break}}if(s.outputs)for(let D=0;D<s.outputs.length;D++)S=s.outputs[D],!(!S.links||!S.links.length)&&(N=S);if(C){let D=0,P=LiteGraph.NODE_TITLE_HEIGHT*-.5;u&&(D=s._collapsed_width*.5,P=-LiteGraph.NODE_TITLE_HEIGHT),t.fillStyle="#686",t.beginPath(),S.type===LiteGraph.EVENT||S.shape===RenderShape.BOX?t.rect(D-7+.5,P-4,14,8):S.shape===RenderShape.ARROW?(t.moveTo(D+8,P),t.lineTo(D+-4,P-4),t.lineTo(D+-4,P+4),t.closePath()):t.arc(D,P,4,0,Math.PI*2),t.fill()}if(N){let D=s._collapsed_width,P=LiteGraph.NODE_TITLE_HEIGHT*-.5;u&&(D=s._collapsed_width*.5,P=0),t.fillStyle="#686",t.strokeStyle="black",t.beginPath(),S.type===LiteGraph.EVENT||S.shape===RenderShape.BOX?t.rect(D-7+.5,P-4,14,8):S.shape===RenderShape.ARROW?(t.moveTo(D+6,P),t.lineTo(D-6,P-4),t.lineTo(D-6,P+4),t.closePath()):t.arc(D,P,4,0,Math.PI*2),t.fill()}}}else{if(s.inputs)for(let C=0;C<s.inputs.length;C++){const N=toClass(NodeInputSlot,s.inputs[C]),S=!this.connecting_links||c&&LiteGraph.isValidConnection(N.type,c.type),D=S&&((A=s.mouseOver)==null?void 0:A.inputId)===C,P=D?d:LiteGraph.NODE_TEXT_COLOR;t.globalAlpha=S?r:.4*r;const F=s.getConnectionPos(!0,C,f);F[0]-=s.pos[0],F[1]-=s.pos[1],g<F[1]+LiteGraph.NODE_SLOT_HEIGHT*.5&&(g=F[1]+LiteGraph.NODE_SLOT_HEIGHT*.5),N.draw(t,{pos:F,colorContext:this,labelColor:P,labelPosition:LabelPosition.Right,horizontal:u,lowQuality:o,renderText:p,highlight:D})}if(s.outputs)for(let C=0;C<s.outputs.length;C++){const N=toClass(NodeOutputSlot,s.outputs[C]),S=N.type,D=!this.connecting_links||_&&LiteGraph.isValidConnection(S,_.type),P=D&&((E=s.mouseOver)==null?void 0:E.outputId)===C,F=P?d:LiteGraph.NODE_TEXT_COLOR;t.globalAlpha=D?r:.4*r;const U=s.getConnectionPos(!1,C,f);U[0]-=s.pos[0],U[1]-=s.pos[1],g<U[1]+LiteGraph.NODE_SLOT_HEIGHT*.5&&(g=U[1]+LiteGraph.NODE_SLOT_HEIGHT*.5),N.draw(t,{pos:U,colorContext:this,labelColor:F,labelPosition:LabelPosition.Left,horizontal:u,lowQuality:o,renderText:p,highlight:P})}if(t.textAlign="left",t.globalAlpha=1,s.widgets){let C=g;(u||s.widgets_up)&&(C=2),s.widgets_start_y!=null&&(C=s.widgets_start_y),this.drawNodeWidgets(s,C,t,this.node_widget&&this.node_widget[0]==s?this.node_widget[1]:null)}}s.clip_area&&t.restore(),t.globalAlpha=1}drawLinkTooltip(s,t){var u;const i=t._pos;if(s.fillStyle="black",s.beginPath(),this.linkMarkerShape===LinkMarkerShape.Arrow){const p=s.getTransform();s.translate(i[0],i[1]),Number.isFinite(t._centreAngle)&&s.rotate(t._centreAngle),s.moveTo(-2,-3),s.lineTo(4,0),s.lineTo(-2,3),s.setTransform(p)}else(this.linkMarkerShape==null||this.linkMarkerShape===LinkMarkerShape.Circle)&&s.arc(i[0],i[1],3,0,Math.PI*2);s.fill();const n=t.data;if(n==null||((u=this.onDrawLinkTooltip)==null?void 0:u.call(this,s,t,this))==!0)return;let o=null;if(typeof n=="number"?o=n.toFixed(2):typeof n=="string"?o='"'+n+'"':typeof n=="boolean"?o=String(n):n.toToolTip?o=n.toToolTip():o="["+n.constructor.name+"]",o==null)return;o=o.substring(0,30),s.font="14px Courier New";const a=s.measureText(o).width+20,l=24;s.shadowColor="black",s.shadowOffsetX=2,s.shadowOffsetY=2,s.shadowBlur=3,s.fillStyle="#454",s.beginPath(),s.roundRect(i[0]-a*.5,i[1]-15-l,a,l,[3]),s.moveTo(i[0]-10,i[1]-15),s.lineTo(i[0]+10,i[1]-15),s.lineTo(i[0],i[1]-5),s.fill(),s.shadowColor="transparent",s.textAlign="center",s.fillStyle="#CEC",s.fillText(o,i[0],i[1]-15-l*.3)}drawNodeShape(s,t,i,n,o,r){var f,m,w,b;t.strokeStyle=n,t.fillStyle=LiteGraph.use_legacy_node_error_indicator?"#F00":o;const a=LiteGraph.NODE_TITLE_HEIGHT,l=this.low_quality,{collapsed:u}=s.flags,p=s.renderingShape,d=s.title_mode,c=!(d==TitleMode.TRANSPARENT_TITLE||d==TitleMode.NO_TITLE),_=O(_LGraphCanvas,je);s.measure(_),_[0]-=s.pos[0],_[1]-=s.pos[1];const g=t.globalAlpha;if(t.beginPath(),p==RenderShape.BOX||l?t.fillRect(_[0],_[1],_[2],_[3]):p==RenderShape.ROUND||p==RenderShape.CARD?t.roundRect(_[0],_[1],_[2],_[3],p==RenderShape.CARD?[LiteGraph.ROUND_RADIUS,LiteGraph.ROUND_RADIUS,0,0]:[LiteGraph.ROUND_RADIUS]):p==RenderShape.CIRCLE&&t.arc(i[0]*.5,i[1]*.5,i[0]*.5,0,Math.PI*2),t.fill(),s.has_errors&&!LiteGraph.use_legacy_node_error_indicator&&strokeShape(t,_,{shape:p,title_mode:d,title_height:a,padding:12,colour:LiteGraph.NODE_ERROR_COLOUR,collapsed:u,thickness:10}),!u&&c&&(t.shadowColor="transparent",t.fillStyle="rgba(0,0,0,0.2)",t.fillRect(0,-1,_[2],2)),t.shadowColor="transparent",(f=s.onDrawBackground)==null||f.call(s,t),(c||d==TitleMode.TRANSPARENT_TITLE)&&(s.drawTitleBarBackground(t,{scale:this.ds.scale,low_quality:l}),s.drawTitleBox(t,{scale:this.ds.scale,low_quality:l,box_size:10}),t.globalAlpha=g,s.drawTitleText(t,{scale:this.ds.scale,default_title_color:this.node_title_color,low_quality:l}),(m=s.onDrawTitle)==null||m.call(s,t)),r){(w=s.onBounding)==null||w.call(s,_);const k=s.has_errors&&!LiteGraph.use_legacy_node_error_indicator?20:void 0;strokeShape(t,_,{shape:p,title_height:a,title_mode:d,padding:k,collapsed:(b=s.flags)==null?void 0:b.collapsed})}s.execute_triggered>0&&s.execute_triggered--,s.action_triggered>0&&s.action_triggered--}drawSnapGuide(s,t,i=RenderShape.ROUND){const n=O(_LGraphCanvas,Ye);n.set(t.boundingRect);const{pos:o}=t,r=o[0]-n[0],a=o[1]-n[1];n[0]+=r,n[1]+=a,snapPoint(n,O(this,ee)),n[0]-=r,n[1]-=a;const{globalAlpha:l}=s;s.globalAlpha=1,s.beginPath();const[u,p,d,c]=n;if(i===RenderShape.CIRCLE){const _=u+d*.5,g=p+c*.5,f=Math.min(d*.5,c*.5);s.arc(_,g,f,0,Math.PI*2)}else s.rect(u,p,d,c);s.lineWidth=.5,s.strokeStyle="#FFFFFF66",s.fillStyle="#FFFFFF22",s.fill(),s.stroke(),s.globalAlpha=l}drawConnections(s){var a;const t=this.renderedPaths;if(t.clear(),this.links_render_mode===LinkRenderType.HIDDEN_LINK)return;const i=[],n=LiteGraph.getTime(),o=this.visible_area;O(_LGraphCanvas,re)[0]=o[0]-20,O(_LGraphCanvas,re)[1]=o[1]-20,O(_LGraphCanvas,re)[2]=o[2]+40,O(_LGraphCanvas,re)[3]=o[3]+40,s.lineWidth=this.connections_width,s.fillStyle="#AAA",s.strokeStyle="#AAA",s.globalAlpha=this.editor_alpha;const r=this.graph._nodes;for(let l=0,u=r.length;l<u;++l){const p=r[l];if(!(!p.inputs||!p.inputs.length))for(let d=0;d<p.inputs.length;++d){const c=p.inputs[d];if(!c||c.link==null)continue;const _=c.link,g=this.graph._links.get(_);if(!g)continue;const f=this.graph.getNodeById(g.origin_id);if(f==null)continue;const m=g.origin_slot,w=m==-1?[f.pos[0]+10,f.pos[1]+10]:f.getConnectionPos(!1,m,O(_LGraphCanvas,Ke)),b=p.getConnectionPos(!0,d,O(_LGraphCanvas,qe)),k=this.reroutesEnabled?LLink.getReroutes(this.graph,g):[],L=[w,...k.map(D=>D.pos),b],T=L.map(D=>D[0]),A=L.map(D=>D[1]);if(O(_LGraphCanvas,Q)[0]=Math.min(...T),O(_LGraphCanvas,Q)[1]=Math.min(...A),O(_LGraphCanvas,Q)[2]=Math.max(...T)-O(_LGraphCanvas,Q)[0],O(_LGraphCanvas,Q)[3]=Math.max(...A)-O(_LGraphCanvas,Q)[1],!overlapBounding(O(_LGraphCanvas,Q),O(_LGraphCanvas,re)))continue;const E=f.outputs[m],C=p.inputs[d];if(!E||!C)continue;const N=E.dir||(f.horizontal?LinkDirection.DOWN:LinkDirection.RIGHT),S=C.dir||(p.horizontal?LinkDirection.UP:LinkDirection.LEFT);if(k.length){let D;const P=k.length;for(let F=0;F<P;F++){const U=k[F];if(!t.has(U)){t.add(U),i.push(U),U._colour=g.color||_LGraphCanvas.link_type_colors[g.type]||this.default_link_color;const K=this.graph.reroutes.get(U.parentId),$=(K==null?void 0:K.pos)??w;U.calculateAngle(this.last_draw_time,this.graph,$),this.renderLink(s,$,U.pos,g,!1,0,null,N,S,{startControl:D,endControl:U.controlPoint,reroute:U})}const H=((a=k[F+1])==null?void 0:a.pos)??b,X=Math.min(80,distance(U.pos,H)*.25);D=[X*U.cos,X*U.sin]}this.renderLink(s,L.at(-2),L.at(-1),g,!1,0,null,N,S,{startControl:D})}else this.renderLink(s,w,b,g,!1,0,null,N,S);if(t.add(g),g&&g._last_time&&n-g._last_time<1e3){const D=2-(n-g._last_time)*.002,P=s.globalAlpha;s.globalAlpha=P*D,this.renderLink(s,w,b,g,!0,D,"white",N,S),s.globalAlpha=P}}}for(const l of i)O(this,ee)&&this.isDragging&&this.selectedItems.has(l)&&this.drawSnapGuide(s,l,RenderShape.CIRCLE),l.draw(s);s.globalAlpha=1}renderLink(s,t,i,n,o,r,a,l,u,{startControl:p,endControl:d,reroute:c,num_sublines:_=1}={}){n&&this.visible_links.push(n);const g=n!=null&&this.highlighted_links[n.id]?"#FFF":a||(n==null?void 0:n.color)||_LGraphCanvas.link_type_colors[n.type]||this.default_link_color,f=l||LinkDirection.RIGHT,m=u||LinkDirection.LEFT,w=this.links_render_mode==LinkRenderType.SPLINE_LINK&&(!d||!p)?distance(t,i):null;this.render_connections_border&&!this.low_quality&&(s.lineWidth=this.connections_width+4),s.lineJoin="round",_||(_=1),_>1&&(s.lineWidth=.5);const b=new Path2D,k=c??n;k&&(k.path=b);const L=O(_LGraphCanvas,$e),T=O(_LGraphCanvas,Ze),A=(k==null?void 0:k._pos)??[0,0];for(let E=0;E<_;E+=1){const C=(E-(_-1)*.5)*5;if(L[0]=t[0],L[1]=t[1],T[0]=i[0],T[1]=i[1],this.links_render_mode==LinkRenderType.SPLINE_LINK){if(d?(T[0]=i[0]+d[0],T[1]=i[1]+d[1]):M(this,_e,Ne).call(this,T,m,w),p?(L[0]=t[0]+p[0],L[1]=t[1]+p[1]):M(this,_e,Ne).call(this,L,f,w),b.moveTo(t[0],t[1]+C),b.bezierCurveTo(L[0],L[1]+C,T[0],T[1]+C,i[0],i[1]+C),findPointOnCurve(A,t,i,L,T,.5),k&&this.linkMarkerShape===LinkMarkerShape.Arrow){const N=O(_LGraphCanvas,Je);findPointOnCurve(N,t,i,L,T,.51),k._centreAngle=Math.atan2(N[1]-A[1],N[0]-A[0])}}else if(this.links_render_mode==LinkRenderType.LINEAR_LINK){switch(f){case LinkDirection.LEFT:L[0]+=-15;break;case LinkDirection.RIGHT:L[0]+=15;break;case LinkDirection.UP:L[1]+=-15;break;case LinkDirection.DOWN:L[1]+=15;break}switch(m){case LinkDirection.LEFT:T[0]+=-15;break;case LinkDirection.RIGHT:T[0]+=15;break;case LinkDirection.UP:T[1]+=-15;break;case LinkDirection.DOWN:T[1]+=15;break}b.moveTo(t[0],t[1]+C),b.lineTo(L[0],L[1]+C),b.lineTo(T[0],T[1]+C),b.lineTo(i[0],i[1]+C),A[0]=(L[0]+T[0])*.5,A[1]=(L[1]+T[1])*.5,k&&this.linkMarkerShape===LinkMarkerShape.Arrow&&(k._centreAngle=Math.atan2(T[1]-L[1],T[0]-L[0]))}else if(this.links_render_mode==LinkRenderType.STRAIGHT_LINK){f==LinkDirection.RIGHT?L[0]+=10:L[1]+=10,m==LinkDirection.LEFT?T[0]-=10:T[1]-=10;const N=(L[0]+T[0])*.5;if(b.moveTo(t[0],t[1]),b.lineTo(L[0],L[1]),b.lineTo(N,L[1]),b.lineTo(N,T[1]),b.lineTo(T[0],T[1]),b.lineTo(i[0],i[1]),A[0]=N,A[1]=(L[1]+T[1])*.5,k&&this.linkMarkerShape===LinkMarkerShape.Arrow){const S=T[1]-L[1];Math.abs(S)<4?k._centreAngle=0:S>0?k._centreAngle=Math.PI*.5:k._centreAngle=-(Math.PI*.5)}}else return}if(this.render_connections_border&&!this.low_quality&&!o&&(s.strokeStyle="rgba(0,0,0,0.5)",s.stroke(b)),s.lineWidth=this.connections_width,s.fillStyle=s.strokeStyle=g,s.stroke(b),this.ds.scale>=.6&&this.highquality_render&&k&&m!=LinkDirection.CENTER){if(this.render_connection_arrows){const E=this.computeConnectionPoint(t,i,.25,f,m),C=this.computeConnectionPoint(t,i,.26,f,m),N=this.computeConnectionPoint(t,i,.75,f,m),S=this.computeConnectionPoint(t,i,.76,f,m);let D=0,P=0;this.render_curved_connections?(D=-Math.atan2(C[0]-E[0],C[1]-E[1]),P=-Math.atan2(S[0]-N[0],S[1]-N[1])):P=D=i[1]>t[1]?0:Math.PI;const F=s.getTransform();s.translate(E[0],E[1]),s.rotate(D),s.beginPath(),s.moveTo(-5,-3),s.lineTo(0,7),s.lineTo(5,-3),s.fill(),s.setTransform(F),s.translate(N[0],N[1]),s.rotate(P),s.beginPath(),s.moveTo(-5,-3),s.lineTo(0,7),s.lineTo(5,-3),s.fill(),s.setTransform(F)}if(s.beginPath(),this.linkMarkerShape===LinkMarkerShape.Arrow){const E=s.getTransform();s.translate(A[0],A[1]),s.rotate(k._centreAngle),s.moveTo(-3.2,-5),s.lineTo(7,0),s.lineTo(-3.2,5),s.fill(),s.setTransform(E)}else(this.linkMarkerShape==null||this.linkMarkerShape===LinkMarkerShape.Circle)&&s.arc(A[0],A[1],5,0,Math.PI*2);s.fill()}if(r){s.fillStyle=g;for(let E=0;E<5;++E){const C=(LiteGraph.getTime()*.001+E*.2)%1,N=this.computeConnectionPoint(t,i,C,f,m);s.beginPath(),s.arc(N[0],N[1],5,0,2*Math.PI),s.fill()}}}computeConnectionPoint(s,t,i,n,o){n||(n=LinkDirection.RIGHT),o||(o=LinkDirection.LEFT);const r=distance(s,t),a=[s[0],s[1]],l=[t[0],t[1]];M(this,_e,Ne).call(this,a,n,r),M(this,_e,Ne).call(this,l,o,r);const u=(1-i)*(1-i)*(1-i),p=3*((1-i)*(1-i))*i,d=3*(1-i)*(i*i),c=i*i*i,_=u*s[0]+p*a[0]+d*l[0]+c*t[0],g=u*s[1]+p*a[1]+d*l[1]+c*t[1];return[_,g]}drawExecutionOrder(s){s.shadowColor="transparent",s.globalAlpha=.25,s.textAlign="center",s.strokeStyle="white",s.globalAlpha=.75;const t=this.visible_nodes;for(let i=0;i<t.length;++i){const n=t[i];s.fillStyle="black",s.fillRect(n.pos[0]-LiteGraph.NODE_TITLE_HEIGHT,n.pos[1]-LiteGraph.NODE_TITLE_HEIGHT,LiteGraph.NODE_TITLE_HEIGHT,LiteGraph.NODE_TITLE_HEIGHT),n.order==0&&s.strokeRect(n.pos[0]-LiteGraph.NODE_TITLE_HEIGHT+.5,n.pos[1]-LiteGraph.NODE_TITLE_HEIGHT+.5,LiteGraph.NODE_TITLE_HEIGHT,LiteGraph.NODE_TITLE_HEIGHT),s.fillStyle="#FFF",s.fillText(stringOrEmpty(n.order),n.pos[0]+LiteGraph.NODE_TITLE_HEIGHT*-.5,n.pos[1]-6)}s.globalAlpha=1}drawNodeWidgets(s,t,i,n){var _;if(!s.widgets||!s.widgets.length)return 0;const o=s.size[0],r=s.widgets;t+=2;const a=LiteGraph.NODE_WIDGET_HEIGHT,l=!this.low_quality;i.save(),i.globalAlpha=this.editor_alpha;const u=LiteGraph.WIDGET_BGCOLOR,p=LiteGraph.WIDGET_TEXT_COLOR,d=LiteGraph.WIDGET_SECONDARY_TEXT_COLOR,c=15;for(let g=0;g<r.length;++g){const f=r[g];if(f.hidden||f.advanced&&!s.showAdvanced)continue;const m=f.y||t,w=f.advanced?LiteGraph.WIDGET_ADVANCED_OUTLINE_COLOR:LiteGraph.WIDGET_OUTLINE_COLOR;f===this.link_over_widget&&new NodeInputSlot({name:"",type:this.link_over_widget_type,link:0}).draw(i,{pos:[10,m+10],colorContext:this}),f.last_y=m,i.strokeStyle=w,i.fillStyle="#222",i.textAlign="left",f.disabled&&(i.globalAlpha*=.5);const b=f.width||o;switch(f.type){case"button":i.fillStyle=u,f.clicked&&(i.fillStyle="#AAA",f.clicked=!1,this.dirty_canvas=!0),i.fillRect(c,m,b-c*2,a),l&&!f.disabled&&i.strokeRect(c,m,b-c*2,a),l&&(i.textAlign="center",i.fillStyle=p,i.fillText(f.label||f.name,b*.5,m+a*.7));break;case"toggle":toClass(BooleanWidget,f).drawWidget(i,{y:m,width:b,show_text:l,margin:c});break;case"slider":{i.fillStyle=u,i.fillRect(c,m,b-c*2,a);const k=f.options.max-f.options.min;let L=(f.value-f.options.min)/k;if(L<0&&(L=0),L>1&&(L=1),i.fillStyle=f.options.hasOwnProperty("slider_color")?f.options.slider_color:n==f?"#89A":"#678",i.fillRect(c,m,L*(b-c*2),a),l&&!f.disabled&&i.strokeRect(c,m,b-c*2,a),f.marker){let T=(f.marker-f.options.min)/k;T<0&&(T=0),T>1&&(T=1),i.fillStyle=f.options.hasOwnProperty("marker_color")?f.options.marker_color:"#AA9",i.fillRect(c+T*(b-c*2),m,2,a)}l&&(i.textAlign="center",i.fillStyle=p,i.fillText((f.label||f.name)+"  "+Number(f.value).toFixed(f.options.precision!=null?f.options.precision:3),b*.5,m+a*.7));break}case"number":case"combo":if(i.textAlign="left",i.strokeStyle=w,i.fillStyle=u,i.beginPath(),l?i.roundRect(c,m,b-c*2,a,[a*.5]):i.rect(c,m,b-c*2,a),i.fill(),l)if(f.disabled||i.stroke(),i.fillStyle=p,f.disabled||(i.beginPath(),i.moveTo(c+16,m+5),i.lineTo(c+6,m+a*.5),i.lineTo(c+16,m+a-5),i.fill(),i.beginPath(),i.moveTo(b-c-16,m+5),i.lineTo(b-c-6,m+a*.5),i.lineTo(b-c-16,m+a-5),i.fill()),i.fillStyle=d,i.fillText(f.label||f.name,c*2+5,m+a*.7),i.fillStyle=p,i.textAlign="right",f.type=="number")i.fillText(Number(f.value).toFixed(f.options.precision!==void 0?f.options.precision:3),b-c*2-20,m+a*.7);else{let k=typeof f.value=="number"?String(f.value):f.value;if(f.options.values){let C=f.options.values;typeof C=="function"&&(C=C()),C&&!Array.isArray(C)&&(k=C[f.value])}const L=i.measureText(f.label||f.name).width+c*2,A=b-c*4-L,E=i.measureText(k).width;if(E>A){const C="…",N=i.measureText(C).width,S=i.measureText("a").width;if(A<=N)k="․";else{if(k=`${k}`,E+N-A+S*3>A){const P=A+S*3,F=Math.floor((P-N)/S);k=k.substr(0,F)}for(;i.measureText(k).width+N>A;)k=k.substr(0,k.length-1);k+=C}}i.fillText(k,b-c*2-20,m+a*.7)}break;case"string":case"text":if(i.textAlign="left",i.strokeStyle=w,i.fillStyle=u,i.beginPath(),l?i.roundRect(c,m,b-c*2,a,[a*.5]):i.rect(c,m,b-c*2,a),i.fill(),l){f.disabled||i.stroke(),i.save(),i.beginPath(),i.rect(c,m,b-c*2,a),i.clip(),i.fillStyle=d;const k=f.label||f.name;k!=null&&i.fillText(k,c*2,m+a*.7),i.fillStyle=p,i.textAlign="right",i.fillText(String(f.value).substr(0,30),b-c*2,m+a*.7),i.restore()}break;default:(_=f.draw)==null||_.call(f,i,s,b,m,a);break}t+=(f.computeSize?f.computeSize(b)[1]:a)+4,i.globalAlpha=this.editor_alpha}i.restore(),i.textAlign="left"}drawGroups(s,t){if(!this.graph)return;const i=this.graph._groups;t.save(),t.globalAlpha=.5*this.editor_alpha;const n=O(this,ee)&&this.isDragging;for(let o=0;o<i.length;++o){const r=i[o];overlapBounding(this.visible_area,r._bounding)&&(n&&this.selectedItems.has(r)&&this.drawSnapGuide(t,r),r.draw(this,t))}t.restore()}adjustNodesSize(){const s=this.graph._nodes;for(let t=0;t<s.length;++t)s[t].size=s[t].computeSize();this.setDirty(!0,!0)}resize(s,t){if(!s&&!t){const i=this.canvas.parentElement;s=i.offsetWidth,t=i.offsetHeight}this.canvas.width==s&&this.canvas.height==t||(this.canvas.width=s,this.canvas.height=t,this.bgcanvas.width=this.canvas.width,this.bgcanvas.height=this.canvas.height,this.setDirty(!0,!0))}onNodeSelectionChange(){}boundaryNodesForSelection(){return _LGraphCanvas.getBoundaryNodes(this.selected_nodes)}showLinkMenu(s,t){var p,d;const{graph:i}=this,n=i.getNodeById(s.origin_id),o=((d=(p=n==null?void 0:n.outputs)==null?void 0:p[s.origin_slot])==null?void 0:d.type)??"*",r=["Add Node",null,"Delete",null];this.reroutesEnabled&&r.splice(1,0,"Add Reroute");const a="data"in s&&s.data!=null?s.data.constructor.name:null,l=new LiteGraph.ContextMenu(r,{event:t,title:a,callback:u.bind(this)});function u(c,_,g){switch(c){case"Add Node":_LGraphCanvas.onMenuAdd(null,null,g,l,function(f){var w,b;if(!((w=f.inputs)!=null&&w.length)||!((b=f.outputs)!=null&&b.length))return;const m=this.reroutesEnabled?{afterRerouteId:s.parentId}:void 0;n.connectByType(s.origin_slot,f,o,m)&&(f.pos[0]-=f.size[0]*.5)});break;case"Add Reroute":{this.adjustMouseEvent(g),i.createReroute([g.canvasX,g.canvasY],s),this.setDirty(!1,!0);break}case"Delete":i.removeLink(s.id);break}}return!1}createDefaultNodeForSlot(s){const t=Object.assign({nodeFrom:null,slotFrom:null,nodeTo:null,slotTo:null,position:[0,0],nodeType:null,posAdd:[0,0],posSizeFix:[0,0]},s||{}),{afterRerouteId:i}=t,n=t.nodeFrom&&t.slotFrom!==null,o=!n&&t.nodeTo&&t.slotTo!==null;if(!n&&!o)return console.warn("No data passed to createDefaultNodeForSlot "+t.nodeFrom+" "+t.slotFrom+" "+t.nodeTo+" "+t.slotTo),!1;if(!t.nodeType)return console.warn("No type to createDefaultNodeForSlot"),!1;const r=n?t.nodeFrom:t.nodeTo;let a=n?t.slotFrom:t.slotTo,l=!1;switch(typeof a){case"string":l=n?r.findOutputSlot(a,!1):r.findInputSlot(a,!1),a=n?r.outputs[a]:r.inputs[a];break;case"object":l=n?r.findOutputSlot(a.name):r.findInputSlot(a.name);break;case"number":l=a,a=n?r.outputs[a]:r.inputs[a];break;case"undefined":default:return console.warn("Cant get slot information "+a),!1}const u=a.type==LiteGraph.EVENT?"_event_":a.type,p=n?LiteGraph.slot_types_default_out:LiteGraph.slot_types_default_in;if(p!=null&&p[u]){let d=!1;if(typeof p[u]=="object"){for(const c in p[u])if(t.nodeType==p[u][c]||t.nodeType=="AUTO"){d=p[u][c];break}}else(t.nodeType==p[u]||t.nodeType=="AUTO")&&(d=p[u]);if(d){let c=!1;typeof d=="object"&&d.node&&(c=d,d=d.node);const _=LiteGraph.createNode(d);if(_){if(c){if(c.properties)for(const g in c.properties)_.addProperty(g,c.properties[g]);if(c.inputs){_.inputs=[];for(const g in c.inputs)_.addOutput(c.inputs[g][0],c.inputs[g][1])}if(c.outputs){_.outputs=[];for(const g in c.outputs)_.addOutput(c.outputs[g][0],c.outputs[g][1])}c.title&&(_.title=c.title),c.json&&_.configure(c.json)}return this.graph.add(_),_.pos=[t.position[0]+t.posAdd[0]+(t.posSizeFix[0]?t.posSizeFix[0]*_.size[0]:0),t.position[1]+t.posAdd[1]+(t.posSizeFix[1]?t.posSizeFix[1]*_.size[1]:0)],n?t.nodeFrom.connectByType(l,_,u,{afterRerouteId:i}):t.nodeTo.connectByTypeOutput(l,_,u,{afterRerouteId:i}),!0}console.log("failed creating "+d)}}return!1}showConnectionMenu(s){const t=Object.assign({nodeFrom:null,slotFrom:null,nodeTo:null,slotTo:null,e:null,allow_searchbox:this.allow_searchbox,showSearchBox:this.showSearchBox},s||{}),i=this,{afterRerouteId:n}=t,o=t.nodeFrom&&t.slotFrom,r=!o&&t.nodeTo&&t.slotTo;if(!o&&!r){console.warn("No data passed to showConnectionMenu");return}const a=o?t.nodeFrom:t.nodeTo;let l=o?t.slotFrom:t.slotTo,u;switch(typeof l){case"string":u=o?a.findOutputSlot(l,!1):a.findInputSlot(l,!1),l=o?a.outputs[l]:a.inputs[l];break;case"object":u=o?a.findOutputSlot(l.name):a.findInputSlot(l.name);break;case"number":u=l,l=o?a.outputs[l]:a.inputs[l];break;default:console.warn("Cant get slot information "+l);return}const p=["Add Node",null];t.allow_searchbox&&(p.push("Search"),p.push(null));const d=l.type==LiteGraph.EVENT?"_event_":l.type,c=o?LiteGraph.slot_types_default_out:LiteGraph.slot_types_default_in;if(c!=null&&c[d])if(typeof c[d]=="object")for(const f in c[d])p.push(c[d][f]);else p.push(c[d]);const _=new LiteGraph.ContextMenu(p,{event:t.e,title:(l&&l.name!=""?l.name+(d?" | ":""):"")+(l&&d?d:""),callback:g});function g(f,m,w){switch(f){case"Add Node":_LGraphCanvas.onMenuAdd(null,null,w,_,function(b){o?t.nodeFrom.connectByType(u,b,d,{afterRerouteId:n}):t.nodeTo.connectByTypeOutput(u,b,d,{afterRerouteId:n})});break;case"Search":o?t.showSearchBox(w,{node_from:t.nodeFrom,slot_from:l,type_filter_in:d}):t.showSearchBox(w,{node_to:t.nodeTo,slot_from:l,type_filter_out:d});break;default:{i.createDefaultNodeForSlot(Object.assign(t,{position:[t.e.canvasX,t.e.canvasY],nodeType:f,afterRerouteId:n}));break}}}}prompt(s,t,i,n,o){var L;const r=this;s=s||"";const a=document.createElement("div");a.is_modified=!1,a.className="graphdialog rounded",a.innerHTML=o?"<span class='name'></span> <textarea autofocus class='value'></textarea><button class='rounded'>OK</button>":"<span class='name'></span> <input autofocus type='text' class='value'/><button class='rounded'>OK</button>",a.close=function(){r.prompt_box=null,a.parentNode&&a.parentNode.removeChild(a)};const u=_LGraphCanvas.active_canvas.canvas;u.parentNode.appendChild(a),this.ds.scale>1&&(a.style.transform="scale("+this.ds.scale+")");let p=null,d=0;LiteGraph.pointerListenerAdd(a,"leave",function(){d||LiteGraph.dialog_close_on_mouse_leave&&!a.is_modified&&LiteGraph.dialog_close_on_mouse_leave&&(p=setTimeout(a.close,LiteGraph.dialog_close_on_mouse_leave_delay))}),LiteGraph.pointerListenerAdd(a,"enter",function(){LiteGraph.dialog_close_on_mouse_leave&&p&&clearTimeout(p)});const c=a.querySelectorAll("select");if(c)for(const T of c)T.addEventListener("click",function(){d++}),T.addEventListener("blur",function(){d=0}),T.addEventListener("change",function(){d=-1});(L=this.prompt_box)==null||L.close(),this.prompt_box=a;const _=a.querySelector(".name");_.innerText=s;const g=a.querySelector(".value");g.value=t,g.select();const f=g;f.addEventListener("keydown",function(T){if(a.is_modified=!0,T.keyCode==27)a.close();else if(T.keyCode==13&&T.target.localName!="textarea")i&&i(this.value),a.close();else return;T.preventDefault(),T.stopPropagation()}),a.querySelector("button").addEventListener("click",function(){i==null||i(f.value),r.setDirty(!0),a.close()});const w=u.getBoundingClientRect();let b=-20,k=-20;return w&&(b-=w.left,k-=w.top),n?(a.style.left=n.clientX+b+"px",a.style.top=n.clientY+k+"px"):(a.style.left=u.width*.5+b+"px",a.style.top=u.height*.5+k+"px"),setTimeout(function(){f.focus();const T=Date.now();function A(E){E.target===u&&Date.now()-T>256&&(a.close(),u.parentNode.removeEventListener("click",A),u.parentNode.removeEventListener("touchend",A))}u.parentNode.addEventListener("click",A),u.parentNode.addEventListener("touchend",A)},10),a}showSearchBox(s,t){var A;const i={slot_from:null,node_from:null,node_to:null,do_type_filter:LiteGraph.search_filter_enabled,type_filter_in:!1,type_filter_out:!1,show_general_if_none_on_typefilter:!0,show_general_after_typefiltered:!0,hide_on_mouse_leave:LiteGraph.search_hide_on_mouse_leave,show_all_if_empty:!0,show_all_on_open:LiteGraph.search_show_all_on_open};t=Object.assign(i,t||{});const n=this,o=_LGraphCanvas.active_canvas,r=o.canvas,a=r.ownerDocument||document,l=document.createElement("div");l.className="litegraph litesearchbox graphdialog rounded",l.innerHTML="<span class='name'>Search</span> <input autofocus type='text' class='value rounded'/>",t.do_type_filter&&(l.innerHTML+="<select class='slot_in_type_filter'><option value=''></option></select>",l.innerHTML+="<select class='slot_out_type_filter'><option value=''></option></select>"),l.innerHTML+="<div class='helper'></div>",a.fullscreenElement?a.fullscreenElement.appendChild(l):(a.body.appendChild(l),a.body.style.overflow="hidden");let u,p;if(t.do_type_filter&&(u=l.querySelector(".slot_in_type_filter"),p=l.querySelector(".slot_out_type_filter")),l.close=function(){var E;n.search_box=null,this.blur(),r.focus(),a.body.style.overflow="",setTimeout(function(){n.canvas.focus()},20),(E=l.parentNode)==null||E.removeChild(l)},this.ds.scale>1&&(l.style.transform="scale("+this.ds.scale+")"),t.hide_on_mouse_leave){let E=!1,C=null;LiteGraph.pointerListenerAdd(l,"enter",function(){C&&(clearTimeout(C),C=null)}),LiteGraph.pointerListenerAdd(l,"leave",function(){E||(C=setTimeout(function(){l.close()},typeof t.hide_on_mouse_leave=="number"?t.hide_on_mouse_leave:500))}),t.do_type_filter&&(u.addEventListener("click",function(){E++}),u.addEventListener("blur",function(){E=0}),u.addEventListener("change",function(){E=-1}),p.addEventListener("click",function(){E++}),p.addEventListener("blur",function(){E=0}),p.addEventListener("change",function(){E=-1}))}(A=n.search_box)==null||A.close(),n.search_box=l;const d=l.querySelector(".helper");let c=null,_=null,g=null;const f=l.querySelector("input");if(f&&(f.addEventListener("blur",function(){this.focus()}),f.addEventListener("keydown",function(E){if(E.keyCode==38)L(!1);else if(E.keyCode==40)L(!0);else if(E.keyCode==27)l.close();else if(E.keyCode==13)g?k(unescape(g.dataset.type)):c?k(c):l.close();else{_&&clearInterval(_),_=setTimeout(T,10);return}return E.preventDefault(),E.stopPropagation(),E.stopImmediatePropagation(),!0})),t.do_type_filter){if(u){const E=LiteGraph.slot_types_in,C=E.length;(t.type_filter_in==LiteGraph.EVENT||t.type_filter_in==LiteGraph.ACTION)&&(t.type_filter_in="_event_");for(let N=0;N<C;N++){const S=document.createElement("option");S.value=E[N],S.innerHTML=E[N],u.appendChild(S),t.type_filter_in!==!1&&(t.type_filter_in+"").toLowerCase()==(E[N]+"").toLowerCase()&&(S.selected=!0)}u.addEventListener("change",function(){T()})}if(p){const E=LiteGraph.slot_types_out,C=E.length;(t.type_filter_out==LiteGraph.EVENT||t.type_filter_out==LiteGraph.ACTION)&&(t.type_filter_out="_event_");for(let N=0;N<C;N++){const S=document.createElement("option");S.value=E[N],S.innerHTML=E[N],p.appendChild(S),t.type_filter_out!==!1&&(t.type_filter_out+"").toLowerCase()==(E[N]+"").toLowerCase()&&(S.selected=!0)}p.addEventListener("change",function(){T()})}}const m=r.getBoundingClientRect(),w=(s?s.clientX:m.left+m.width*.5)-80,b=(s?s.clientY:m.top+m.height*.5)-20;l.style.left=w+"px",l.style.top=b+"px",s.layerY>m.height-200&&(d.style.maxHeight=m.height-s.layerY-20+"px"),requestAnimationFrame(function(){f.focus()}),t.show_all_on_open&&T();function k(E){if(E)if(n.onSearchBoxSelection)n.onSearchBoxSelection(E,s,o);else{const C=LiteGraph.searchbox_extras[E.toLowerCase()];C&&(E=C.type),o.graph.beforeChange();const N=LiteGraph.createNode(E);if(N&&(N.pos=o.convertEventToCanvasOffset(s),o.graph.add(N,!1)),C!=null&&C.data){if(C.data.properties)for(const S in C.data.properties)N.addProperty(S,C.data.properties[S]);if(C.data.inputs){N.inputs=[];for(const S in C.data.inputs)N.addOutput(C.data.inputs[S][0],C.data.inputs[S][1])}if(C.data.outputs){N.outputs=[];for(const S in C.data.outputs)N.addOutput(C.data.outputs[S][0],C.data.outputs[S][1])}C.data.title&&(N.title=C.data.title),C.data.json&&N.configure(C.data.json)}if(t.node_from){let S=!1;switch(typeof t.slot_from){case"string":S=t.node_from.findOutputSlot(t.slot_from);break;case"object":S=t.slot_from.name?t.node_from.findOutputSlot(t.slot_from.name):-1,S==-1&&typeof t.slot_from.slot_index<"u"&&(S=t.slot_from.slot_index);break;case"number":S=t.slot_from;break;default:S=0}typeof t.node_from.outputs[S]<"u"&&S!==!1&&S>-1&&t.node_from.connectByType(S,N,t.node_from.outputs[S].type)}if(t.node_to){let S=!1;switch(typeof t.slot_from){case"string":S=t.node_to.findInputSlot(t.slot_from);break;case"object":S=t.slot_from.name?t.node_to.findInputSlot(t.slot_from.name):-1,S==-1&&typeof t.slot_from.slot_index<"u"&&(S=t.slot_from.slot_index);break;case"number":S=t.slot_from;break;default:S=0}typeof t.node_to.inputs[S]<"u"&&S!==!1&&S>-1&&t.node_to.connectByTypeOutput(S,N,t.node_to.inputs[S].type)}o.graph.afterChange()}l.close()}function L(E){const C=g;g?(g.classList.remove("selected"),g=E?g.nextSibling:g.previousSibling,g||(g=C)):g=E?d.childNodes[0]:d.childNodes[d.childNodes.length],g&&(g.classList.add("selected"),g.scrollIntoView({block:"end",behavior:"smooth"}))}function T(){_=null;let E=f.value;if(c=null,d.innerHTML="",!E&&!t.show_all_if_empty)return;if(n.onSearchBox){const N=n.onSearchBox(d,E,o);if(N)for(let S=0;S<N.length;++S)C(N[S])}else{let N=function(H,X){var Le,Te;X=X||{};const $=Object.assign({skipFilter:!1,inTypeOverride:!1,outTypeOverride:!1},X),ke=LiteGraph.registered_node_types[H];if(D&&ke.filter!=D||(!t.show_all_if_empty||E)&&H.toLowerCase().indexOf(E)===-1&&(!ke.title||ke.title.toLowerCase().indexOf(E)===-1))return!1;if(t.do_type_filter&&!$.skipFilter){const ge=H;let q=$.inTypeOverride!==!1?$.inTypeOverride:P.value;if(P&&q&&((Le=LiteGraph.registered_slot_in_types[q])!=null&&Le.nodes)&&LiteGraph.registered_slot_in_types[q].nodes.includes(ge)===!1||(q=F.value,$.outTypeOverride!==!1&&(q=$.outTypeOverride),F&&q&&((Te=LiteGraph.registered_slot_out_types[q])!=null&&Te.nodes)&&LiteGraph.registered_slot_out_types[q].nodes.includes(ge)===!1))return!1}return!0},S=0;E=E.toLowerCase();const D=o.filter||o.graph.filter;let P=!1,F=!1;t.do_type_filter&&n.search_box&&(P=n.search_box.querySelector(".slot_in_type_filter"),F=n.search_box.querySelector(".slot_out_type_filter"));for(const H in LiteGraph.searchbox_extras){const X=LiteGraph.searchbox_extras[H];if((!t.show_all_if_empty||E)&&X.desc.toLowerCase().indexOf(E)===-1)continue;const K=LiteGraph.registered_node_types[X.type];if(!(K&&K.filter!=D)&&N(X.type)&&(C(X.desc,"searchbox_extra"),_LGraphCanvas.search_limit!==-1&&S++>_LGraphCanvas.search_limit))break}let U=null;if(Array.prototype.filter)U=Object.keys(LiteGraph.registered_node_types).filter(N);else{U=[];for(const H in LiteGraph.registered_node_types)N(H)&&U.push(H)}for(let H=0;H<U.length&&(C(U[H]),!(_LGraphCanvas.search_limit!==-1&&S++>_LGraphCanvas.search_limit));H++);if(t.show_general_after_typefiltered&&(P.value||F.value)){filtered_extra=[];for(const H in LiteGraph.registered_node_types)N(H,{inTypeOverride:P&&P.value?"*":!1,outTypeOverride:F&&F.value?"*":!1})&&filtered_extra.push(H);for(let H=0;H<filtered_extra.length&&(C(filtered_extra[H],"generic_type"),!(_LGraphCanvas.search_limit!==-1&&S++>_LGraphCanvas.search_limit));H++);}if((P.value||F.value)&&d.childNodes.length==0&&t.show_general_if_none_on_typefilter){filtered_extra=[];for(const H in LiteGraph.registered_node_types)N(H,{skipFilter:!0})&&filtered_extra.push(H);for(let H=0;H<filtered_extra.length&&(C(filtered_extra[H],"not_in_filter"),!(_LGraphCanvas.search_limit!==-1&&S++>_LGraphCanvas.search_limit));H++);}}function C(N,S){const D=document.createElement("div");c||(c=N);const P=LiteGraph.registered_node_types[N];if(P!=null&&P.title){D.innerText=P==null?void 0:P.title;const F=document.createElement("span");F.className="litegraph lite-search-item-type",F.textContent=N,D.append(F)}else D.innerText=N;D.dataset.type=escape(N),D.className="litegraph lite-search-item",S&&(D.className+=" "+S),D.addEventListener("click",function(){k(unescape(this.dataset.type))}),d.appendChild(D)}}return l}showEditPropertyValue(s,t,i){if(!s||s.properties[t]===void 0)return;i=i||{};const n=s.getPropertyInfo(t),o=n.type;let r="";if(o=="string"||o=="number"||o=="array"||o=="object")r="<input autofocus type='text' class='value'/>";else if((o=="enum"||o=="combo")&&n.values){r="<select autofocus type='text' class='value'>";for(const c in n.values){const _=Array.isArray(n.values)?n.values[c]:c;r+="<option value='"+_+"' "+(_==s.properties[t]?"selected":"")+">"+n.values[c]+"</option>"}r+="</select>"}else if(o=="boolean"||o=="toggle")r="<input autofocus type='checkbox' class='value' "+(s.properties[t]?"checked":"")+"/>";else{console.warn("unknown type: "+o);return}const a=this.createDialog("<span class='name'>"+(n.label||t)+"</span>"+r+"<button>OK</button>",i);let l;if((o=="enum"||o=="combo")&&n.values)l=a.querySelector("select"),l.addEventListener("change",function(c){var _;a.modified(),d((_=c.target)==null?void 0:_.value)});else if(o=="boolean"||o=="toggle")l=a.querySelector("input"),l==null||l.addEventListener("click",function(){a.modified(),d(!!l.checked)});else if(l=a.querySelector("input"),l){l.addEventListener("blur",function(){this.focus()});let c=s.properties[t]!==void 0?s.properties[t]:"";o!=="string"&&(c=JSON.stringify(c)),l.value=c,l.addEventListener("keydown",function(_){if(_.keyCode==27)a.close();else if(_.keyCode==13)p();else if(_.keyCode!=13){a.modified();return}_.preventDefault(),_.stopPropagation()})}l==null||l.focus(),a.querySelector("button").addEventListener("click",p);function p(){d(l.value)}function d(c){var _,g;n!=null&&n.values&&typeof n.values=="object"&&n.values[c]!=null&&(c=n.values[c]),typeof s.properties[t]=="number"&&(c=Number(c)),(o=="array"||o=="object")&&(c=JSON.parse(c)),s.properties[t]=c,s.graph&&s.graph._version++,(_=s.onPropertyChanged)==null||_.call(s,t,c),(g=i.onclose)==null||g.call(i),a.close(),this.setDirty(!0,!0)}return a}createDialog(s,t){t=Object.assign({checkForInput:!1,closeOnLeave:!0,closeOnLeave_checkModified:!0},t||{});const n=document.createElement("div");n.className="graphdialog",n.innerHTML=s,n.is_modified=!1;const o=this.canvas.getBoundingClientRect();let r=-20,a=-20;if(o&&(r-=o.left,a-=o.top),t.position?(r+=t.position[0],a+=t.position[1]):t.event?(r+=t.event.clientX,a+=t.event.clientY):(r+=this.canvas.width*.5,a+=this.canvas.height*.5),n.style.left=r+"px",n.style.top=a+"px",this.canvas.parentNode.appendChild(n),t.checkForInput){const d=n.querySelectorAll("input");d==null||d.forEach(function(c){c.addEventListener("keydown",function(_){if(n.modified(),_.keyCode==27)n.close();else if(_.keyCode!=13)return;_.preventDefault(),_.stopPropagation()}),c.focus()})}n.modified=function(){n.is_modified=!0},n.close=function(){var d;(d=n.parentNode)==null||d.removeChild(n)};let l=null,u=0;n.addEventListener("mouseleave",function(){u||!n.is_modified&&LiteGraph.dialog_close_on_mouse_leave&&(l=setTimeout(n.close,LiteGraph.dialog_close_on_mouse_leave_delay))}),n.addEventListener("mouseenter",function(){(t.closeOnLeave||LiteGraph.dialog_close_on_mouse_leave)&&l&&clearTimeout(l)});const p=n.querySelectorAll("select");return p==null||p.forEach(function(d){d.addEventListener("click",function(){u++}),d.addEventListener("blur",function(){u=0}),d.addEventListener("change",function(){u=-1})}),n}createPanel(s,t){t=t||{};const i=t.window||window,n=document.createElement("div");if(n.className="litegraph dialog",n.innerHTML="<div class='dialog-header'><span class='dialog-title'></span></div><div class='dialog-content'></div><div style='display:none;' class='dialog-alt-content'></div><div class='dialog-footer'></div>",n.header=n.querySelector(".dialog-header"),t.width&&(n.style.width=t.width+(typeof t.width=="number"?"px":"")),t.height&&(n.style.height=t.height+(typeof t.height=="number"?"px":"")),t.closable){const o=document.createElement("span");o.innerHTML="&#10005;",o.classList.add("close"),o.addEventListener("click",function(){n.close()}),n.header.appendChild(o)}return n.title_element=n.querySelector(".dialog-title"),n.title_element.innerText=s,n.content=n.querySelector(".dialog-content"),n.alt_content=n.querySelector(".dialog-alt-content"),n.footer=n.querySelector(".dialog-footer"),n.close=function(){var o,r;typeof n.onClose=="function"&&n.onClose(),(o=n.parentNode)==null||o.removeChild(n),(r=this.parentNode)==null||r.removeChild(this)},n.toggleAltContent=function(o){let r,a;typeof o<"u"?(r=o?"block":"none",a=o?"none":"block"):(r=n.alt_content.style.display!="block"?"block":"none",a=n.alt_content.style.display!="block"?"none":"block"),n.alt_content.style.display=r,n.content.style.display=a},n.toggleFooterVisibility=function(o){let r;typeof o<"u"?r=o?"block":"none":r=n.footer.style.display!="block"?"block":"none",n.footer.style.display=r},n.clear=function(){this.content.innerHTML=""},n.addHTML=function(o,r,a){const l=document.createElement("div");return r&&(l.className=r),l.innerHTML=o,a?n.footer.appendChild(l):n.content.appendChild(l),l},n.addButton=function(o,r,a){const l=document.createElement("button");return l.innerText=o,l.options=a,l.classList.add("btn"),l.addEventListener("click",r),n.footer.appendChild(l),l},n.addSeparator=function(){const o=document.createElement("div");o.className="separator",n.content.appendChild(o)},n.addWidget=function(o,r,a,l,u){l=l||{};let p=String(a);o=o.toLowerCase(),o=="number"&&(p=a.toFixed(3));const d=document.createElement("div");d.className="property",d.innerHTML="<span class='property_name'></span><span class='property_value'></span>",d.querySelector(".property_name").innerText=l.label||r;const c=d.querySelector(".property_value");if(c.innerText=p,d.dataset.property=r,d.dataset.type=l.type||o,d.options=l,d.value=a,o=="code")d.addEventListener("click",function(){n.inner_showCodePad(this.dataset.property)});else if(o=="boolean")d.classList.add("boolean"),a&&d.classList.add("bool-on"),d.addEventListener("click",function(){const g=this.dataset.property;this.value=!this.value,this.classList.toggle("bool-on"),this.querySelector(".property_value").innerText=this.value?"true":"false",_(g,this.value)});else if(o=="string"||o=="number")c.setAttribute("contenteditable",!0),c.addEventListener("keydown",function(g){g.code=="Enter"&&(o!="string"||!g.shiftKey)&&(g.preventDefault(),this.blur())}),c.addEventListener("blur",function(){let g=this.innerText;const f=this.parentNode.dataset.property;this.parentNode.dataset.type=="number"&&(g=Number(g)),_(f,g)});else if(o=="enum"||o=="combo"){const g=_LGraphCanvas.getPropertyPrintableValue(a,l.values);c.innerText=g,c.addEventListener("click",function(f){const m=l.values||[],w=this.parentNode.dataset.property,b=this;new LiteGraph.ContextMenu(m,{event:f,className:"dark",callback:k},i);function k(L){return b.innerText=L,_(w,L),!1}})}n.content.appendChild(d);function _(g,f){var m;(m=l.callback)==null||m.call(l,g,f,l),u==null||u(g,f,l)}return d},n.onOpen&&typeof n.onOpen=="function"&&n.onOpen(),n}closePanels(){var s,t;(s=document.querySelector("#node-panel"))==null||s.close(),(t=document.querySelector("#option-panel"))==null||t.close()}showShowNodePanel(s){this.SELECTED_NODE=s,this.closePanels();const t=this.getCanvasWindow(),i=this,n=this.createPanel(s.title||"",{closable:!0,window:t,onOpen:function(){i.NODEPANEL_IS_OPEN=!0},onClose:function(){i.NODEPANEL_IS_OPEN=!1,i.node_panel=null}});i.node_panel=n,n.id="node-panel",n.node=s,n.classList.add("settings");function o(){var l,u;n.content.innerHTML="",n.addHTML(`<span class='node_type'>${s.type}</span><span class='node_desc'>${s.constructor.desc||""}</span><span class='separator'></span>`),n.addHTML("<h3>Properties</h3>");const r=function(p,d){switch(i.graph.beforeChange(s),p){case"Title":s.title=d;break;case"Mode":{const c=Object.values(LiteGraph.NODE_MODES).indexOf(d);c>=0&&LiteGraph.NODE_MODES[c]?s.changeMode(c):console.warn("unexpected mode: "+d);break}case"Color":_LGraphCanvas.node_colors[d]?(s.color=_LGraphCanvas.node_colors[d].color,s.bgcolor=_LGraphCanvas.node_colors[d].bgcolor):console.warn("unexpected color: "+d);break;default:s.setProperty(p,d);break}i.graph.afterChange(),i.dirty_canvas=!0};n.addWidget("string","Title",s.title,{},r),n.addWidget("combo","Mode",LiteGraph.NODE_MODES[s.mode],{values:LiteGraph.NODE_MODES},r);const a=s.color!==void 0?Object.keys(_LGraphCanvas.node_colors).filter(function(p){return _LGraphCanvas.node_colors[p].color==s.color}):"";n.addWidget("combo","Color",a,{values:Object.keys(_LGraphCanvas.node_colors)},r);for(const p in s.properties){const d=s.properties[p],c=s.getPropertyInfo(p);(l=s.onAddPropertyToPanel)!=null&&l.call(s,p,n)||n.addWidget(c.widget||c.type,p,d,c,r)}n.addSeparator(),(u=s.onShowCustomPanelInfo)==null||u.call(s,n),n.footer.innerHTML="",n.addButton("Delete",function(){s.block_delete||(s.graph.remove(s),n.close())}).classList.add("delete")}n.inner_showCodePad=function(r){n.classList.remove("settings"),n.classList.add("centered"),n.alt_content.innerHTML="<textarea class='code'></textarea>";const a=n.alt_content.querySelector("textarea"),l=function(){n.toggleAltContent(!1),n.toggleFooterVisibility(!0),a.parentNode.removeChild(a),n.classList.add("settings"),n.classList.remove("centered"),o()};a.value=s.properties[r],a.addEventListener("keydown",function(d){d.code=="Enter"&&d.ctrlKey&&(s.setProperty(r,a.value),l())}),n.toggleAltContent(!0),n.toggleFooterVisibility(!1),a.style.height="calc(100% - 40px)";const u=n.addButton("Assign",function(){s.setProperty(r,a.value),l()});n.alt_content.appendChild(u);const p=n.addButton("Close",l);p.style.float="right",n.alt_content.appendChild(p)},o(),this.canvas.parentNode.appendChild(n)}checkPanels(){if(!this.canvas)return;const s=this.canvas.parentNode.querySelectorAll(".litegraph.dialog");for(let t=0;t<s.length;++t){const i=s[t];i.node&&(!i.node.graph||i.graph!=this.graph)&&i.close()}}getCanvasMenuOptions(){var i;let s=null;this.getMenuOptions?s=this.getMenuOptions():(s=[{content:"Add Node",has_submenu:!0,callback:_LGraphCanvas.onMenuAdd},{content:"Add Group",callback:_LGraphCanvas.onGroupAdd}],Object.keys(this.selected_nodes).length>1&&s.push({content:"Align",has_submenu:!0,callback:_LGraphCanvas.onGroupAlign}));const t=(i=this.getExtraMenuOptions)==null?void 0:i.call(this,this,s);return Array.isArray(t)?s.concat(t):s}getNodeMenuOptions(s){var r,a,l,u,p,d;let t=null;s.getMenuOptions?t=s.getMenuOptions(this):(t=[{content:"Inputs",has_submenu:!0,disabled:!0,callback:_LGraphCanvas.showMenuNodeOptionalInputs},{content:"Outputs",has_submenu:!0,disabled:!0,callback:_LGraphCanvas.showMenuNodeOptionalOutputs},null,{content:"Properties",has_submenu:!0,callback:_LGraphCanvas.onShowMenuNodeProperties},{content:"Properties Panel",callback:function(c,_,g,f,m){_LGraphCanvas.active_canvas.showShowNodePanel(m)}},null,{content:"Title",callback:_LGraphCanvas.onShowPropertyEditor},{content:"Mode",has_submenu:!0,callback:_LGraphCanvas.onMenuNodeMode}],s.resizable!==!1&&t.push({content:"Resize",callback:_LGraphCanvas.onMenuResizeNode}),s.collapsible&&t.push({content:s.collapsed?"Expand":"Collapse",callback:_LGraphCanvas.onMenuNodeCollapse}),(r=s.widgets)!=null&&r.some(c=>c.advanced)&&t.push({content:s.showAdvanced?"Hide Advanced":"Show Advanced",callback:_LGraphCanvas.onMenuToggleAdvanced}),t.push({content:s.pinned?"Unpin":"Pin",callback:(...c)=>{_LGraphCanvas.onMenuNodePin(...c);for(const _ in this.selected_nodes)this.selected_nodes[_].pin();this.setDirty(!0,!0)}},{content:"Colors",has_submenu:!0,callback:_LGraphCanvas.onMenuNodeColors},{content:"Shapes",has_submenu:!0,callback:_LGraphCanvas.onMenuNodeShapes},null));const i=(a=s.onGetInputs)==null?void 0:a.call(s);i!=null&&i.length&&(t[0].disabled=!1);const n=(l=s.onGetOutputs)==null?void 0:l.call(s);n!=null&&n.length&&(t[1].disabled=!1);const o=(u=s.getExtraMenuOptions)==null?void 0:u.call(s,this,t);return Array.isArray(o)&&o.length>0&&(o.push(null),t=o.concat(t)),s.clonable!==!1&&t.push({content:"Clone",callback:_LGraphCanvas.onMenuNodeClone}),Object.keys(this.selected_nodes).length>1&&(t.push({content:"Align Selected To",has_submenu:!0,callback:_LGraphCanvas.onNodeAlign}),t.push({content:"Distribute Nodes",has_submenu:!0,callback:_LGraphCanvas.createDistributeMenu})),t.push(null,{content:"Remove",disabled:!(s.removable!==!1&&!s.block_delete),callback:_LGraphCanvas.onMenuNodeRemove}),(d=(p=s.graph)==null?void 0:p.onGetNodeMenuOptions)==null||d.call(p,t,s),t}getGroupMenuOptions(s){return console.warn("LGraphCanvas.getGroupMenuOptions is deprecated, use LGraphGroup.getMenuOptions instead"),s.getMenuOptions()}processContextMenu(s,t){var p,d;const i=this,o=_LGraphCanvas.active_canvas.getCanvasWindow();let r=null;const a={event:t,callback:u,extra:s};s&&(a.title=s.type);let l=null;if(s&&(l=s.getSlotInPosition(t.canvasX,t.canvasY),_LGraphCanvas.active_node=s),l){if(r=[],s.getSlotMenuOptions)r=s.getSlotMenuOptions(l);else{(d=(p=l==null?void 0:l.output)==null?void 0:p.links)!=null&&d.length&&r.push({content:"Disconnect Links",slot:l});const c=l.input||l.output;c.removable&&r.push(c.locked?"Cannot remove":{content:"Remove Slot",slot:l}),c.nameLocked||r.push({content:"Rename Slot",slot:l})}a.title=(l.input?l.input.type:l.output.type)||"*",l.input&&l.input.type==LiteGraph.ACTION&&(a.title="Action"),l.output&&l.output.type==LiteGraph.EVENT&&(a.title="Event")}else if(s)r=this.getNodeMenuOptions(s);else{if(r=this.getCanvasMenuOptions(),this.reroutesEnabled&&this.links_render_mode!==LinkRenderType.HIDDEN_LINK){const _=this.graph.getRerouteOnPos(t.canvasX,t.canvasY);_&&r.unshift({content:"Delete Reroute",callback:()=>this.graph.removeReroute(_.id)},null)}const c=this.graph.getGroupOnPos(t.canvasX,t.canvasY);c&&r.push(null,{content:"Edit Group",has_submenu:!0,submenu:{title:"Group",extra:c,options:c.getMenuOptions()}})}if(!r)return;new LiteGraph.ContextMenu(r,a,o);function u(c,_){if(c){if(c.content=="Remove Slot"){const g=c.slot;s.graph.beforeChange(),g.input?s.removeInput(g.slot):g.output&&s.removeOutput(g.slot),s.graph.afterChange();return}else if(c.content=="Disconnect Links"){const g=c.slot;s.graph.beforeChange(),g.output?s.disconnectOutput(g.slot):g.input&&s.disconnectInput(g.slot),s.graph.afterChange();return}else if(c.content=="Rename Slot"){const g=c.slot,f=g.input?s.getInputInfo(g.slot):s.getOutputInfo(g.slot),m=i.createDialog("<span class='name'>Name</span><input autofocus type='text'/><button>OK</button>",_),w=m.querySelector("input");w&&f&&(w.value=f.label||"");const b=function(){s.graph.beforeChange(),w.value&&(f&&(f.label=w.value),i.setDirty(!0)),m.close(),s.graph.afterChange()};m.querySelector("button").addEventListener("click",b),w.addEventListener("keydown",function(k){if(m.is_modified=!0,k.keyCode==27)m.close();else if(k.keyCode==13)b();else if(k.keyCode!=13&&k.target.localName!="textarea")return;k.preventDefault(),k.stopPropagation()}),w.focus()}}}}animateToBounds(s,{duration:t=350,zoom:i=.75,easing:n=EaseFunction.EASE_IN_OUT_QUAD}={}){const o={linear:b=>b,easeInQuad:b=>b*b,easeOutQuad:b=>b*(2-b),easeInOutQuad:b=>b<.5?2*b*b:-1+(4-2*b)*b},r=o[n]??o.linear;let a=null;const l=performance.now(),u=this.ds.offset[0],p=this.ds.offset[1],d=this.ds.scale,c=this.canvas.width/window.devicePixelRatio,_=this.canvas.height/window.devicePixelRatio;let g=d,f=u,m=p;if(i>0){const b=i*c/Math.max(s[2],300),k=i*_/Math.max(s[3],300);g=Math.min(b,k,this.ds.max_scale)}f=-s[0]-s[2]*.5+c*.5/g,m=-s[1]-s[3]*.5+_*.5/g;const w=b=>{const k=b-l,L=Math.min(k/t,1),T=r(L);this.ds.offset[0]=u+(f-u)*T,this.ds.offset[1]=p+(m-p)*T,i>0&&(this.ds.scale=d+(g-d)*T),this.setDirty(!0,!0),L<1?a=requestAnimationFrame(w):cancelAnimationFrame(a)};a=requestAnimationFrame(w)}fitViewToSelectionAnimated(s={}){const t=this.selectedItems.size?Array.from(this.selectedItems):this.positionableItems;this.animateToBounds(createBounds(t),s)}};let LGraphCanvas=_LGraphCanvas;Ye=new WeakMap,ze=new WeakMap,je=new WeakMap,re=new WeakMap,Q=new WeakMap,Ke=new WeakMap,qe=new WeakMap,$e=new WeakMap,Ze=new WeakMap,Je=new WeakMap,be=new WeakSet,Ue=function(){if(this.state.shouldSetCursor){let s="default";this.state.draggingCanvas?s="grabbing":this.state.readOnly?s="grab":this.state.hoveringOver&CanvasItem.ResizeSe?s="se-resize":this.state.hoveringOver&CanvasItem.Node&&(s="crosshair"),this.canvas.style.cursor=s}},ae=new WeakMap,ee=new WeakMap,Fe=new WeakMap,se=new WeakMap,Qe=new WeakSet,mt=function(s){if(typeof s=="string"){const t=document.getElementById(s);if(!(t instanceof HTMLCanvasElement))throw"Error validating LiteGraph canvas: Canvas element not found";return t}return s},te=new WeakSet,ne=function(){this.dirty_canvas=!0,this.dirty_bgcanvas=!0},et=new WeakSet,yt=function(s,t){var l;const{pointer:i,graph:n}=this,o=s.canvasX,r=s.canvasY,a=s.ctrlKey||s.metaKey;if(a&&!s.altKey){const u=new Float32Array(4);u[0]=o,u[1]=r,u[2]=1,u[3]=1,i.onClick=p=>{const d=t??(this.reroutesEnabled?n.getRerouteOnPos(p.canvasX,p.canvasY):null)??n.getGroupTitlebarOnPos(p.canvasX,p.canvasY);this.processSelect(d,p)},i.onDragStart=()=>this.dragging_rectangle=u,i.onDragEnd=p=>M(this,ot,Tt).call(this,p,u),i.finally=()=>this.dragging_rectangle=null;return}if(this.read_only){i.finally=()=>this.dragging_canvas=!1,this.dragging_canvas=!0;return}if(LiteGraph.alt_drag_do_clone_nodes&&s.altKey&&!s.ctrlKey&&t&&this.allow_interaction){const u=(l=t.clone())==null?void 0:l.serialize(),p=LiteGraph.createNode(u.type);if(p){p.configure(u),p.pos[0]+=5,p.pos[1]+=5,this.allow_dragnodes?(i.onDragStart=d=>{n.add(p,!1),M(this,le,ve).call(this,p,d)},i.onDragEnd=d=>M(this,he,me).call(this,d)):(n.beforeChange(),n.add(p,!1),n.afterChange());return}}if(t&&(this.allow_interaction||t.flags.allow_interaction))M(this,tt,wt).call(this,s,a,t);else{if(this.reroutesEnabled&&this.links_render_mode!==LinkRenderType.HIDDEN_LINK){const c=n.getRerouteOnPos(o,r);if(c){if(s.shiftKey){const _=n._links.get(c.linkIds.values().next().value),g=n.getNodeById(_.origin_id),f=_.origin_slot,m={node:g,slot:f,input:null,pos:g.getConnectionPos(!1,f),afterRerouteId:c.id};this.connecting_links=[m],i.onDragStart=()=>m.output=g.outputs[f],this.dirty_bgcanvas=!0}i.onClick=()=>this.processSelect(c,s),i.onDragStart||(i.onDragStart=_=>M(this,le,ve).call(this,c,_,!0),i.onDragEnd=_=>M(this,he,me).call(this,_));return}}const{lineWidth:u}=this.ctx;this.ctx.lineWidth=this.connections_width+7;const p=(window==null?void 0:window.devicePixelRatio)||1;for(const c of this.renderedPaths){const _=c._pos;if(_){if((s.shiftKey||s.altKey)&&c.path&&this.ctx.isPointInStroke(c.path,o*p,r*p)){if(this.ctx.lineWidth=u,s.shiftKey&&!s.altKey){const g=c.origin_slot,f=n._nodes_by_id[c.origin_id],m={node:f,slot:g,pos:f.getConnectionPos(!1,g)};this.connecting_links=[m],c.parentId&&(m.afterRerouteId=c.parentId),i.onDragStart=()=>m.output=f.outputs[g];return}else if(this.reroutesEnabled&&s.altKey&&!s.shiftKey){const g=n.createReroute([o,r],c);i.onDragStart=f=>M(this,le,ve).call(this,g,f),i.onDragEnd=f=>M(this,he,me).call(this,f);return}}else if(isInRectangle(o,r,_[0]-4,_[1]-4,8,8)){this.ctx.lineWidth=u,i.onClick=()=>this.showLinkMenu(c,s),i.onDragStart=()=>this.dragging_canvas=!0,i.finally=()=>this.dragging_canvas=!1,this.over_link_center=null;return}}}this.ctx.lineWidth=u;const d=n.getGroupOnPos(o,r);if(this.selected_group=d,d){if(d.isInResize(o,r)){const c=d.boundingRect,_=o-(c[0]+c[2]),g=r-(c[1]+c[3]);i.onDragStart=()=>this.resizingGroup=d,i.onDrag=f=>{if(this.read_only)return;const m=[f.canvasX-d.pos[0]-_,f.canvasY-d.pos[1]-g];snapPoint(m,O(this,ee)),d.resize(m[0],m[1])&&(this.dirty_bgcanvas=!0)},i.finally=()=>this.resizingGroup=null}else{const _=(d.font_size||LiteGraph.DEFAULT_GROUP_FONT_SIZE)*1.4;isInRectangle(o,r,d.pos[0],d.pos[1],d.size[0],_)&&(i.onClick=()=>this.processSelect(d,s),i.onDragStart=g=>{d.recomputeInsideNodes(),M(this,le,ve).call(this,d,g,!0)},i.onDragEnd=g=>M(this,he,me).call(this,g))}i.onDoubleClick=()=>{this.emitEvent({subType:"group-double-click",originalEvent:s,group:d})}}else i.onDoubleClick=()=>{this.allow_searchbox&&(this.showSearchBox(s),s.preventDefault()),this.emitEvent({subType:"empty-double-click",originalEvent:s})}}!i.onDragStart&&!i.onClick&&!i.onDrag&&this.allow_dragcanvas&&(i.onClick=()=>this.processSelect(null,s),i.finally=()=>this.dragging_canvas=!1,this.dragging_canvas=!0)},tt=new WeakSet,wt=function(s,t,i){var d,c;const{pointer:n,graph:o}=this,r=s.canvasX,a=s.canvasY;n.onClick=()=>this.processSelect(i,s),i.flags.pinned||this.bringToFront(i);const l=i.isPointInCollapse(r,a);if(l)n.onClick=()=>{i.collapse(),this.setDirty(!0,!0)};else if(!i.flags.collapsed){if(i.resizable!==!1&&i.inResizeCorner(r,a)){const _=i.boundingRect,g=r-(_[0]+_[2]),f=a-(_[1]+_[3]);n.onDragStart=()=>{o.beforeChange(),this.resizing_node=i},n.onDrag=m=>{if(this.read_only)return;const w=[m.canvasX-i.pos[0]-g,m.canvasY-i.pos[1]-f];snapPoint(w,O(this,ee));const b=i.computeSize();w[0]=Math.max(b[0],w[0]),w[1]=Math.max(b[1],w[1]),i.setSize(w),M(this,te,ne).call(this)},n.onDragEnd=m=>{M(this,te,ne).call(this),o.afterChange(this.resizing_node)},n.finally=()=>this.resizing_node=null,this.canvas.style.cursor="se-resize";return}if(i.outputs)for(let _=0,g=i.outputs.length;_<g;++_){const f=i.outputs[_],m=i.getConnectionPos(!1,_);if(isInRectangle(r,a,m[0]-15,m[1]-10,30,20)){if(s.shiftKey&&((d=f.links)==null?void 0:d.length)>0){this.connecting_links=[];for(const w of f.links){const b=o._links.get(w),k=b.target_slot,L=o._nodes_by_id[b.target_id],T=L.inputs[k],A=L.getConnectionPos(!0,k);this.connecting_links.push({node:L,slot:k,input:T,output:null,pos:A,direction:i.horizontal!==!0?LinkDirection.RIGHT:LinkDirection.CENTER})}return}f.slot_index=_,this.connecting_links=[{node:i,slot:_,input:null,output:f,pos:m}],LiteGraph.shift_click_do_break_link_from?s.shiftKey&&i.disconnectOutput(_):LiteGraph.ctrl_alt_click_do_break_link&&t&&s.altKey&&!s.shiftKey&&i.disconnectOutput(_),n.onDoubleClick=()=>{var w;return(w=i.onOutputDblClick)==null?void 0:w.call(i,_,s)},n.onClick=()=>{var w;return(w=i.onOutputClick)==null?void 0:w.call(i,_,s)};return}}if(i.inputs)for(let _=0,g=i.inputs.length;_<g;++_){const f=i.inputs[_],m=i.getConnectionPos(!0,_);if(isInRectangle(r,a,m[0]-15,m[1]-10,30,20)){if(n.onDoubleClick=()=>{var w;return(w=i.onInputDblClick)==null?void 0:w.call(i,_,s)},n.onClick=()=>{var w;return(w=i.onInputClick)==null?void 0:w.call(i,_,s)},f.link!==null){const w=o._links.get(f.link),b=w.origin_slot,k=o._nodes_by_id[w.origin_id];if(LiteGraph.click_do_break_link_to||LiteGraph.ctrl_alt_click_do_break_link&&t&&s.altKey&&!s.shiftKey)i.disconnectInput(_);else if(s.shiftKey||this.allow_reconnect_links){const L={node:k,slot:b,output:k.outputs[b],pos:k.getConnectionPos(!1,b)};this.connecting_links=[L],n.onDragStart=()=>{this.allow_reconnect_links&&!LiteGraph.click_do_break_link_to&&i.disconnectInput(_),L.output=k.outputs[b]},this.dirty_bgcanvas=!0}}if(!n.onDragStart){const w={node:i,slot:_,output:null,pos:m};this.connecting_links=[w],n.onDragStart=()=>w.input=f,this.dirty_bgcanvas=!0}return}}}const u=[r-i.pos[0],a-i.pos[1]],p=i.getWidgetOnPos(r,a);if(p)M(this,it,bt).call(this,s,i,p),this.node_widget=[i,p];else{if(n.onDoubleClick=()=>{var _,g;u[1]<0&&!l&&((_=i.onNodeTitleDblClick)==null||_.call(i,s,u,this)),(g=i.onDblClick)==null||g.call(i,s,u,this),this.emitEvent({subType:"node-double-click",originalEvent:s,node:i}),this.processNodeDblClicked(i)},(c=i.onMouseDown)!=null&&c.call(i,s,u,this)||!this.allow_dragnodes)return;n.onDragStart=_=>M(this,le,ve).call(this,i,_,!0),n.onDragEnd=_=>M(this,he,me).call(this,_)}this.dirty_canvas=!0},it=new WeakSet,bt=function(e,node,widget){var s;const{pointer}=this;if(typeof widget.onPointerDown=="function"&&widget.onPointerDown(pointer,node,this))return;const width=widget.width||node.width,oldValue=widget.value,pos=this.graph_mouse,x=pos[0]-node.pos[0],y=pos[1]-node.pos[1];switch(widget.type){case"button":pointer.onClick=()=>{var t;(t=widget.callback)==null||t.call(widget,widget,this,node,pos,e),widget.clicked=!0,this.dirty_canvas=!0};break;case"slider":{if(widget.options.read_only)break;pointer.onDrag=t=>{const i=t.canvasX-node.pos[0],n=clamp((i-15)/(width-30),0,1);widget.value=widget.options.min+(widget.options.max-widget.options.min)*n,oldValue!=widget.value&&setWidgetValue(this,node,widget,widget.value),this.dirty_canvas=!0};break}case"number":{const delta=x<40?-1:x>width-40?1:0;pointer.onClick=upEvent=>{let newValue=widget.value+delta*.1*(widget.options.step||1);widget.options.min!=null&&newValue<widget.options.min&&(newValue=widget.options.min),widget.options.max!=null&&newValue>widget.options.max&&(newValue=widget.options.max),newValue!==widget.value&&setWidgetValue(this,node,widget,newValue),delta===0&&(this.prompt("Value",widget.value,v=>{if(/^[0-9+\-*/()\s]+|\d+\.\d+$/.test(v))try{v=eval(v)}catch{}widget.value=Number(v),setWidgetValue(this,node,widget,widget.value)},e),this.dirty_canvas=!0)},pointer.onDrag=t=>{const i=t.canvasX-node.pos[0];if(delta&&i>-3&&i<width+3)return;let n=widget.value;t.deltaX&&(n+=t.deltaX*.1*(widget.options.step||1)),widget.options.min!=null&&n<widget.options.min&&(n=widget.options.min),widget.options.max!=null&&n>widget.options.max&&(n=widget.options.max),n!==widget.value&&setWidgetValue(this,node,widget,n)};break}case"combo":{let t,i;pointer.onClick=n=>{const o=x<40?-1:x>width-40?1:0;if(t=widget.options.values,typeof t=="function"&&(t=t(widget,node)),i=null,i=Array.isArray(t)?t:Object.keys(t),o){let a=-1;this.last_mouseclick=0,a=typeof t=="object"?i.indexOf(String(widget.value))+o:i.indexOf(widget.value)+o,a>=i.length&&(a=i.length-1),a<0&&(a=0),widget.value=Array.isArray(t)?t[a]:a,oldValue!=widget.value&&setWidgetValue(this,node,widget,widget.value),this.dirty_canvas=!0;return}const r=t!=i?Object.values(t):t;new LiteGraph.ContextMenu(r,{scale:Math.max(1,this.ds.scale),event:e,className:"dark",callback:a=>(widget.value=t!=i?r.indexOf(a):a,setWidgetValue(this,node,widget,widget.value),this.dirty_canvas=!0,!1)})};break}case"toggle":pointer.onClick=()=>{widget.value=!widget.value,setWidgetValue(this,node,widget,widget.value)};break;case"string":case"text":pointer.onClick=()=>this.prompt("Value",widget.value,t=>setWidgetValue(this,node,widget,t),e,widget.options?widget.options.multiline:!1);break;default:if(widget.mouse){const t=widget.mouse(e,[x,y],node);t!=null&&(this.dirty_canvas=t)}break}oldValue!=widget.value&&((s=node.onWidgetChanged)==null||s.call(node,widget.name,widget.value,oldValue,widget),node.graph._version++),pointer.finally=()=>{if(widget.mouse){const{eUp:t}=pointer,{canvasX:i,canvasY:n}=t;widget.mouse(t,[i-node.pos[0],n-node.pos[1]],node)}this.node_widget=null};function setWidgetValue(t,i,n,o){var a,l,u;const r=n.type==="number"?Number(o):o;n.value=r,(a=n.options)!=null&&a.property&&i.properties[n.options.property]!==void 0&&i.setProperty(n.options.property,r),(l=n.callback)==null||l.call(n,n.value,t,i,pos,e),(u=i.onWidgetChanged)==null||u.call(i,n.name,r,oldValue,n),i.graph._version++}},st=new WeakSet,kt=function(s,t){const{pointer:i}=this;if(LiteGraph.middle_click_slot_add_default_node&&t&&this.allow_interaction&&!this.read_only&&!this.connecting_links&&!t.flags.collapsed){let n=!1,o=!1,r=!1;if(t.outputs)for(let a=0,l=t.outputs.length;a<l;++a){const u=t.outputs[a],p=t.getConnectionPos(!1,a);if(isInRectangle(s.canvasX,s.canvasY,p[0]-15,p[1]-10,30,20)){n=u,o=a,r=!0;break}}if(t.inputs)for(let a=0,l=t.inputs.length;a<l;++a){const u=t.inputs[a],p=t.getConnectionPos(!0,a);if(isInRectangle(s.canvasX,s.canvasY,p[0]-15,p[1]-10,30,20)){n=u,o=a,r=!1;break}}if(n&&o!==!1){const a=.5-(o+1)/(r?t.outputs.length:t.inputs.length),l=t.getBounding(),u=[r?l[0]+l[2]:l[0],s.canvasY-80];i.onClick=()=>this.createDefaultNodeForSlot({nodeFrom:r?t:null,slotFrom:r?o:null,nodeTo:r?null:t,slotTo:r?null:o,position:u,nodeType:"AUTO",posAdd:[r?30:-30,-a*130],posSizeFix:[r?0:-1,0]})}}this.allow_dragcanvas&&(i.onDragStart=()=>this.dragging_canvas=!0,i.finally=()=>this.dragging_canvas=!1)},nt=new WeakSet,Lt=function(s){if(!s.buttons){Y(this,se,null);return}const t=s.y-O(this,se).pos[1],n=O(this,se).scale-t/100;this.ds.changeScale(n,O(this,se).pos),this.graph.change()},le=new WeakSet,ve=function(s,t,i=!1){this.emitBeforeChange(),this.graph.beforeChange(),t.finally=()=>{this.isDragging=!1,this.graph.afterChange(),this.emitAfterChange()},this.processSelect(s,t.eDown,i),this.isDragging=!0},he=new WeakSet,me=function(s){var i;const{graph:t}=this;(s.shiftKey||LiteGraph.alwaysSnapToGrid)&&t.snapToGrid(this.selectedItems),this.dirty_canvas=!0,this.dirty_bgcanvas=!0,(i=this.onNodeMoved)==null||i.call(this,findFirstNode(this.selectedItems))},ot=new WeakSet,Tt=function(s,t){const{graph:i,selectedItems:n}=this,o=Math.abs(t[2]),r=Math.abs(t[3]);t[2]<0&&(t[0]-=o),t[3]<0&&(t[1]-=r),t[2]=o,t[3]=r;const a=[],l=[];for(const u of i._nodes)overlapBounding(t,u.boundingRect)&&(!u.selected||!n.has(u)?l.push(u):a.push(u));for(const u of i.groups)containsRect(t,u._bounding)&&(u.recomputeInsideNodes(),!u.selected||!n.has(u)?l.push(u):a.push(u));for(const u of i.reroutes.values())isPointInRect(u.pos,t)&&(n.add(u),u.selected=!0,!u.selected||!n.has(u)?l.push(u):a.push(u));if(s.shiftKey)for(const u of l)this.select(u);else if(s.altKey)for(const u of a)this.deselect(u);else{for(const u of n.values())a.includes(u)||this.deselect(u);for(const u of l)this.select(u)}},rt=new WeakSet,Ct=function(s){for(const t of this.renderedPaths){const i=t._pos;if(i&&isInRectangle(s.canvasX,s.canvasY,i[0]-4,i[1]-4,8,8))return t}},at=new WeakSet,Et=function(){return LiteGraph.snaps_for_comfy?this._highlight_pos??this.graph_mouse:this.graph_mouse},lt=new WeakSet,Nt=function(s,t){var T,A;if(!this._highlight_pos||(s.fillStyle="#ffcc00",s.beginPath(),((T=this._highlight_input)==null?void 0:T.shape)===RenderShape.ARROW?(s.moveTo(t[0]+8,t[1]+.5),s.lineTo(t[0]-4,t[1]+6+.5),s.lineTo(t[0]-4,t[1]-6+.5),s.closePath()):s.arc(t[0],t[1],6,0,Math.PI*2),s.fill(),!LiteGraph.snap_highlights_node))return;const n=this.node_over;if(!(n&&((A=this.connecting_links)!=null&&A[0])))return;const{strokeStyle:o,lineWidth:r}=s,a=n.boundingRect,l=3,u=LiteGraph.ROUND_RADIUS+l,p=a[0]-l,d=a[1]-l,c=a[2]+l*2,_=a[3]+l*2;s.beginPath(),s.roundRect(p,d,c,_,u);const g=this.connecting_links[0].output===null?0:1,f=g?-1:1,m=t[0],w=t[1],b=c<_?c:c*Math.max(_/c,.5),k=s.createRadialGradient(m,w,0,m,w,b);k.addColorStop(1,"#00000000"),k.addColorStop(0,"#ffcc00aa");const L=s.createLinearGradient(p,d,p+c,d);L.addColorStop(.5,"#00000000"),L.addColorStop(g+.67*f,"#ddeeff33"),L.addColorStop(g+f,"#ffcc0055"),s.setLineDash([u,u*.001]),s.lineWidth=1,s.strokeStyle=L,s.stroke(),s.strokeStyle=k,s.stroke(),s.setLineDash([]),s.lineWidth=r,s.strokeStyle=o},_e=new WeakSet,Ne=function(s,t,i,n=.25){switch(t){case LinkDirection.LEFT:s[0]+=i*-n;break;case LinkDirection.RIGHT:s[0]+=i*n;break;case LinkDirection.UP:s[1]+=i*-n;break;case LinkDirection.DOWN:s[1]+=i*n;break}},R(LGraphCanvas,Ye,new Float32Array(4)),R(LGraphCanvas,ze,new Float32Array(2)),R(LGraphCanvas,je,new Float32Array(4)),R(LGraphCanvas,re,new Float32Array(4)),R(LGraphCanvas,Q,new Float32Array(4)),R(LGraphCanvas,Ke,new Float32Array(2)),R(LGraphCanvas,qe,new Float32Array(2)),R(LGraphCanvas,$e,new Float32Array(2)),R(LGraphCanvas,Ze,new Float32Array(2)),R(LGraphCanvas,Je,new Float32Array(2)),h(LGraphCanvas,"DEFAULT_BACKGROUND_IMAGE","data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAIAAAD/gAIDAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAQBJREFUeNrs1rEKwjAUhlETUkj3vP9rdmr1Ysammk2w5wdxuLgcMHyptfawuZX4pJSWZTnfnu/lnIe/jNNxHHGNn//HNbbv+4dr6V+11uF527arU7+u63qfa/bnmh8sWLBgwYJlqRf8MEptXPBXJXa37BSl3ixYsGDBMliwFLyCV/DeLIMFCxYsWLBMwSt4Be/NggXLYMGCBUvBK3iNruC9WbBgwYJlsGApeAWv4L1ZBgsWLFiwYJmCV/AK3psFC5bBggULloJX8BpdwXuzYMGCBctgwVLwCl7Be7MMFixYsGDBsu8FH1FaSmExVfAxBa/gvVmwYMGCZbBg/W4vAQYA5tRF9QYlv/QAAAAASUVORK5CYII="),h(LGraphCanvas,"link_type_colors"),h(LGraphCanvas,"gradients",{}),h(LGraphCanvas,"search_limit",-1),h(LGraphCanvas,"node_colors",{red:{color:"#322",bgcolor:"#533",groupcolor:"#A88"},brown:{color:"#332922",bgcolor:"#593930",groupcolor:"#b06634"},green:{color:"#232",bgcolor:"#353",groupcolor:"#8A8"},blue:{color:"#223",bgcolor:"#335",groupcolor:"#88A"},pale_blue:{color:"#2a363b",bgcolor:"#3f5159",groupcolor:"#3f789e"},cyan:{color:"#233",bgcolor:"#355",groupcolor:"#8AA"},purple:{color:"#323",bgcolor:"#535",groupcolor:"#a1309b"},yellow:{color:"#432",bgcolor:"#653",groupcolor:"#b58b2a"},black:{color:"#222",bgcolor:"#000",groupcolor:"#444"}}),h(LGraphCanvas,"active_canvas"),h(LGraphCanvas,"active_node");class MapProxyHandler{getOwnPropertyDescriptor(t,i){const n=this.get(t,i);if(n)return{configurable:!0,enumerable:!0,value:n}}has(t,i){if(typeof i=="symbol")return!1;const n=parseInt(i,10);return t.has(isNaN(n)?i:n)}ownKeys(t){return[...t.keys()].map(i=>String(i))}get(t,i){if(i in t)return Reflect.get(t,i,t);if(typeof i=="symbol")return;const n=parseInt(i,10);return t.get(isNaN(n)?i:n)}set(t,i,n){if(typeof i=="symbol")return!1;const o=parseInt(i,10);return t.set(isNaN(o)?i:o,n),!0}deleteProperty(t,i){return t.delete(i)}static bindAllMethods(t){t.clear=t.clear.bind(t),t.delete=t.delete.bind(t),t.forEach=t.forEach.bind(t),t.get=t.get.bind(t),t.has=t.has.bind(t),t.set=t.set.bind(t),t.entries=t.entries.bind(t),t.keys=t.keys.bind(t),t.values=t.values.bind(t)}}var He;const ie=class{constructor(t){h(this,"_version");h(this,"_links",new Map);h(this,"links");h(this,"list_of_graphcanvas");h(this,"status");h(this,"state");h(this,"_nodes");h(this,"_nodes_by_id");h(this,"_nodes_in_order");h(this,"_nodes_executable");h(this,"_groups");h(this,"iteration");h(this,"globaltime");h(this,"runningtime");h(this,"fixedtime");h(this,"fixedtime_lapse");h(this,"elapsed_time");h(this,"last_update_time");h(this,"starttime");h(this,"catch_errors");h(this,"execution_timer_id");h(this,"errors_in_execution");h(this,"execution_time");h(this,"_last_trigger_time");h(this,"filter");h(this,"config");h(this,"vars");h(this,"nodes_executing");h(this,"nodes_actioning");h(this,"nodes_executedAction");h(this,"extra");h(this,"inputs");h(this,"outputs");R(this,He,new Map);h(this,"_input_nodes");LiteGraph.debug&&console.log("Graph created");const i=this._links;MapProxyHandler.bindAllMethods(i);const n=new MapProxyHandler;this.links=new Proxy(i,n),this.list_of_graphcanvas=null,this.clear(),t&&this.configure(t)}get empty(){return this._nodes.length+this._groups.length+this.reroutes.size===0}*positionableItems(){for(const t of this._nodes)yield t;for(const t of this._groups)yield t;for(const t of this.reroutes.values())yield t}get reroutes(){return O(this,He)}set reroutes(t){if(!t)throw new TypeError("Attempted to set LGraph.reroutes to a falsy value.");const i=O(this,He);if(t.size===0){i.clear();return}for(const n of i.keys())t.has(n)||i.delete(n);for(const[n,o]of t)i.set(n,o)}get last_node_id(){return this.state.lastNodeId}set last_node_id(t){this.state.lastNodeId=t}get last_link_id(){return this.state.lastLinkId}set last_link_id(t){this.state.lastLinkId=t}getSupportedTypes(){return this.supported_types||ie.supported_types}clear(){var t,i;if(this.stop(),this.status=ie.STATUS_STOPPED,this.state={lastGroupId:0,lastNodeId:0,lastLinkId:0,lastRerouteId:0},this._version=-1,this._nodes)for(let n=0;n<this._nodes.length;++n)(i=(t=this._nodes[n]).onRemoved)==null||i.call(t);this._nodes=[],this._nodes_by_id={},this._nodes_in_order=[],this._nodes_executable=null,this._links.clear(),this.reroutes.clear(),this._groups=[],this.iteration=0,this.config={},this.vars={},this.extra={},this.globaltime=0,this.runningtime=0,this.fixedtime=0,this.fixedtime_lapse=.01,this.elapsed_time=.01,this.last_update_time=0,this.starttime=0,this.catch_errors=!0,this.nodes_executing=[],this.nodes_actioning=[],this.nodes_executedAction=[],this.inputs={},this.outputs={},this.change(),this.canvasAction(n=>n.clear())}get nodes(){return this._nodes}get groups(){return this._groups}attachCanvas(t){var i;if(t.constructor!=LGraphCanvas)throw"attachCanvas expects a LGraphCanvas instance";t.graph!=this&&((i=t.graph)==null||i.detachCanvas(t)),t.graph=this,this.list_of_graphcanvas||(this.list_of_graphcanvas=[]),this.list_of_graphcanvas.push(t)}detachCanvas(t){if(!this.list_of_graphcanvas)return;const i=this.list_of_graphcanvas.indexOf(t);i!=-1&&(t.graph=null,this.list_of_graphcanvas.splice(i,1))}start(t){var n;if(this.status==ie.STATUS_RUNNING)return;this.status=ie.STATUS_RUNNING,(n=this.onPlayEvent)==null||n.call(this),this.sendEventToAllNodes("onStart"),this.starttime=LiteGraph.getTime(),this.last_update_time=this.starttime,t||(t=0);const i=this;if(t==0&&typeof window<"u"&&window.requestAnimationFrame){let o=function(){var r,a;i.execution_timer_id==-1&&(window.requestAnimationFrame(o),(r=i.onBeforeStep)==null||r.call(i),i.runStep(1,!i.catch_errors),(a=i.onAfterStep)==null||a.call(i))};this.execution_timer_id=-1,o()}else this.execution_timer_id=setInterval(function(){var o,r;(o=i.onBeforeStep)==null||o.call(i),i.runStep(1,!i.catch_errors),(r=i.onAfterStep)==null||r.call(i)},t)}stop(){var t;this.status!=ie.STATUS_STOPPED&&(this.status=ie.STATUS_STOPPED,(t=this.onStopEvent)==null||t.call(this),this.execution_timer_id!=null&&(this.execution_timer_id!=-1&&clearInterval(this.execution_timer_id),this.execution_timer_id=null),this.sendEventToAllNodes("onStop"))}runStep(t,i,n){var u,p,d,c,_,g;t=t||1;const o=LiteGraph.getTime();this.globaltime=.001*(o-this.starttime);const r=this._nodes_executable?this._nodes_executable:this._nodes;if(!r)return;if(n=n||r.length,i){for(let f=0;f<t;f++){for(let m=0;m<n;++m){const w=r[m];w.mode==LGraphEventMode.ALWAYS&&w.onExecute&&((u=w.doExecute)==null||u.call(w))}this.fixedtime+=this.fixedtime_lapse,(p=this.onExecuteStep)==null||p.call(this)}(d=this.onAfterExecute)==null||d.call(this)}else try{for(let f=0;f<t;f++){for(let m=0;m<n;++m){const w=r[m];w.mode==LGraphEventMode.ALWAYS&&((c=w.onExecute)==null||c.call(w))}this.fixedtime+=this.fixedtime_lapse,(_=this.onExecuteStep)==null||_.call(this)}(g=this.onAfterExecute)==null||g.call(this),this.errors_in_execution=!1}catch(f){if(this.errors_in_execution=!0,LiteGraph.throw_errors)throw f;LiteGraph.debug&&console.log("Error during execution: "+f),this.stop()}const a=LiteGraph.getTime();let l=a-o;l==0&&(l=1),this.execution_time=.001*l,this.globaltime+=.001*l,this.iteration+=1,this.elapsed_time=(a-this.last_update_time)*.001,this.last_update_time=a,this.nodes_executing=[],this.nodes_actioning=[],this.nodes_executedAction=[]}updateExecutionOrder(){this._nodes_in_order=this.computeExecutionOrder(!1),this._nodes_executable=[];for(let t=0;t<this._nodes_in_order.length;++t)this._nodes_in_order[t].onExecute&&this._nodes_executable.push(this._nodes_in_order[t])}computeExecutionOrder(t,i){var p;const n=[],o=[],r={},a={},l={};for(let d=0,c=this._nodes.length;d<c;++d){const _=this._nodes[d];if(t&&!_.onExecute)continue;r[_.id]=_;let g=0;if(_.inputs)for(let f=0,m=_.inputs.length;f<m;f++)((p=_.inputs[f])==null?void 0:p.link)!=null&&(g+=1);g==0?(o.push(_),i&&(_._level=1)):(i&&(_._level=0),l[_.id]=g)}for(;;){const d=o.shift();if(d===void 0)break;if(n.push(d),delete r[d.id],!!d.outputs)for(let c=0;c<d.outputs.length;c++){const _=d.outputs[c];if(!((_==null?void 0:_.links)==null||_.links.length==0))for(let g=0;g<_.links.length;g++){const f=_.links[g],m=this._links.get(f);if(!m||a[m.id])continue;const w=this.getNodeById(m.target_id);if(w==null){a[m.id]=!0;continue}i&&(!w._level||w._level<=d._level)&&(w._level=d._level+1),a[m.id]=!0,l[w.id]-=1,l[w.id]==0&&o.push(w)}}}for(const d in r)n.push(r[d]);n.length!=this._nodes.length&&LiteGraph.debug&&console.warn("something went wrong, nodes missing"),n.length;function u(d){const c=d.length;for(let _=0;_<c;++_)d[_].order=_}return u(n),n.sort(function(d,c){const _=d.constructor.priority||d.priority||0,g=c.constructor.priority||c.priority||0;return _==g?d.order-c.order:_-g}),u(n),n}getAncestors(t){const i=[],n=[t],o={};for(;n.length;){const r=n.shift();if(r!=null&&r.inputs){!o[r.id]&&r!=t&&(o[r.id]=!0,i.push(r));for(let a=0;a<r.inputs.length;++a){const l=r.getInputNode(a);l&&i.indexOf(l)==-1&&n.push(l)}}}return i.sort(function(r,a){return r.order-a.order}),i}arrange(t,i){t=t||100;const n=this.computeExecutionOrder(!1,!0),o=[];for(let a=0;a<n.length;++a){const l=n[a],u=l._level||1;o[u]||(o[u]=[]),o[u].push(l)}let r=t;for(let a=0;a<o.length;++a){const l=o[a];if(!l)continue;let u=100,p=t+LiteGraph.NODE_TITLE_HEIGHT;for(let d=0;d<l.length;++d){const c=l[d];c.pos[0]=i==LiteGraph.VERTICAL_LAYOUT?p:r,c.pos[1]=i==LiteGraph.VERTICAL_LAYOUT?r:p;const _=i==LiteGraph.VERTICAL_LAYOUT?1:0;c.size[_]>u&&(u=c.size[_]);const g=i==LiteGraph.VERTICAL_LAYOUT?0:1;p+=c.size[g]+t+LiteGraph.NODE_TITLE_HEIGHT}r+=u+t}this.setDirtyCanvas(!0,!0)}getTime(){return this.globaltime}getFixedTime(){return this.fixedtime}getElapsedTime(){return this.elapsed_time}sendEventToAllNodes(t,i,n){n=n||LGraphEventMode.ALWAYS;const o=this._nodes_in_order?this._nodes_in_order:this._nodes;if(o)for(let r=0,a=o.length;r<a;++r){const l=o[r];!l[t]||l.mode!=n||(i===void 0?l[t]():i&&i.constructor===Array?l[t].apply(l,i):l[t](i))}}canvasAction(t){var i;(i=this.list_of_graphcanvas)==null||i.forEach(t)}sendActionToCanvas(t,i){var n;if(this.list_of_graphcanvas)for(let o=0;o<this.list_of_graphcanvas.length;++o){const r=this.list_of_graphcanvas[o];(n=r[t])==null||n.apply(r,i)}}add(t,i){var o,r;if(!t)return;const{state:n}=this;if(LiteGraph.alwaysSnapToGrid){const a=this.getSnapToGridSize();a&&t.snapToGrid(a)}if(t instanceof LGraphGroup){(t.id==null||t.id===-1)&&(t.id=++n.lastGroupId),t.id>n.lastGroupId&&(n.lastGroupId=t.id),this._groups.push(t),this.setDirtyCanvas(!0),this.change(),t.graph=this,this._version++;return}if(t.id!=-1&&this._nodes_by_id[t.id]!=null&&(console.warn("LiteGraph: there is already a node with this ID, changing it"),t.id=LiteGraph.use_uuids?LiteGraph.uuidv4():++n.lastNodeId),this._nodes.length>=LiteGraph.MAX_NUMBER_OF_NODES)throw"LiteGraph: max number of nodes in a graph reached";return LiteGraph.use_uuids?(t.id==null||t.id==-1)&&(t.id=LiteGraph.uuidv4()):t.id==null||t.id==-1?t.id=++n.lastNodeId:typeof t.id=="number"&&n.lastNodeId<t.id&&(n.lastNodeId=t.id),t.graph=this,this._version++,this._nodes.push(t),this._nodes_by_id[t.id]=t,(o=t.onAdded)==null||o.call(t,this),this.config.align_to_grid&&t.alignToGrid(),i||this.updateExecutionOrder(),(r=this.onNodeAdded)==null||r.call(this,t),this.setDirtyCanvas(!0),this.change(),t}remove(t){var n,o,r;if(t instanceof LGraphGroup){const a=this._groups.indexOf(t);a!=-1&&this._groups.splice(a,1),t.graph=null,this._version++,this.setDirtyCanvas(!0,!0),this.change();return}if(this._nodes_by_id[t.id]==null||t.ignore_remove)return;if(this.beforeChange(),t.inputs)for(let a=0;a<t.inputs.length;a++)t.inputs[a].link!=null&&t.disconnectInput(a);if(t.outputs)for(let a=0;a<t.outputs.length;a++)(n=t.outputs[a].links)!=null&&n.length&&t.disconnectOutput(a);if((o=t.onRemoved)==null||o.call(t),t.graph=null,this._version++,this.list_of_graphcanvas)for(let a=0;a<this.list_of_graphcanvas.length;++a){const l=this.list_of_graphcanvas[a];l.selected_nodes[t.id]&&delete l.selected_nodes[t.id]}const i=this._nodes.indexOf(t);i!=-1&&this._nodes.splice(i,1),delete this._nodes_by_id[t.id],(r=this.onNodeRemoved)==null||r.call(this,t),this.canvasAction(a=>a.checkPanels()),this.setDirtyCanvas(!0,!0),this.afterChange(),this.change(),this.updateExecutionOrder()}getNodeById(t){return t!=null?this._nodes_by_id[t]:null}findNodesByClass(t,i){i=i||[],i.length=0;for(let n=0,o=this._nodes.length;n<o;++n)this._nodes[n].constructor===t&&i.push(this._nodes[n]);return i}findNodesByType(t,i){var o;const n=t.toLowerCase();i=i||[],i.length=0;for(let r=0,a=this._nodes.length;r<a;++r)((o=this._nodes[r].type)==null?void 0:o.toLowerCase())==n&&i.push(this._nodes[r]);return i}findNodeByTitle(t){for(let i=0,n=this._nodes.length;i<n;++i)if(this._nodes[i].title==t)return this._nodes[i];return null}findNodesByTitle(t){const i=[];for(let n=0,o=this._nodes.length;n<o;++n)this._nodes[n].title==t&&i.push(this._nodes[n]);return i}getNodeOnPos(t,i,n){const o=n||this._nodes;let r=o.length;for(;--r>=0;){const a=o[r];if(a.isPointInside(t,i))return a}return null}getGroupOnPos(t,i){return this._groups.toReversed().find(n=>n.isPointInside(t,i))}getGroupTitlebarOnPos(t,i){return this._groups.toReversed().find(n=>n.isPointInTitlebar(t,i))}getRerouteOnPos(t,i){for(const n of this.reroutes.values()){const{pos:o}=n;if(isSortaInsideOctagon(t-o[0],i-o[1],2*Reroute.radius))return n}}snapToGrid(t){const i=this.getSnapToGridSize();i&&getAllNestedItems(t).forEach(n=>{n.pinned||n.snapToGrid(i)})}getSnapToGridSize(){return LiteGraph.alwaysSnapToGrid?LiteGraph.CANVAS_GRID_SIZE||1:LiteGraph.CANVAS_GRID_SIZE}checkNodeTypes(){for(let t=0;t<this._nodes.length;t++){const i=this._nodes[t],n=LiteGraph.registered_node_types[i.type];if(i.constructor==n)continue;console.log("node being replaced by newer version: "+i.type);const o=LiteGraph.createNode(i.type);this._nodes[t]=o,o.configure(i.serialize()),o.graph=this,this._nodes_by_id[o.id]=o,i.inputs&&(o.inputs=i.inputs.concat()),i.outputs&&(o.outputs=i.outputs.concat())}this.updateExecutionOrder()}onAction(t,i,n){this._input_nodes=this.findNodesByClass(LiteGraph.GraphInput,this._input_nodes);for(let o=0;o<this._input_nodes.length;++o){const r=this._input_nodes[o];if(r.properties.name==t){r.actionDo(t,i,n);break}}}trigger(t,i){var n;(n=this.onTrigger)==null||n.call(this,t,i)}addInput(t,i,n){var r,a;this.inputs[t]||(this.beforeChange(),this.inputs[t]={name:t,type:i,value:n},this._version++,this.afterChange(),(r=this.onInputAdded)==null||r.call(this,t,i),(a=this.onInputsOutputsChange)==null||a.call(this))}setInputData(t,i){const n=this.inputs[t];n&&(n.value=i)}getInputData(t){const i=this.inputs[t];return i?i.value:null}renameInput(t,i){var n,o;if(i!=t){if(!this.inputs[t])return!1;if(this.inputs[i])return console.error("there is already one input with that name"),!1;this.inputs[i]=this.inputs[t],delete this.inputs[t],this._version++,(n=this.onInputRenamed)==null||n.call(this,t,i),(o=this.onInputsOutputsChange)==null||o.call(this)}}changeInputType(t,i){var n;if(!this.inputs[t])return!1;this.inputs[t].type&&String(this.inputs[t].type).toLowerCase()==String(i).toLowerCase()||(this.inputs[t].type=i,this._version++,(n=this.onInputTypeChanged)==null||n.call(this,t,i))}removeInput(t){var i,n;return this.inputs[t]?(delete this.inputs[t],this._version++,(i=this.onInputRemoved)==null||i.call(this,t),(n=this.onInputsOutputsChange)==null||n.call(this),!0):!1}addOutput(t,i,n){var o,r;this.outputs[t]={name:t,type:i,value:n},this._version++,(o=this.onOutputAdded)==null||o.call(this,t,i),(r=this.onInputsOutputsChange)==null||r.call(this)}setOutputData(t,i){const n=this.outputs[t];n&&(n.value=i)}getOutputData(t){const i=this.outputs[t];return i?i.value:null}renameOutput(t,i){var n,o;if(!this.outputs[t])return!1;if(this.outputs[i])return console.error("there is already one output with that name"),!1;this.outputs[i]=this.outputs[t],delete this.outputs[t],this._version++,(n=this.onOutputRenamed)==null||n.call(this,t,i),(o=this.onInputsOutputsChange)==null||o.call(this)}changeOutputType(t,i){var n;if(!this.outputs[t])return!1;this.outputs[t].type&&String(this.outputs[t].type).toLowerCase()==String(i).toLowerCase()||(this.outputs[t].type=i,this._version++,(n=this.onOutputTypeChanged)==null||n.call(this,t,i))}removeOutput(t){var i,n;return this.outputs[t]?(delete this.outputs[t],this._version++,(i=this.onOutputRemoved)==null||i.call(this,t),(n=this.onInputsOutputsChange)==null||n.call(this),!0):!1}triggerInput(t,i){const n=this.findNodesByTitle(t);for(let o=0;o<n.length;++o)n[o].onTrigger(i)}setCallback(t,i){const n=this.findNodesByTitle(t);for(let o=0;o<n.length;++o)n[o].setTrigger(i)}beforeChange(t){var i;(i=this.onBeforeChange)==null||i.call(this,this,t),this.canvasAction(n=>{var o;return(o=n.onBeforeChange)==null?void 0:o.call(n,this)})}afterChange(t){var i;(i=this.onAfterChange)==null||i.call(this,this,t),this.canvasAction(n=>{var o;return(o=n.onAfterChange)==null?void 0:o.call(n,this)})}connectionChange(t){var i;this.updateExecutionOrder(),(i=this.onConnectionChange)==null||i.call(this,t),this._version++,this.canvasAction(n=>{var o;return(o=n.onConnectionChange)==null?void 0:o.call(n)})}clearTriggeredSlots(){for(const t of this._links.values())t&&t._last_time&&(t._last_time=0)}change(){var t;LiteGraph.debug&&console.log("Graph changed"),this.canvasAction(i=>i.setDirty(!0,!0)),(t=this.on_change)==null||t.call(this,this)}setDirtyCanvas(t,i){this.canvasAction(n=>n.setDirty(t,i))}setReroute({id:t,parentId:i,pos:n,linkIds:o}){t??(t=++this.state.lastRerouteId),t>this.state.lastRerouteId&&(this.state.lastRerouteId=t);const r=this.reroutes.get(t)??new Reroute(t,this);return r.update(i,n,o),this.reroutes.set(t,r),r}createReroute(t,i){var a;const n=++this.state.lastRerouteId,o=i instanceof Reroute?i.linkIds:[i.id],r=new Reroute(n,this,t,i.parentId,o);this.reroutes.set(n,r);for(const l of o){const u=this._links.get(l);u&&(u.parentId===i.parentId&&(u.parentId=n),(a=LLink.getReroutes(this,u))==null||a.filter(p=>p.parentId===i.parentId).forEach(p=>p.parentId=n))}return r}removeReroute(t){const{reroutes:i}=this,n=i.get(t);if(!n)return;const{parentId:o,linkIds:r}=n;for(const a of i.values())a.parentId===t&&(a.parentId=o);for(const a of r){const l=this._links.get(a);l&&l.parentId===t&&(l.parentId=o)}i.delete(t),this.setDirtyCanvas(!1,!0)}removeLink(t){const i=this._links.get(t);if(!i)return;const n=this.getNodeById(i.target_id);n==null||n.disconnectInput(i.target_slot),i.disconnect(this)}serialize(t){const{config:i,state:n,groups:o,nodes:r,reroutes:a,extra:l}=this.asSerialisable(t),u=[...this._links.values()],p=u.map(d=>d.serialize());return a.length&&(l.reroutes=a,l.linkExtensions=u.filter(d=>d.parentId!==void 0).map(d=>({id:d.id,parentId:d.parentId}))),{last_node_id:n.lastNodeId,last_link_id:n.lastLinkId,nodes:r,links:p,groups:o,config:i,extra:l,version:LiteGraph.VERSION}}asSerialisable(t){var c;const{config:i,state:n,extra:o}=this,a=(!LiteGraph.use_uuids&&(t!=null&&t.sortNodes)?[...this._nodes].sort((_,g)=>_.id-g.id):this._nodes).map(_=>_.serialize()),l=this._groups.map(_=>_.serialize()),u=[...this._links.values()].map(_=>_.asSerialisable()),p=[...this.reroutes.values()].map(_=>_.asSerialisable()),d={version:ie.serialisedSchemaVersion,config:i,state:n,groups:l,nodes:a,links:u,reroutes:p,extra:o};return(c=this.onSerialize)==null||c.call(this,d),d}configure(t,i){var l;if(!t)return;i||this.clear();const{extra:n}=t;let o;if(t.version===.4){if(Array.isArray(t.links))for(const u of t.links){const p=LLink.createFromArray(u);this._links.set(p.id,p)}if(Array.isArray(n==null?void 0:n.linkExtensions))for(const u of n.linkExtensions){const p=this._links.get(u.id);p&&(p.parentId=u.parentId)}o=n==null?void 0:n.reroutes}else{if(t.state){const{state:{lastGroupId:u,lastLinkId:p,lastNodeId:d,lastRerouteId:c}}=t;u!=null&&(this.state.lastGroupId=u),p!=null&&(this.state.lastLinkId=p),d!=null&&(this.state.lastNodeId=d),c!=null&&(this.state.lastRerouteId=c)}if(Array.isArray(t.links))for(const u of t.links){const p=LLink.create(u);this._links.set(p.id,p)}o=t.reroutes}if(Array.isArray(o))for(const u of o)this.setReroute(u).validateLinks(this._links)||this.reroutes.delete(u.id);const r=t.nodes;for(const u in t)u=="nodes"||u=="groups"||u=="links"||u==="state"||u==="reroutes"||(this[u]=t[u]);let a=!1;if(this._nodes=[],r){for(let u=0,p=r.length;u<p;++u){const d=r[u];let c=LiteGraph.createNode(d.type,d.title);c||(LiteGraph.debug&&console.log("Node not found or has errors: "+d.type),c=new LGraphNode(void 0),c.last_serialization=d,c.has_errors=!0,a=!0),c.id=d.id,this.add(c,!0)}for(let u=0,p=r.length;u<p;++u){const d=r[u],c=this.getNodeById(d.id);c==null||c.configure(d)}}if(this._groups.length=0,t.groups)for(let u=0;u<t.groups.length;++u){const p=new LiteGraph.LGraphGroup;p.configure(t.groups[u]),this.add(p)}return this.updateExecutionOrder(),this.extra=t.extra||{},(l=this.onConfigure)==null||l.call(this,t),this._version++,this.setDirtyCanvas(!0,!0),a}load(t,i){const n=this;if(t instanceof Blob||t instanceof File){const r=new FileReader;r.addEventListener("load",function(a){const l=JSON.parse(a.target.result.toString());n.configure(l),i==null||i()}),r.readAsText(t);return}const o=new XMLHttpRequest;o.open("GET",t,!0),o.send(null),o.onload=function(){if(o.status!==200){console.error("Error loading graph:",o.status,o.response);return}const r=JSON.parse(o.response);n.configure(r),i==null||i()},o.onerror=function(r){console.error("Error loading graph:",r)}}onNodeTrace(t,i){}};let LGraph=ie;He=new WeakMap,h(LGraph,"serialisedSchemaVersion",1),h(LGraph,"supported_types",["number","string","boolean"]),h(LGraph,"STATUS_STOPPED",1),h(LGraph,"STATUS_RUNNING",2);class ContextMenu{constructor(t,i){h(this,"options");h(this,"parentMenu");h(this,"root");h(this,"current_submenu");h(this,"lock");var c,_;i||(i={}),this.options=i;const n=i.parentMenu;n&&(n instanceof ContextMenu?(this.parentMenu=n,this.parentMenu.lock=!0,this.parentMenu.current_submenu=this):(console.error("parentMenu must be of class ContextMenu, ignoring it"),i.parentMenu=null),((c=n.options)==null?void 0:c.className)==="dark"&&(i.className="dark"));const o=i.event?i.event.constructor.name:null;o!=="MouseEvent"&&o!=="CustomEvent"&&o!=="PointerEvent"&&(console.error(`Event passed to ContextMenu is not of type MouseEvent or CustomEvent. Ignoring it. (${o})`),i.event=null);const r=document.createElement("div");let a="litegraph litecontextmenu litemenubar-panel";if(i.className&&(a+=" "+i.className),r.className=a,r.style.minWidth="100",r.style.minHeight="100",r.style.pointerEvents="none",setTimeout(function(){r.style.pointerEvents="auto"},100),LiteGraph.pointerListenerAdd(r,"up",function(g){return g.preventDefault(),!0},!0),r.addEventListener("contextmenu",function(g){return g.button!=2||g.preventDefault(),!1},!0),LiteGraph.pointerListenerAdd(r,"down",g=>{if(g.button==2)return this.close(),g.preventDefault(),!0},!0),this.root=r,i.title){const g=document.createElement("div");g.className="litemenu-title",g.innerHTML=i.title,r.appendChild(g)}for(let g=0;g<t.length;g++){const f=t[g];let m=Array.isArray(t)?f:String(g);typeof m!="string"&&(m=m!=null?m.content===void 0?String(m):m.content:m),this.addItem(m,f,i)}LiteGraph.pointerListenerAdd(r,"enter",function(){r.closing_timer&&clearTimeout(r.closing_timer)});const u=((_=i.event)==null?void 0:_.target).ownerDocument||document;u.fullscreenElement?u.fullscreenElement.appendChild(r):u.body.appendChild(r);let p=i.left||0,d=i.top||0;if(i.event){if(p=i.event.clientX-10,d=i.event.clientY-10,i.title&&(d-=20),n){const m=n.root.getBoundingClientRect();p=m.left+m.width}const g=document.body.getBoundingClientRect(),f=r.getBoundingClientRect();g.height==0&&console.error("document.body height is 0. That is dangerous, set html,body { height: 100%; }"),g.width&&p>g.width-f.width-10&&(p=g.width-f.width-10),g.height&&d>g.height-f.height-10&&(d=g.height-f.height-10)}r.style.left=p+"px",r.style.top=d+"px",LiteGraph.context_menu_scaling&&i.scale&&(r.style.transform=`scale(${Math.round(i.scale*4)*.25})`)}addItem(t,i,n){n||(n={});const o=document.createElement("div");o.className="litemenu-entry submenu";let r=!1;i===null?o.classList.add("separator"):(typeof i=="string"?o.innerHTML=t:(o.innerHTML=(i==null?void 0:i.title)??t,i.disabled&&(r=!0,o.classList.add("disabled"),o.setAttribute("aria-disabled","true")),(i.submenu||i.has_submenu)&&(o.classList.add("has_submenu"),o.setAttribute("aria-haspopup","true"),o.setAttribute("aria-expanded","false")),i.className&&(o.className+=" "+i.className)),o.value=i,o.setAttribute("role","menuitem"),typeof i=="function"?(o.dataset.value=t,o.onclick_callback=i):o.dataset.value=String(i)),this.root.appendChild(o),r||o.addEventListener("click",p),!r&&n.autoopen&&LiteGraph.pointerListenerAdd(o,"enter",l);const a=()=>{const d=this.root.querySelectorAll("div.litemenu-entry.has_submenu");if(d)for(let c=0;c<d.length;c++)d[c].setAttribute("aria-expanded","false");o.setAttribute("aria-expanded","true")};function l(d){const c=this.value;!c||!c.has_submenu||(p.call(this,d),a())}const u=this;function p(d){var g;const c=this.value;let _=!0;if((g=u.current_submenu)==null||g.close(d),(c!=null&&c.has_submenu||c!=null&&c.submenu)&&a(),n.callback&&n.callback.call(this,c,n,d,u,n.node)===!0&&(_=!1),typeof c=="object"&&(c.callback&&!n.ignore_item_callbacks&&c.disabled!==!0&&c.callback.call(this,c,n,d,u,n.extra)===!0&&(_=!1),c.submenu)){if(!c.submenu.options)throw"ContextMenu submenu needs options";new u.constructor(c.submenu.options,{callback:c.submenu.callback,event:d,parentMenu:u,ignore_item_callbacks:c.submenu.ignore_item_callbacks,title:c.submenu.title,extra:c.submenu.extra,autoopen:n.autoopen}),_=!1}_&&!u.lock&&u.close()}return o}close(t,i){var n;this.root.remove(),this.parentMenu&&!i&&(this.parentMenu.lock=!1,this.parentMenu.current_submenu=null,t===void 0?this.parentMenu.close():t&&!ContextMenu.isCursorOverElement(t,this.parentMenu.root)&&ContextMenu.trigger(this.parentMenu.root,LiteGraph.pointerevents_method+"leave",t)),(n=this.current_submenu)==null||n.close(t,!0),this.root.closing_timer&&clearTimeout(this.root.closing_timer)}static trigger(t,i,n,o){const r=document.createEvent("CustomEvent");return r.initCustomEvent(i,!0,!0,n),t.dispatchEvent?t.dispatchEvent(r):t.__events&&t.__events.dispatchEvent(r),r}getTopMenu(){return this.options.parentMenu?this.options.parentMenu.getTopMenu():this}getFirstEvent(){return this.options.parentMenu?this.options.parentMenu.getFirstEvent():this.options.event}static isCursorOverElement(t,i){const n=t.clientX,o=t.clientY,r=i.getBoundingClientRect();return r?o>r.top&&o<r.top+r.height&&n>r.left&&n<r.left+r.width:!1}}class CurveEditor{constructor(t){h(this,"points");h(this,"selected");h(this,"nearest");h(this,"size");h(this,"must_update");h(this,"margin");h(this,"_nearest");this.points=t,this.selected=-1,this.nearest=-1,this.size=null,this.must_update=!0,this.margin=5}static sampleCurve(t,i){if(i){for(let n=0;n<i.length-1;++n){const o=i[n],r=i[n+1];if(r[0]<t)continue;const a=r[0]-o[0];if(Math.abs(a)<1e-5)return o[1];const l=(t-o[0])/a;return o[1]*(1-l)+r[1]*l}return 0}}draw(t,i,n,o,r,a=!1){const l=this.points;if(!l)return;this.size=i;const u=i[0]-this.margin*2,p=i[1]-this.margin*2;r=r||"#666",t.save(),t.translate(this.margin,this.margin),o&&(t.fillStyle="#111",t.fillRect(0,0,u,p),t.fillStyle="#222",t.fillRect(u*.5,0,1,p),t.strokeStyle="#333",t.strokeRect(0,0,u,p)),t.strokeStyle=r,a&&(t.globalAlpha=.5),t.beginPath();for(let d=0;d<l.length;++d){const c=l[d];t.lineTo(c[0]*u,(1-c[1])*p)}if(t.stroke(),t.globalAlpha=1,!a)for(let d=0;d<l.length;++d){const c=l[d];t.fillStyle=this.selected==d?"#FFF":this.nearest==d?"#DDD":"#AAA",t.beginPath(),t.arc(c[0]*u,(1-c[1])*p,2,0,Math.PI*2),t.fill()}t.restore()}onMouseDown(t,i){const n=this.points;if(!n||t[1]<0)return;const o=this.size[0]-this.margin*2,r=this.size[1]-this.margin*2,a=t[0]-this.margin,l=t[1]-this.margin,u=[a,l],p=30/i.ds.scale;if(this.selected=this.getCloserPoint(u,p),this.selected==-1){const d=[a/o,1-l/r];n.push(d),n.sort(function(c,_){return c[0]-_[0]}),this.selected=n.indexOf(d),this.must_update=!0}if(this.selected!=-1)return!0}onMouseMove(t,i){const n=this.points;if(!n)return;const o=this.selected;if(o<0)return;const r=(t[0]-this.margin)/(this.size[0]-this.margin*2),a=(t[1]-this.margin)/(this.size[1]-this.margin*2),l=[t[0]-this.margin,t[1]-this.margin],u=30/i.ds.scale;this._nearest=this.getCloserPoint(l,u);const p=n[o];if(p){const d=o==0||o==n.length-1;if(!d&&(t[0]<-10||t[0]>this.size[0]+10||t[1]<-10||t[1]>this.size[1]+10)){n.splice(o,1),this.selected=-1;return}d?p[0]=o==0?0:1:p[0]=clamp(r,0,1),p[1]=1-clamp(a,0,1),n.sort(function(c,_){return c[0]-_[0]}),this.selected=n.indexOf(p),this.must_update=!0}}onMouseUp(){return this.selected=-1,!1}getCloserPoint(t,i){const n=this.points;if(!n)return-1;i=i||30;const o=this.size[0]-this.margin*2,r=this.size[1]-this.margin*2,a=n.length,l=[0,0];let u=1e6,p=-1;for(let d=0;d<a;++d){const c=n[d];l[0]=c[0]*o,l[1]=(1-c[1])*r;const _=distance(t,l);_>u||_>i||(p=d,u=_)}return p}}const ft=class{constructor(){h(this,"SlotShape",SlotShape);h(this,"SlotDirection",SlotDirection);h(this,"SlotType",SlotType);h(this,"LabelPosition",LabelPosition);h(this,"VERSION",.4);h(this,"CANVAS_GRID_SIZE",10);h(this,"NODE_TITLE_HEIGHT",30);h(this,"NODE_TITLE_TEXT_Y",20);h(this,"NODE_SLOT_HEIGHT",20);h(this,"NODE_WIDGET_HEIGHT",20);h(this,"NODE_WIDTH",140);h(this,"NODE_MIN_WIDTH",50);h(this,"NODE_COLLAPSED_RADIUS",10);h(this,"NODE_COLLAPSED_WIDTH",80);h(this,"NODE_TITLE_COLOR","#999");h(this,"NODE_SELECTED_TITLE_COLOR","#FFF");h(this,"NODE_TEXT_SIZE",14);h(this,"NODE_TEXT_COLOR","#AAA");h(this,"NODE_TEXT_HIGHLIGHT_COLOR","#EEE");h(this,"NODE_SUBTEXT_SIZE",12);h(this,"NODE_DEFAULT_COLOR","#333");h(this,"NODE_DEFAULT_BGCOLOR","#353535");h(this,"NODE_DEFAULT_BOXCOLOR","#666");h(this,"NODE_DEFAULT_SHAPE",RenderShape.ROUND);h(this,"NODE_BOX_OUTLINE_COLOR","#FFF");h(this,"NODE_ERROR_COLOUR","#E00");h(this,"DEFAULT_SHADOW_COLOR","rgba(0,0,0,0.5)");h(this,"DEFAULT_GROUP_FONT",24);h(this,"DEFAULT_GROUP_FONT_SIZE");h(this,"WIDGET_BGCOLOR","#222");h(this,"WIDGET_OUTLINE_COLOR","#666");h(this,"WIDGET_ADVANCED_OUTLINE_COLOR","rgba(56, 139, 253, 0.8)");h(this,"WIDGET_TEXT_COLOR","#DDD");h(this,"WIDGET_SECONDARY_TEXT_COLOR","#999");h(this,"LINK_COLOR","#9A9");h(this,"EVENT_LINK_COLOR","#A86");h(this,"CONNECTING_LINK_COLOR","#AFA");h(this,"MAX_NUMBER_OF_NODES",1e4);h(this,"DEFAULT_POSITION",[100,100]);h(this,"VALID_SHAPES",["default","box","round","card"]);h(this,"ROUND_RADIUS",8);h(this,"BOX_SHAPE",RenderShape.BOX);h(this,"ROUND_SHAPE",RenderShape.ROUND);h(this,"CIRCLE_SHAPE",RenderShape.CIRCLE);h(this,"CARD_SHAPE",RenderShape.CARD);h(this,"ARROW_SHAPE",RenderShape.ARROW);h(this,"GRID_SHAPE",RenderShape.GRID);h(this,"INPUT",NodeSlotType.INPUT);h(this,"OUTPUT",NodeSlotType.OUTPUT);h(this,"EVENT",-1);h(this,"ACTION",-1);h(this,"NODE_MODES",["Always","On Event","Never","On Trigger"]);h(this,"NODE_MODES_COLORS",["#666","#422","#333","#224","#626"]);h(this,"ALWAYS",LGraphEventMode.ALWAYS);h(this,"ON_EVENT",LGraphEventMode.ON_EVENT);h(this,"NEVER",LGraphEventMode.NEVER);h(this,"ON_TRIGGER",LGraphEventMode.ON_TRIGGER);h(this,"UP",LinkDirection.UP);h(this,"DOWN",LinkDirection.DOWN);h(this,"LEFT",LinkDirection.LEFT);h(this,"RIGHT",LinkDirection.RIGHT);h(this,"CENTER",LinkDirection.CENTER);h(this,"LINK_RENDER_MODES",["Straight","Linear","Spline"]);h(this,"HIDDEN_LINK",LinkRenderType.HIDDEN_LINK);h(this,"STRAIGHT_LINK",LinkRenderType.STRAIGHT_LINK);h(this,"LINEAR_LINK",LinkRenderType.LINEAR_LINK);h(this,"SPLINE_LINK",LinkRenderType.SPLINE_LINK);h(this,"NORMAL_TITLE",TitleMode.NORMAL_TITLE);h(this,"NO_TITLE",TitleMode.NO_TITLE);h(this,"TRANSPARENT_TITLE",TitleMode.TRANSPARENT_TITLE);h(this,"AUTOHIDE_TITLE",TitleMode.AUTOHIDE_TITLE);h(this,"VERTICAL_LAYOUT","vertical");h(this,"proxy",null);h(this,"node_images_path","");h(this,"debug",!1);h(this,"catch_exceptions",!0);h(this,"throw_errors",!0);h(this,"allow_scripts",!1);h(this,"registered_node_types",{});h(this,"node_types_by_file_extension",{});h(this,"Nodes",{});h(this,"Globals",{});h(this,"searchbox_extras",{});h(this,"auto_sort_node_types",!1);h(this,"node_box_coloured_when_on",!1);h(this,"node_box_coloured_by_mode",!1);h(this,"dialog_close_on_mouse_leave",!1);h(this,"dialog_close_on_mouse_leave_delay",500);h(this,"shift_click_do_break_link_from",!1);h(this,"click_do_break_link_to",!1);h(this,"ctrl_alt_click_do_break_link",!0);h(this,"snaps_for_comfy",!0);h(this,"snap_highlights_node",!0);h(this,"alwaysSnapToGrid");h(this,"snapToGrid");h(this,"search_hide_on_mouse_leave",!0);h(this,"search_filter_enabled",!1);h(this,"search_show_all_on_open",!0);h(this,"auto_load_slot_types",!1);h(this,"registered_slot_in_types",{});h(this,"registered_slot_out_types",{});h(this,"slot_types_in",[]);h(this,"slot_types_out",[]);h(this,"slot_types_default_in",{});h(this,"slot_types_default_out",{});h(this,"alt_drag_do_clone_nodes",!1);h(this,"do_add_triggers_slots",!1);h(this,"allow_multi_output_for_events",!0);h(this,"middle_click_slot_add_default_node",!1);h(this,"release_link_on_empty_shows_menu",!1);h(this,"pointerevents_method","pointer");h(this,"ctrl_shift_v_paste_connect_unselected_outputs",!0);h(this,"use_uuids",!1);h(this,"highlight_selected_group",!0);h(this,"use_legacy_node_error_indicator",!1);h(this,"context_menu_scaling",!1);h(this,"LGraph",LGraph);h(this,"LLink",LLink);h(this,"LGraphNode",LGraphNode);h(this,"LGraphGroup",LGraphGroup);h(this,"DragAndScale",DragAndScale);h(this,"LGraphCanvas",LGraphCanvas);h(this,"ContextMenu",ContextMenu);h(this,"CurveEditor",CurveEditor);h(this,"Reroute",Reroute);h(this,"getTime");h(this,"distance",distance);h(this,"isInsideRectangle",isInsideRectangle);h(this,"overlapBounding",overlapBounding);typeof performance<"u"?this.getTime=performance.now.bind(performance):typeof Date<"u"&&Date.now?this.getTime=Date.now.bind(Date):typeof process<"u"?this.getTime=function(){const t=process.hrtime();return t[0]*.001+t[1]*1e-6}:this.getTime=function(){return new Date().getTime()}}registerNodeType(t,i){var a,l,u;if(!i.prototype)throw"Cannot register a simple object, it must be a class with a prototype";i.type=t,this.debug&&console.log("Node registered: "+t);const n=i.name,o=t.lastIndexOf("/");i.category=t.substring(0,o),i.title||(i.title=n);for(const p in LGraphNode.prototype)(a=i.prototype)[p]||(a[p]=LGraphNode.prototype[p]);const r=this.registered_node_types[t];r&&console.log("replacing node type: "+t),this.registered_node_types[t]=i,i.constructor.name&&(this.Nodes[n]=i),(l=this.onNodeTypeRegistered)==null||l.call(this,t,i),r&&((u=this.onNodeTypeReplaced)==null||u.call(this,t,i,r)),i.prototype.onPropertyChange&&console.warn(`LiteGraph node class ${t} has onPropertyChange method, it must be called onPropertyChanged with d at the end`),this.auto_load_slot_types&&new i(i.title||"tmpnode")}unregisterNodeType(t){const i=typeof t=="string"?this.registered_node_types[t]:t;if(!i)throw"node type not found: "+t;delete this.registered_node_types[i.type];const n=i.constructor.name;n&&delete this.Nodes[n]}registerNodeAndSlotType(t,i,n){n||(n=!1);const r=(typeof t=="string"&&this.registered_node_types[t]!=="anonymous"?this.registered_node_types[t]:t).constructor.type;let a=[];typeof i=="string"?a=i.split(","):i==this.EVENT||i==this.ACTION?a=["_event_"]:a=["*"];for(let l=0;l<a.length;++l){let u=a[l];u===""&&(u="*");const p=n?"registered_slot_out_types":"registered_slot_in_types";this[p][u]===void 0&&(this[p][u]={nodes:[]}),this[p][u].nodes.includes(r)||this[p][u].nodes.push(r);const d=n?this.slot_types_out:this.slot_types_in;d.includes(u.toLowerCase())||(d.push(u.toLowerCase()),d.sort())}}clearRegisteredTypes(){this.registered_node_types={},this.node_types_by_file_extension={},this.Nodes={},this.searchbox_extras={}}addNodeMethod(t,i){LGraphNode.prototype[t]=i;for(const n in this.registered_node_types){const o=this.registered_node_types[n];o.prototype[t]&&(o.prototype["_"+t]=o.prototype[t]),o.prototype[t]=i}}createNode(t,i,n){var a;const o=this.registered_node_types[t];if(!o)return this.debug&&console.log(`GraphNode type "${t}" not registered.`),null;i=i||o.title||t;let r=null;if(this.catch_exceptions)try{r=new o(i)}catch(l){return console.error(l),null}else r=new o(i);if(r.type=t,!r.title&&i&&(r.title=i),r.properties||(r.properties={}),r.properties_info||(r.properties_info=[]),r.flags||(r.flags={}),r.size||(r.size=r.computeSize()),r.pos||(r.pos=this.DEFAULT_POSITION.concat()),r.mode||(r.mode=LGraphEventMode.ALWAYS),n)for(const l in n)r[l]=n[l];return(a=r.onNodeCreated)==null||a.call(r),r}getNodeType(t){return this.registered_node_types[t]}getNodeTypesInCategory(t,i){const n=[];for(const o in this.registered_node_types){const r=this.registered_node_types[o];r.filter==i&&(t==""?r.category==null&&n.push(r):r.category==t&&n.push(r))}return this.auto_sort_node_types&&n.sort(function(o,r){return o.title.localeCompare(r.title)}),n}getNodeTypesCategories(t){const i={"":1};for(const o in this.registered_node_types){const r=this.registered_node_types[o];if(r.category&&!r.skip_list){if(r.filter!=t)continue;i[r.category]=1}}const n=[];for(const o in i)n.push(o);return this.auto_sort_node_types?n.sort():n}reloadNodes(t){const i=document.getElementsByTagName("script"),n=[];for(let r=0;r<i.length;r++)n.push(i[r]);const o=document.getElementsByTagName("head")[0];t=document.location.href+t;for(let r=0;r<n.length;r++){const a=n[r].src;if(!(!a||a.substr(0,t.length)!=t))try{this.debug&&console.log("Reloading: "+a);const l=document.createElement("script");l.type="text/javascript",l.src=a,o.appendChild(l),o.removeChild(n[r])}catch(l){if(this.throw_errors)throw l;this.debug&&console.log("Error while reloading "+a)}}this.debug&&console.log("Nodes reloaded")}cloneObject(t,i){if(t==null)return null;const n=JSON.parse(JSON.stringify(t));if(!i)return n;for(const o in n)i[o]=n[o];return i}uuidv4(){return("10000000-1000-4000-8000"+-1e11).replace(/[018]/g,t=>(t^Math.random()*16>>t/4).toString(16))}isValidConnection(t,i){if((t==""||t==="*")&&(t=0),(i==""||i==="*")&&(i=0),!t||!i||t==i||t==this.EVENT&&i==this.ACTION)return!0;if(t=String(t),i=String(i),t=t.toLowerCase(),i=i.toLowerCase(),t.indexOf(",")==-1&&i.indexOf(",")==-1)return t==i;const n=t.split(","),o=i.split(",");for(let r=0;r<n.length;++r)for(let a=0;a<o.length;++a)if(this.isValidConnection(n[r],o[a]))return!0;return!1}registerSearchboxExtra(t,i,n){this.searchbox_extras[i.toLowerCase()]={type:t,desc:i,data:n}}fetchFile(t,i,n,o){if(!t)return null;if(i=i||"text",typeof t=="string")return t.substr(0,4)=="http"&&this.proxy&&(t=this.proxy+t.substr(t.indexOf(":")+3)),fetch(t).then(function(r){if(!r.ok)throw new Error("File not found");if(i=="arraybuffer")return r.arrayBuffer();if(i=="text"||i=="string")return r.text();if(i=="json")return r.json();if(i=="blob")return r.blob()}).then(function(r){n==null||n(r)}).catch(function(r){console.error("error fetching file:",t),o==null||o(r)});if(t instanceof File||t instanceof Blob){const r=new FileReader;if(r.onload=function(a){let l=a.target.result;i=="json"&&(l=JSON.parse(l)),n==null||n(l)},i=="arraybuffer")return r.readAsArrayBuffer(t);if(i=="text"||i=="json")return r.readAsText(t);if(i=="blob")return r.readAsBinaryString(t)}return null}getParameterNames(t){return(t+"").replace(/[/][/].*$/gm,"").replace(/\s+/g,"").replace(/[/][*][^/*]*[*][/]/g,"").split("){",1)[0].replace(/^[^(]*[(]/,"").replace(/=[^,]+/g,"").split(",").filter(Boolean)}pointerListenerAdd(t,i,n,o=!1){if(!t||!t.addEventListener||!i||typeof n!="function")return;let r=this.pointerevents_method,a=i;if(r=="pointer"&&!window.PointerEvent)switch(console.warn("sMethod=='pointer' && !window.PointerEvent"),console.log("Converting pointer["+a+"] : down move up cancel enter TO touchstart touchmove touchend, etc .."),a){case"down":{r="touch",a="start";break}case"move":{r="touch";break}case"up":{r="touch",a="end";break}case"cancel":{r="touch";break}case"enter":{console.log("debug: Should I send a move event?");break}default:console.warn("PointerEvent not available in this browser ? The event "+a+" would not be called")}switch(a){case"down":case"up":case"move":case"over":case"out":case"enter":t.addEventListener(r+a,n,o);case"leave":case"cancel":case"gotpointercapture":case"lostpointercapture":if(r!="mouse")return t.addEventListener(r+a,n,o);default:return t.addEventListener(a,n,o)}}pointerListenerRemove(t,i,n,o=!1){if(!(!t||!t.removeEventListener||!i||typeof n!="function"))switch(i){case"down":case"up":case"move":case"over":case"out":case"enter":(this.pointerevents_method=="pointer"||this.pointerevents_method=="mouse")&&t.removeEventListener(this.pointerevents_method+i,n,o);case"leave":case"cancel":case"gotpointercapture":case"lostpointercapture":if(this.pointerevents_method=="pointer")return t.removeEventListener(this.pointerevents_method+i,n,o);default:return t.removeEventListener(i,n,o)}}compareObjects(t,i){for(const n in t)if(t[n]!=i[n])return!1;return!0}colorToString(t){return"rgba("+Math.round(t[0]*255).toFixed()+","+Math.round(t[1]*255).toFixed()+","+Math.round(t[2]*255).toFixed()+","+(t.length==4?t[3].toFixed(2):"1.0")+")"}growBounding(t,i,n){i<t[0]?t[0]=i:i>t[2]&&(t[2]=i),n<t[1]?t[1]=n:n>t[3]&&(t[3]=n)}isInsideBounding(t,i){return!(t[0]<i[0][0]||t[1]<i[0][1]||t[0]>i[1][0]||t[1]>i[1][1])}hex2num(t){t.charAt(0)=="#"&&(t=t.slice(1)),t=t.toUpperCase();const i="0123456789ABCDEF",n=new Array(3);let o=0,r,a;for(let l=0;l<6;l+=2)r=i.indexOf(t.charAt(l)),a=i.indexOf(t.charAt(l+1)),n[o]=r*16+a,o++;return n}num2hex(t){const i="0123456789ABCDEF";let n="#",o,r;for(let a=0;a<3;a++)o=t[a]/16,r=t[a]%16,n+=i.charAt(o)+i.charAt(r);return n}closeAllContextMenus(t){t=t||window;const i=t.document.querySelectorAll(".litecontextmenu");if(!i.length)return;const n=[];for(let o=0;o<i.length;o++)n.push(i[o]);for(let o=0;o<n.length;o++)n[o].close?n[o].close():n[o].parentNode&&n[o].parentNode.removeChild(n[o])}extendClass(t,i){for(const n in i)t.hasOwnProperty(n)||(t[n]=i[n]);if(i.prototype)for(const n in i.prototype)i.prototype.hasOwnProperty(n)&&(t.prototype.hasOwnProperty(n)||(i.prototype.__lookupGetter__(n)?t.prototype.__defineGetter__(n,i.prototype.__lookupGetter__(n)):t.prototype[n]=i.prototype[n],i.prototype.__lookupSetter__(n)&&t.prototype.__defineSetter__(n,i.prototype.__lookupSetter__(n))))}};let LiteGraphGlobal=ft;h(LiteGraphGlobal,"DEFAULT_EVENT_LINK_COLOR","#A86"),(()=>{LGraphCanvas.link_type_colors={"-1":ft.DEFAULT_EVENT_LINK_COLOR,number:"#AAA",node:"#DCA"}})();function loadPolyfills(){typeof window<"u"&&window.CanvasRenderingContext2D&&!window.CanvasRenderingContext2D.prototype.roundRect&&(window.CanvasRenderingContext2D.prototype.roundRect=function(s,t,i,n,o,r){let a=0,l=0,u=0,p=0;if(o===0){this.rect(s,t,i,n);return}if(r===void 0&&(r=o),o!=null&&o.constructor===Array)if(o.length==1)a=l=u=p=o[0];else if(o.length==2)a=p=o[0],l=u=o[1];else if(o.length==4)a=o[0],l=o[1],u=o[2],p=o[3];else return;else a=o||0,l=o||0,u=r||0,p=r||0;this.moveTo(s+a,t),this.lineTo(s+i-l,t),this.quadraticCurveTo(s+i,t,s+i,t+l),this.lineTo(s+i,t+n-p),this.quadraticCurveTo(s+i,t+n,s+i-p,t+n),this.lineTo(s+p,t+n),this.quadraticCurveTo(s,t+n,s,t+n-u),this.lineTo(s,t+u),this.quadraticCurveTo(s,t,s+a,t)}),typeof window<"u"&&!window.requestAnimationFrame&&(window.requestAnimationFrame=window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(s){window.setTimeout(s,1e3/60)})}const LiteGraph=new LiteGraphGlobal;function clamp(s,t,i){return t>s?t:i<s?i:s}loadPolyfills();class WorkflowEditorModel{constructor(){h(this,"state",ref({blocks:[],wires:[],blockTypes:[],name:"",description:"",workflowId:"",undoStack:[],redoStack:[],clipboard:null}));h(this,"skipSavingHistory",ref(!1));h(this,"viewState",computed(()=>({blocks:this.state.value.blocks,wires:this.state.value.wires,blockTypes:this.state.value.blockTypes,name:this.state.value.name,description:this.state.value.description,workflowId:this.state.value.workflowId,canUndo:this.state.value.undoStack.length>0,canRedo:this.state.value.redoStack.length>0,hasClipboard:this.state.value.clipboard!==null,skipSavingHistory:this.skipSavingHistory.value})));h(this,"intent",{initialize:t=>{this.performActionWithoutHistory(()=>{Object.assign(this.state.value,{blocks:t.blocks,wires:t.wires,blockTypes:t.blockTypes,name:t.name||"",description:t.description||"",workflowId:t.workflowId||""})})},updateBlocks:t=>{this.state.value.blocks=t},updateWires:t=>{this.state.value.wires=t},updateName:t=>{this.state.value.name=t},updateDescription:t=>{this.state.value.description=t},updateWorkflowId:t=>{this.state.value.workflowId=t},saveToHistory:()=>{this.skipSavingHistory.value||this.pushToUndoStack()},undo:()=>{if(this.state.value.undoStack.length===0)return;this.pushToRedoStack();const t=this.state.value.undoStack.pop();this.restoreState(t)},redo:()=>{if(this.state.value.redoStack.length===0)return;this.pushToUndoStack();const t=this.state.value.redoStack.pop();this.restoreState(t)},reset:()=>{this.intent.saveToHistory(),this.state.value.blocks=[],this.state.value.wires=[]}})}performActionWithoutHistory(t){this.skipSavingHistory.value=!0;try{t()}finally{this.skipSavingHistory.value=!1}}pushToUndoStack(){const t={blocks:[...this.state.value.blocks],wires:[...this.state.value.wires],name:this.state.value.name,description:this.state.value.description,workflowId:this.state.value.workflowId};this.state.value.undoStack.push(t),this.state.value.redoStack=[]}pushToRedoStack(){const t={blocks:[...this.state.value.blocks],wires:[...this.state.value.wires],name:this.state.value.name,description:this.state.value.description,workflowId:this.state.value.workflowId};this.state.value.redoStack.push(t)}restoreState(t){Object.assign(this.state.value,{blocks:t.blocks,wires:t.wires,name:t.name,description:t.description,workflowId:t.workflowId})}getViewState(){return this.viewState}getIntent(){return this.intent}}const workflowEditorModel=new WorkflowEditorModel,typeColors={},darkenColor=(s,t=.2)=>{let i=s.replace("#","");i.length===3&&(i=i[0]+i[0]+i[1]+i[1]+i[2]+i[2]);const n=parseInt(i.substring(0,2),16),o=parseInt(i.substring(2,4),16),r=parseInt(i.substring(4,6),16),a=Math.max(0,Math.round(n-n*t)),l=Math.max(0,Math.round(o-o*t)),u=Math.max(0,Math.round(r-r*t)),p=d=>{const c=d.toString(16);return c.length===1?"0"+c:c};return`#${p(a)}${p(l)}${p(u)}`},baseColors={},distinctColors=["#c74671","#73d64e","#713fc9","#d0d34a","#cb4ebf","#77db9e","#552d75","#678935","#7678d4","#c78b39","#5c769c","#d64e34","#86c9d8","#783928","#cfcf9d","#46283e","#589077","#cd9bc7","#3b492d","#bb8978"],generateColor=s=>{let t=0;for(let n=0;n<s.length;n++)t=s.charCodeAt(n)+((t<<5)-t);const i=Math.abs(t)%distinctColors.length;return distinctColors[i]},getTypeColor=(s,t=!0)=>{if(typeColors[s])return typeColors[s];let i=baseColors[s];i||(i=generateColor(s),baseColors[s]=i);const n=i;let o=darkenColor(i,.2);return t||(o=darkenColor(o,.2)),typeColors[s]={color_on:n,color_off:o},typeColors[s]},_hoisted_1$1={class:"workflow-canvas"},_hoisted_2={class:"toolbar"},_hoisted_3={key:0,class:"loading-overlay"},_sfc_main$1=defineComponent({__name:"WorkflowCanvas",props:{blocks:{},wires:{},blockTypes:{},initialName:{},initialDescription:{},initialWorkflowId:{},loading:{type:Boolean}},emits:["update:blocks","update:wires","save"],setup(s,{emit:t}){const i=s,n=t,o=ref({}),r=ref();let a=new LGraph,l=null;const u=workflowEditorModel.getIntent(),p=workflowEditorModel.getViewState(),d=ref(!1),c=useMessage(),_=useLoadingBar(),g=ref(),f=ref({workflowId:"",name:"",description:""});p.value.workflowId="user:"+Array.from({length:5},()=>Math.floor(Math.random()*36).toString(36)).join(""),p.value.name=f.value.name,p.value.description=f.value.description;const m={workflowId:{required:!0,trigger:["blur","input"],validator:(G,I)=>I?/^[^:]+:[^:]+$/.test(I)?!0:new Error("工作流ID必须是 group_id:workflow_id 的格式"):new Error("工作流ID不能为空")},name:{required:!0,trigger:["blur","input"],message:"工作流名称不能为空"}},w=ref(!1),b=ref(!1),k=ref(!1),L=()=>{if(!r.value){console.warn("[initGraph] Canvas container is not available.");return}const G=r.value;a=new LGraph,l=new LGraphCanvas(G,a),l.allow_dragcanvas=!0,l.allow_dragnodes=!0,l.allow_interaction=!0,l.connections_width=2,l.align_to_grid=!0,l.highquality_render=!0,LiteGraph.NODE_TEXT_SIZE=14,LiteGraph.NODE_SUBTEXT_SIZE=12,LiteGraph.NODE_COLLAPSED_RADIUS=8,LiteGraph.DEFAULT_GROUP_FONT=14,LiteGraph.NODE_TITLE_HEIGHT=38,LiteGraph.NODE_SLOT_HEIGHT=24,LiteGraph.NODE_WIDTH=220,LiteGraph.NODE_MIN_WIDTH=150,LiteGraph.NODE_COLLAPSED_WIDTH=100,LiteGraph.CANVAS_GRID_SIZE=20,LiteGraph.auto_sort_node_types=!0,LiteGraph.use_uuids=!0,LiteGraph.isValidConnection=(I,B)=>(console.log("[isValidConnection] Checking connection between",I,"and",B),!!(I==B||o.value[I]&&o.value[I][B])),N(),T(),S(),window.addEventListener("resize",T),C(),console.log("[initGraph] LiteGraph initialized.")},T=()=>{var V;if(!r.value||!l){console.warn("[updateCanvasSize] Canvas or container is not available.");return}const G=r.value,I=window.devicePixelRatio,B=G.parentElement.getBoundingClientRect(),{width:W,height:z}=B;G.width=W*I,G.height=z*I,G.style.width=W+"px",G.style.height=z+"px",l.resize(G.width,G.height),(V=G.getContext("2d"))==null||V.scale(I,I)},A=()=>{if(!l)return;const G=i.blockTypes.flatMap(I=>[...I.inputs.map(B=>B.type),...I.outputs.map(B=>B.type)]).reduce((I,B)=>(I[B]=getTypeColor(B).color_on,I),{});l.default_connection_color_byType=G,LGraphCanvas.link_type_colors=G,console.log("[updateLinkColors] Link colors updated.")},E=()=>{if(!i.blockTypes||i.blockTypes.length===0){console.warn("[registerNodeTypes] No block types to register.");return}i.blockTypes.forEach(G=>{class I extends LiteGraph.LGraphNode{constructor(){super(G.label),G.inputs.forEach(z=>{const V=getTypeColor(z.type,z.required);this.addInput(z.name,z.type,{label:z.label,color_off:V.color_off,color_on:V.color_on})}),G.outputs.forEach(z=>{const V=getTypeColor(z.type);this.addOutput(z.name,z.type,{label:z.label,color_off:V.color_off,color_on:V.color_on})});const W=()=>{var z;this.properties.config||(this.properties.config={}),(z=this.widgets)==null||z.forEach(V=>{V.name&&V.actual_name&&(this.properties.config[V.actual_name]=V.value,D())})};G.configs.forEach(z=>{let V;z.type==="int"?V=this.addWidget("number",z.label,z.default||0,W):z.type==="str"?z.has_options?(console.log(z.options),V=this.addWidget("combo",z.label,z.default||"",W,{values:z.options})):V=this.addWidget("text",z.label,z.default||"",W,{multiline:!0}):z.type==="bool"?V=this.addWidget("toggle",z.label,z.default||!1,W):console.warn(`[registerNodeTypes] Unsupported config type: ${z.type}`),V&&(V.actual_name=z.name)}),W(),this.size=this.computeSize()}onEvent(W,z){console.log(W,z)}}Object.defineProperty(I,"name",{value:G.label}),LiteGraph.registerNodeType(G.type_name.replace(":","/"),I),console.log(`[registerNodeTypes] Registered node type: ${G.type_name}`)}),console.log("[registerNodeTypes] Node types registered.")},C=()=>{l&&(l.getExtraMenuOptions=()=>[{content:"保存工作流",callback:()=>F()},{content:"重置工作流",callback:()=>U()},{content:"导入工作流",callback:()=>Le()},{content:"编辑工作流信息",callback:()=>Te()}])},N=()=>{if(!l)return;const G=r.value;G.tabIndex=0,G.focus(),document.addEventListener("keydown",I=>{document.activeElement===r.value&&((I.ctrlKey||I.metaKey)&&I.key.toLowerCase()==="z"?(I.preventDefault(),I.shiftKey?ke():$()):(I.ctrlKey||I.metaKey)&&I.key.toLowerCase()==="s"&&(I.preventDefault(),F()))})},S=()=>{if(!a){console.warn("[setupEventListeners] Graph is not initialized.");return}a.onNodeAdded=G=>{D(),u.saveToHistory()},a.onNodeRemoved=G=>{D(),u.saveToHistory()}},D=()=>{if(!a){console.warn("[updateBlocks] Graph is not initialized.");return}const G=Array.from(a._nodes.values()).map(I=>({type_name:I.type.replace("/",":"),name:I.id.toString(),config:I.properties.config||{},position:{x:Math.round(I.pos[0]),y:Math.round(I.pos[1])}}));u.updateBlocks(G),n("update:blocks",G)},P=()=>{if(!a){console.warn("[updateWires] Graph is not initialized.");return}const G=Array.from(a._links.values()).map(I=>{try{const B=a.getNodeById(I.origin_id),W=a.getNodeById(I.target_id);return!(B!=null&&B.id)||!(W!=null&&W.id)?null:{source_block:B.id.toString(),source_output:B==null?void 0:B.outputs[I.origin_slot].name,target_block:W.id.toString(),target_input:W==null?void 0:W.inputs[I.target_slot].name}}catch(B){return console.warn("[updateWires] Error getting node:",B),null}}).filter(I=>I!==null);u.updateWires(G),n("update:wires",G)},F=async()=>{var G;try{D();const I=await((G=g.value)==null?void 0:G.validate());if((I==null?void 0:I.length)>0||!f.value.name||!f.value.workflowId){c.error("工作流信息需要修改"),d.value=!0;return}w.value=!0,_.start(),d.value=!1,P(),n("save",f.value.name,f.value.description,f.value.workflowId)}catch(I){c.error((I==null?void 0:I.message)||"保存失败")}finally{w.value=!1,_.finish()}},U=()=>{k.value=!0,_.start(),window.location.reload()},H=G=>{var B;if(!a)return null;const I=LiteGraph.createNode(G.type_name.replace(":","/"));if(!I)return console.warn(`[createNode] Could not create node of type ${G.type_name}`),null;if(I.id=G.name,I.pos=[G.position.x,G.position.y],a.add(I),G.config){I.properties.config=G.config;for(const W in G.config)I.properties[W]=G.config[W];(B=I.widgets)==null||B.forEach(W=>{W.name&&W.actual_name&&(W.value=G.config[W.actual_name]||W.value)})}return I},X=G=>{if(!a)return;const I=a.getNodeById(G.source_block),B=a.getNodeById(G.target_block);I&&B&&I.connect(G.source_output,B,G.target_input)},K=()=>{a&&(a.clear(),p.value.blocks.forEach(G=>{H(G)}),p.value.wires.forEach(G=>{X(G)}))},$=()=>{!p.value.canUndo||!a||(u.undo(),workflowEditorModel.performActionWithoutHistory(()=>{K()}))},ke=()=>{!p.value.canRedo||!a||(u.redo(),workflowEditorModel.performActionWithoutHistory(()=>{K()}))},Le=async()=>{try{b.value=!0,_.start();const G=document.createElement("input");G.type="file",G.accept=".json",G.onchange=async I=>{var W;const B=(W=I.target.files)==null?void 0:W[0];if(B&&a){const z=new FileReader;z.onload=V=>{var gt;try{const Ce=JSON.parse((gt=V.target)==null?void 0:gt.result);Ce.blocks&&Ce.wires&&a&&(u.saveToHistory(),workflowEditorModel.performActionWithoutHistory(()=>{a.clear(),Ce.blocks.forEach(ht=>{H(ht)}),Ce.wires.forEach(ht=>{X(ht)})}),D(),P(),c.success("导入成功"))}catch{c.error("导入失败：文件格式错误")}},z.readAsText(B)}},G.click()}finally{b.value=!1,_.finish()}},Te=()=>{f.value={workflowId:p.value.workflowId||"",name:p.value.name||"",description:p.value.description||""},d.value=!0};let ge=!1,q=!1;const We=async()=>{if(!a){console.warn("[initGraphData] Graph is not initialized.");return}const G=await getTypeCompatibility();o.value=G,console.log("[initGraphData] Type compatibility:",o.value),i.blockTypes.length>0&&!ge&&(a.clear(),E(),A(),ge=!0,console.log("[initGraphData] Node types registered.")),ge&&(i.blocks.length>0||i.wires.length>0)&&!q&&(q=!0,u.initialize({blocks:i.blocks,wires:i.wires,blockTypes:i.blockTypes,name:i.initialName,description:i.initialDescription,workflowId:i.initialWorkflowId}),workflowEditorModel.performActionWithoutHistory(()=>{K(),Array.from(a._nodes.values()).some(B=>B.pos[0]===0&&B.pos[1]===0)&&(console.log("节点坐标为0，重新排列"),a.arrange()),console.log("[initGraphData] Graph initialized.")}))},St=()=>{p.value.name=i.initialName||"",p.value.description=i.initialDescription||"",p.value.workflowId=i.initialWorkflowId||"",f.value={workflowId:i.initialWorkflowId||":",name:i.initialName||"",description:i.initialDescription||""},f.value.workflowId==":"&&(f.value.workflowId="user:"+Array.from({length:5},()=>Math.floor(Math.random()*36).toString(36)).join(""))};watch([()=>i.blocks,()=>i.wires,()=>i.blockTypes],We,{deep:!0}),watch([()=>i.initialName,()=>i.initialDescription,()=>i.initialWorkflowId],St,{deep:!0}),onMounted(()=>{L(),We(),window.addEventListener("beforeunload",_t)});const _t=G=>(G.preventDefault(),G.returnValue="您确定要离开此页面吗？未保存的更改可能会丢失。",G.returnValue);return onBeforeUnmount(()=>{window.removeEventListener("beforeunload",_t)}),(G,I)=>(openBlock(),createElementBlock("div",_hoisted_1$1,[createBaseVNode("div",_hoisted_2,[createVNode(unref(NSpace),null,{default:withCtx(()=>[createVNode(unref(NTooltip),{placement:"bottom",trigger:"hover"},{trigger:withCtx(()=>[createVNode(unref(NButton),{quaternary:"",circle:"",loading:w.value,onClick:F,class:"toolbar-button"},{icon:withCtx(()=>[createVNode(unref(NIcon),null,{default:withCtx(()=>[createVNode(unref(SaveOutline))]),_:1})]),_:1},8,["loading"])]),default:withCtx(()=>[I[5]||(I[5]=createBaseVNode("span",null,"保存工作流",-1))]),_:1}),createVNode(unref(NTooltip),{placement:"bottom",trigger:"hover"},{trigger:withCtx(()=>[createVNode(unref(NButton),{quaternary:"",circle:"",loading:k.value,onClick:U,class:"toolbar-button"},{icon:withCtx(()=>[createVNode(unref(NIcon),null,{default:withCtx(()=>[createVNode(unref(RefreshOutline))]),_:1})]),_:1},8,["loading"])]),default:withCtx(()=>[I[6]||(I[6]=createBaseVNode("span",null,"重置工作流",-1))]),_:1}),createVNode(unref(NTooltip),{placement:"bottom",trigger:"hover"},{trigger:withCtx(()=>[createVNode(unref(NButton),{quaternary:"",circle:"",loading:b.value,onClick:Le,class:"toolbar-button"},{icon:withCtx(()=>[createVNode(unref(NIcon),null,{default:withCtx(()=>[createVNode(unref(DownloadOutline))]),_:1})]),_:1},8,["loading"])]),default:withCtx(()=>[I[7]||(I[7]=createBaseVNode("span",null,"导入工作流",-1))]),_:1}),createVNode(unref(NTooltip),{placement:"bottom",trigger:"hover"},{trigger:withCtx(()=>[createVNode(unref(NButton),{quaternary:"",circle:"",onClick:Te,class:"toolbar-button"},{icon:withCtx(()=>[createVNode(unref(NIcon),null,{default:withCtx(()=>[createVNode(unref(SettingsOutline))]),_:1})]),_:1})]),default:withCtx(()=>[I[8]||(I[8]=createBaseVNode("span",null,"编辑工作流信息",-1))]),_:1})]),_:1})]),createBaseVNode("canvas",{ref_key:"container",ref:r,class:"workflow-canvas",tabindex:"0"},null,512),createVNode(unref(NModal),{show:d.value,"onUpdate:show":I[4]||(I[4]=B=>d.value=B),preset:"card",title:"工作流设置",class:"settings-modal",style:{width:"600px"}},{footer:withCtx(()=>[createVNode(unref(NSpace),{justify:"end"},{default:withCtx(()=>[createVNode(unref(NButton),{onClick:I[3]||(I[3]=B=>d.value=!1)},{default:withCtx(()=>I[9]||(I[9]=[createTextVNode(" 取消 ")])),_:1}),createVNode(unref(NButton),{type:"primary",loading:w.value,onClick:F},{default:withCtx(()=>I[10]||(I[10]=[createTextVNode(" 保存 ")])),_:1},8,["loading"])]),_:1})]),default:withCtx(()=>[createVNode(unref(NForm),{ref_key:"formRef",ref:g,model:f.value,rules:m,"label-placement":"left","label-width":"100","require-mark-placement":"right-hanging",size:"medium",class:"settings-form"},{default:withCtx(()=>[createVNode(unref(NFormItem),{label:"工作流ID",path:"workflowId"},{default:withCtx(()=>[createVNode(unref(NInput),{value:f.value.workflowId,"onUpdate:value":I[0]||(I[0]=B=>f.value.workflowId=B),placeholder:"请输入 group_id:workflow_id"},null,8,["value"])]),_:1}),createVNode(unref(NFormItem),{label:"名称",path:"name"},{default:withCtx(()=>[createVNode(unref(NInput),{value:f.value.name,"onUpdate:value":I[1]||(I[1]=B=>f.value.name=B),placeholder:"请输入工作流名称"},null,8,["value"])]),_:1}),createVNode(unref(NFormItem),{label:"描述",path:"description"},{default:withCtx(()=>[createVNode(unref(NInput),{value:f.value.description,"onUpdate:value":I[2]||(I[2]=B=>f.value.description=B),type:"textarea",placeholder:"请输入工作流描述"},null,8,["value"])]),_:1})]),_:1},8,["model"])]),_:1},8,["show"]),i.loading?(openBlock(),createElementBlock("div",_hoisted_3,[createVNode(unref(NSpin),{size:"large"})])):createCommentVNode("",!0)]))}}),WorkflowCanvas_vue_vue_type_style_index_0_lang="",_hoisted_1={class:"workflow-editor"},_sfc_main=defineComponent({__name:"WorkflowEditor",setup(s){const t=useRoute(),i=useRouter(),n=useMessage(),o=ref(""),r=ref(""),a=ref(""),l=ref(""),u=ref([]),p=ref([]),d=ref([]),c=ref(!1),_=ref(!1),g=ref(null),f=async(L,T,A)=>{const[E,C]=A.split(":"),N={group_id:E,workflow_id:C,name:L,description:T,blocks:u.value,wires:p.value};_.value=!0;try{t.params.id?(await updateWorkflow(r.value,o.value,N),(r.value!==E||o.value!==C)&&(r.value=E,o.value=C,i.push(`/workflow/editor/${N.group_id}:${N.workflow_id}`)),a.value=N.name,l.value=N.description,u.value=N.blocks,p.value=N.wires,document.title=`工作流 - ${N.name}`,n.success("保存成功")):(await createWorkflow(N.group_id,N.workflow_id,N),r.value=N.group_id,o.value=N.workflow_id,l.value=N.description,a.value=N.name,document.title=`工作流 - ${N.name}`,n.success("创建成功"),i.push(`/workflow/editor/${N.group_id}:${N.workflow_id}`))}catch(S){n.error("保存失败"),S.value=S.message||"保存失败"}finally{_.value=!1}},m=async()=>{if(!t.params.id)return;const[L,T]=t.params.id.split(":");r.value=L,o.value=T,c.value=!0,g.value=null;try{const{workflow:A}=await getWorkflow(L,T);a.value=A.name,l.value=A.description,u.value=A.blocks,p.value=A.wires,document.title=`工作流 - ${A.name}`}catch(A){n.error("获取工作流失败"),A.value=A.message||"获取工作流失败"}finally{c.value=!1}},w=async()=>{try{const{types:L}=await listBlockTypes();d.value=L}catch{n.error("获取区块类型失败")}},b=L=>{u.value=L},k=L=>{p.value=L};return onMounted(()=>{m(),w()}),(L,T)=>(openBlock(),createElementBlock("div",_hoisted_1,[createVNode(_sfc_main$1,{blocks:u.value,wires:p.value,"block-types":d.value,"initial-name":a.value,"initial-description":l.value,"initial-workflow-id":r.value+":"+o.value,loading:_.value,"onUpdate:blocks":b,"onUpdate:wires":k,onSave:f},null,8,["blocks","wires","block-types","initial-name","initial-description","initial-workflow-id","loading"])]))}}),WorkflowEditor_vue_vue_type_style_index_0_scoped_4781dc9b_lang="",WorkflowEditor=_export_sfc(_sfc_main,[["__scopeId","data-v-4781dc9b"]]);export{WorkflowEditor as default};
