function l(t){return(a={})=>{const e=a.width?String(a.width):t.defaultWidth;return t.formats[e]||t.formats[t.defaultWidth]}}function d(t){return(a,e)=>{const n=e!=null&&e.context?String(e.context):"standalone";let i;if(n==="formatting"&&t.formattingValues){const r=t.defaultFormattingWidth||t.defaultWidth,o=e!=null&&e.width?String(e.width):r;i=t.formattingValues[o]||t.formattingValues[r]}else{const r=t.defaultWidth,o=e!=null&&e.width?String(e.width):t.defaultWidth;i=t.values[o]||t.values[r]}const u=t.argumentCallback?t.argumentCallback(a):a;return i[u]}}function h(t){return(a,e={})=>{const n=e.width,i=n&&t.matchPatterns[n]||t.matchPatterns[t.defaultMatchWidth],u=a.match(i);if(!u)return null;const r=u[0],o=n&&t.parsePatterns[n]||t.parsePatterns[t.defaultParseWidth],c=Array.isArray(o)?b(o,m=>m.test(r)):y(o,m=>m.test(r));let s;s=t.valueCallback?t.valueCallback(c):c,s=e.valueCallback?e.valueCallback(s):s;const f=a.slice(r.length);return{value:s,rest:f}}}function y(t,a){for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e)&&a(t[e]))return e}function b(t,a){for(let e=0;e<t.length;e++)if(a(t[e]))return e}function g(t){return(a,e={})=>{const n=a.match(t.matchPattern);if(!n)return null;const i=n[0],u=a.match(t.parsePattern);if(!u)return null;let r=t.valueCallback?t.valueCallback(u[0]):u[0];r=e.valueCallback?e.valueCallback(r):r;const o=a.slice(i.length);return{value:r,rest:o}}}const w={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},v=(t,a,e)=>{let n;const i=w[t];return typeof i=="string"?n=i:a===1?n=i.one:n=i.other.replace("{{count}}",a.toString()),e!=null&&e.addSuffix?e.comparison&&e.comparison>0?"in "+n:n+" ago":n},P={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},M=(t,a,e,n)=>P[t],W={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},p={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},k={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},S={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},F={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},C={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},D=(t,a)=>{const e=Number(t),n=e%100;if(n>20||n<10)switch(n%10){case 1:return e+"st";case 2:return e+"nd";case 3:return e+"rd"}return e+"th"},x={ordinalNumber:D,era:d({values:W,defaultWidth:"wide"}),quarter:d({values:p,defaultWidth:"wide",argumentCallback:t=>t-1}),month:d({values:k,defaultWidth:"wide"}),day:d({values:S,defaultWidth:"wide"}),dayPeriod:d({values:F,defaultWidth:"wide",formattingValues:C,defaultFormattingWidth:"wide"})},A=/^(\d+)(th|st|nd|rd)?/i,j=/\d+/i,T={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},V={any:[/^b/i,/^(a|c)/i]},q={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},J={any:[/1/i,/2/i,/3/i,/4/i]},z={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},N={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},O={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},X={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},E={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},L={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},Q={ordinalNumber:g({matchPattern:A,parsePattern:j,valueCallback:t=>parseInt(t,10)}),era:h({matchPatterns:T,defaultMatchWidth:"wide",parsePatterns:V,defaultParseWidth:"any"}),quarter:h({matchPatterns:q,defaultMatchWidth:"wide",parsePatterns:J,defaultParseWidth:"any",valueCallback:t=>t+1}),month:h({matchPatterns:z,defaultMatchWidth:"wide",parsePatterns:N,defaultParseWidth:"any"}),day:h({matchPatterns:O,defaultMatchWidth:"wide",parsePatterns:X,defaultParseWidth:"any"}),dayPeriod:h({matchPatterns:E,defaultMatchWidth:"any",parsePatterns:L,defaultParseWidth:"any"})},R={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},Y={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},_={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},B={date:l({formats:R,defaultWidth:"full"}),time:l({formats:Y,defaultWidth:"full"}),dateTime:l({formats:_,defaultWidth:"full"})},H={code:"en-US",formatDistance:v,formatLong:B,formatRelative:M,localize:x,match:Q,options:{weekStartsOn:0,firstWeekContainsDate:1}};export{H as e};
