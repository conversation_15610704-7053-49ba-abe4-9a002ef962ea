import{d as $,O as n,a7 as ze,P as Re,a9 as wt,Z as xt,I as F,$ as Ct,L as ce,V as q,b as k,w as Mt,X as de,Y as Ie,bQ as Ft,bs as se,aj as P,v as ue,ae as kt,r as z,W as St,af as yt,bR as Pt,ag as Le,ah as Bt,ai as fe,J,R,a1 as zt,aS as he,ay as le,U as Rt,aa as It,F as ve,ac as U,bq as Lt,at as C}from"./index.js";import{u as jt}from"./use-locale.js";import{i as $t,N as ge}from"./Input.js";import{i as Tt,a as Ot,c as Nt,m as be,s as At,N as _t}from"./Select.js";function we(e){switch(e){case"tiny":return"mini";case"small":return"tiny";case"medium":return"small";case"large":return"medium";case"huge":return"large"}throw new Error(`${e} has no smaller size.`)}const xe=$({name:"Backward",render(){return n("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},n("path",{d:"M12.2674 15.793C11.9675 16.0787 11.4927 16.0672 11.2071 15.7673L6.20572 10.5168C5.9298 10.2271 5.9298 9.7719 6.20572 9.48223L11.2071 4.23177C11.4927 3.93184 11.9675 3.92031 12.2674 4.206C12.5673 4.49169 12.5789 4.96642 12.2932 5.26634L7.78458 9.99952L12.2932 14.7327C12.5789 15.0326 12.5673 15.5074 12.2674 15.793Z",fill:"currentColor"}))}}),Ce=$({name:"FastBackward",render(){return n("svg",{viewBox:"0 0 20 20",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},n("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},n("g",{fill:"currentColor","fill-rule":"nonzero"},n("path",{d:"M8.73171,16.7949 C9.03264,17.0795 9.50733,17.0663 9.79196,16.7654 C10.0766,16.4644 10.0634,15.9897 9.76243,15.7051 L4.52339,10.75 L17.2471,10.75 C17.6613,10.75 17.9971,10.4142 17.9971,10 C17.9971,9.58579 17.6613,9.25 17.2471,9.25 L4.52112,9.25 L9.76243,4.29275 C10.0634,4.00812 10.0766,3.53343 9.79196,3.2325 C9.50733,2.93156 9.03264,2.91834 8.73171,3.20297 L2.31449,9.27241 C2.14819,9.4297 2.04819,9.62981 2.01448,9.8386 C2.00308,9.89058 1.99707,9.94459 1.99707,10 C1.99707,10.0576 2.00356,10.1137 2.01585,10.1675 C2.05084,10.3733 2.15039,10.5702 2.31449,10.7254 L8.73171,16.7949 Z"}))))}}),Me=$({name:"FastForward",render(){return n("svg",{viewBox:"0 0 20 20",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},n("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},n("g",{fill:"currentColor","fill-rule":"nonzero"},n("path",{d:"M11.2654,3.20511 C10.9644,2.92049 10.4897,2.93371 10.2051,3.23464 C9.92049,3.53558 9.93371,4.01027 10.2346,4.29489 L15.4737,9.25 L2.75,9.25 C2.33579,9.25 2,9.58579 2,10.0000012 C2,10.4142 2.33579,10.75 2.75,10.75 L15.476,10.75 L10.2346,15.7073 C9.93371,15.9919 9.92049,16.4666 10.2051,16.7675 C10.4897,17.0684 10.9644,17.0817 11.2654,16.797 L17.6826,10.7276 C17.8489,10.5703 17.9489,10.3702 17.9826,10.1614 C17.994,10.1094 18,10.0554 18,10.0000012 C18,9.94241 17.9935,9.88633 17.9812,9.83246 C17.9462,9.62667 17.8467,9.42976 17.6826,9.27455 L11.2654,3.20511 Z"}))))}}),Fe=$({name:"Forward",render(){return n("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},n("path",{d:"M7.73271 4.20694C8.03263 3.92125 8.50737 3.93279 8.79306 4.23271L13.7944 9.48318C14.0703 9.77285 14.0703 10.2281 13.7944 10.5178L8.79306 15.7682C8.50737 16.0681 8.03263 16.0797 7.73271 15.794C7.43279 15.5083 7.42125 15.0336 7.70694 14.7336L12.2155 10.0005L7.70694 5.26729C7.42125 4.96737 7.43279 4.49264 7.73271 4.20694Z",fill:"currentColor"}))}}),ke=$({name:"More",render(){return n("svg",{viewBox:"0 0 16 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},n("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},n("g",{fill:"currentColor","fill-rule":"nonzero"},n("path",{d:"M4,7 C4.55228,7 5,7.44772 5,8 C5,8.55229 4.55228,9 4,9 C3.44772,9 3,8.55229 3,8 C3,7.44772 3.44772,7 4,7 Z M8,7 C8.55229,7 9,7.44772 9,8 C9,8.55229 8.55229,9 8,9 C7.44772,9 7,8.55229 7,8 C7,7.44772 7.44772,7 8,7 Z M12,7 C12.5523,7 13,7.44772 13,8 C13,8.55229 12.5523,9 12,9 C11.4477,9 11,8.55229 11,8 C11,7.44772 11.4477,7 12,7 Z"}))))}});function Ut(e){const{boxShadow2:a}=e;return{menuBoxShadow:a}}const Vt=ze({name:"Popselect",common:Re,peers:{Popover:wt,InternalSelectMenu:Tt},self:Ut}),me=Vt,je=xt("n-popselect"),Dt=F("popselect-menu",`
 box-shadow: var(--n-menu-box-shadow);
`),pe={multiple:Boolean,value:{type:[String,Number,Array],default:null},cancelable:Boolean,options:{type:Array,default:()=>[]},size:{type:String,default:"medium"},scrollable:Boolean,"onUpdate:value":[Function,Array],onUpdateValue:[Function,Array],onMouseenter:Function,onMouseleave:Function,renderLabel:Function,showCheckmark:{type:Boolean,default:void 0},nodeProps:Function,virtualScroll:Boolean,onChange:[Function,Array]},Se=kt(pe),Ht=$({name:"PopselectPanel",props:pe,setup(e){const a=Ct(je),{mergedClsPrefixRef:r,inlineThemeDisabled:s}=ce(e),g=q("Popselect","-pop-select",Dt,me,a.props,r),v=k(()=>Ft(e.options,Nt("value","children")));function w(i,f){const{onUpdateValue:h,"onUpdate:value":d,onChange:b}=e;h&&P(h,i,f),d&&P(d,i,f),b&&P(b,i,f)}function u(i){o(i.key)}function c(i){!se(i,"action")&&!se(i,"empty")&&!se(i,"header")&&i.preventDefault()}function o(i){const{value:{getNode:f}}=v;if(e.multiple)if(Array.isArray(e.value)){const h=[],d=[];let b=!0;e.value.forEach(T=>{if(T===i){b=!1;return}const O=f(T);O&&(h.push(O.key),d.push(O.rawNode))}),b&&(h.push(i),d.push(f(i).rawNode)),w(h,d)}else{const h=f(i);h&&w([i],[h.rawNode])}else if(e.value===i&&e.cancelable)w(null,null);else{const h=f(i);h&&w(i,h.rawNode);const{"onUpdate:show":d,onUpdateShow:b}=a.props;d&&P(d,!1),b&&P(b,!1),a.setShow(!1)}ue(()=>{a.syncPosition()})}Mt(de(e,"options"),()=>{ue(()=>{a.syncPosition()})});const p=k(()=>{const{self:{menuBoxShadow:i}}=g.value;return{"--n-menu-box-shadow":i}}),m=s?Ie("select",void 0,p,a.props):void 0;return{mergedTheme:a.mergedThemeRef,mergedClsPrefix:r,treeMate:v,handleToggle:u,handleMenuMousedown:c,cssVars:s?void 0:p,themeClass:m==null?void 0:m.themeClass,onRender:m==null?void 0:m.onRender}},render(){var e;return(e=this.onRender)===null||e===void 0||e.call(this),n(Ot,{clsPrefix:this.mergedClsPrefix,focusable:!0,nodeProps:this.nodeProps,class:[`${this.mergedClsPrefix}-popselect-menu`,this.themeClass],style:this.cssVars,theme:this.mergedTheme.peers.InternalSelectMenu,themeOverrides:this.mergedTheme.peerOverrides.InternalSelectMenu,multiple:this.multiple,treeMate:this.treeMate,size:this.size,value:this.value,virtualScroll:this.virtualScroll,scrollable:this.scrollable,renderLabel:this.renderLabel,onToggle:this.handleToggle,onMouseenter:this.onMouseenter,onMouseleave:this.onMouseenter,onMousedown:this.handleMenuMousedown,showCheckmark:this.showCheckmark},{header:()=>{var a,r;return((r=(a=this.$slots).header)===null||r===void 0?void 0:r.call(a))||[]},action:()=>{var a,r;return((r=(a=this.$slots).action)===null||r===void 0?void 0:r.call(a))||[]},empty:()=>{var a,r;return((r=(a=this.$slots).empty)===null||r===void 0?void 0:r.call(a))||[]}})}}),Et=Object.assign(Object.assign(Object.assign(Object.assign({},q.props),Le(fe,["showArrow","arrow"])),{placement:Object.assign(Object.assign({},fe.placement),{default:"bottom"}),trigger:{type:String,default:"hover"}}),pe),Wt=$({name:"Popselect",props:Et,slots:Object,inheritAttrs:!1,__popover__:!0,setup(e){const{mergedClsPrefixRef:a}=ce(e),r=q("Popselect","-popselect",void 0,me,e,a),s=z(null);function g(){var u;(u=s.value)===null||u===void 0||u.syncPosition()}function v(u){var c;(c=s.value)===null||c===void 0||c.setShow(u)}return St(je,{props:e,mergedThemeRef:r,syncPosition:g,setShow:v}),Object.assign(Object.assign({},{syncPosition:g,setShow:v}),{popoverInstRef:s,mergedTheme:r})},render(){const{mergedTheme:e}=this,a={theme:e.peers.Popover,themeOverrides:e.peerOverrides.Popover,builtinThemeOverrides:{padding:"0"},ref:"popoverInstRef",internalRenderBody:(r,s,g,v,w)=>{const{$attrs:u}=this;return n(Ht,Object.assign({},u,{class:[u.class,r],style:[u.style,...g]},yt(this.$props,Se),{ref:Pt(s),onMouseenter:be([v,u.onMouseenter]),onMouseleave:be([w,u.onMouseleave])}),{header:()=>{var c,o;return(o=(c=this.$slots).header)===null||o===void 0?void 0:o.call(c)},action:()=>{var c,o;return(o=(c=this.$slots).action)===null||o===void 0?void 0:o.call(c)},empty:()=>{var c,o;return(o=(c=this.$slots).empty)===null||o===void 0?void 0:o.call(c)}})}};return n(Bt,Object.assign({},Le(this.$props,Se),a,{internalDeactivateImmediately:!0}),{trigger:()=>{var r,s;return(s=(r=this.$slots).default)===null||s===void 0?void 0:s.call(r)}})}}),Jt={itemPaddingSmall:"0 4px",itemMarginSmall:"0 0 0 8px",itemMarginSmallRtl:"0 8px 0 0",itemPaddingMedium:"0 4px",itemMarginMedium:"0 0 0 8px",itemMarginMediumRtl:"0 8px 0 0",itemPaddingLarge:"0 4px",itemMarginLarge:"0 0 0 8px",itemMarginLargeRtl:"0 8px 0 0",buttonIconSizeSmall:"14px",buttonIconSizeMedium:"16px",buttonIconSizeLarge:"18px",inputWidthSmall:"60px",selectWidthSmall:"unset",inputMarginSmall:"0 0 0 8px",inputMarginSmallRtl:"0 8px 0 0",selectMarginSmall:"0 0 0 8px",prefixMarginSmall:"0 8px 0 0",suffixMarginSmall:"0 0 0 8px",inputWidthMedium:"60px",selectWidthMedium:"unset",inputMarginMedium:"0 0 0 8px",inputMarginMediumRtl:"0 8px 0 0",selectMarginMedium:"0 0 0 8px",prefixMarginMedium:"0 8px 0 0",suffixMarginMedium:"0 0 0 8px",inputWidthLarge:"60px",selectWidthLarge:"unset",inputMarginLarge:"0 0 0 8px",inputMarginLargeRtl:"0 8px 0 0",selectMarginLarge:"0 0 0 8px",prefixMarginLarge:"0 8px 0 0",suffixMarginLarge:"0 0 0 8px"};function qt(e){const{textColor2:a,primaryColor:r,primaryColorHover:s,primaryColorPressed:g,inputColorDisabled:v,textColorDisabled:w,borderColor:u,borderRadius:c,fontSizeTiny:o,fontSizeSmall:p,fontSizeMedium:m,heightTiny:i,heightSmall:f,heightMedium:h}=e;return Object.assign(Object.assign({},Jt),{buttonColor:"#0000",buttonColorHover:"#0000",buttonColorPressed:"#0000",buttonBorder:`1px solid ${u}`,buttonBorderHover:`1px solid ${u}`,buttonBorderPressed:`1px solid ${u}`,buttonIconColor:a,buttonIconColorHover:a,buttonIconColorPressed:a,itemTextColor:a,itemTextColorHover:s,itemTextColorPressed:g,itemTextColorActive:r,itemTextColorDisabled:w,itemColor:"#0000",itemColorHover:"#0000",itemColorPressed:"#0000",itemColorActive:"#0000",itemColorActiveHover:"#0000",itemColorDisabled:v,itemBorder:"1px solid #0000",itemBorderHover:"1px solid #0000",itemBorderPressed:"1px solid #0000",itemBorderActive:`1px solid ${r}`,itemBorderDisabled:`1px solid ${u}`,itemBorderRadius:c,itemSizeSmall:i,itemSizeMedium:f,itemSizeLarge:h,itemFontSizeSmall:o,itemFontSizeMedium:p,itemFontSizeLarge:m,jumperFontSizeSmall:o,jumperFontSizeMedium:p,jumperFontSizeLarge:m,jumperTextColor:a,jumperTextColorDisabled:w})}const Zt=ze({name:"Pagination",common:Re,peers:{Select:At,Input:$t,Popselect:me},self:qt}),Qt=Zt,ye=`
 background: var(--n-item-color-hover);
 color: var(--n-item-text-color-hover);
 border: var(--n-item-border-hover);
`,Pe=[R("button",`
 background: var(--n-button-color-hover);
 border: var(--n-button-border-hover);
 color: var(--n-button-icon-color-hover);
 `)],Kt=F("pagination",`
 display: flex;
 vertical-align: middle;
 font-size: var(--n-item-font-size);
 flex-wrap: nowrap;
`,[F("pagination-prefix",`
 display: flex;
 align-items: center;
 margin: var(--n-prefix-margin);
 `),F("pagination-suffix",`
 display: flex;
 align-items: center;
 margin: var(--n-suffix-margin);
 `),J("> *:not(:first-child)",`
 margin: var(--n-item-margin);
 `),F("select",`
 width: var(--n-select-width);
 `),J("&.transition-disabled",[F("pagination-item","transition: none!important;")]),F("pagination-quick-jumper",`
 white-space: nowrap;
 display: flex;
 color: var(--n-jumper-text-color);
 transition: color .3s var(--n-bezier);
 align-items: center;
 font-size: var(--n-jumper-font-size);
 `,[F("input",`
 margin: var(--n-input-margin);
 width: var(--n-input-width);
 `)]),F("pagination-item",`
 position: relative;
 cursor: pointer;
 user-select: none;
 -webkit-user-select: none;
 display: flex;
 align-items: center;
 justify-content: center;
 box-sizing: border-box;
 min-width: var(--n-item-size);
 height: var(--n-item-size);
 padding: var(--n-item-padding);
 background-color: var(--n-item-color);
 color: var(--n-item-text-color);
 border-radius: var(--n-item-border-radius);
 border: var(--n-item-border);
 fill: var(--n-button-icon-color);
 transition:
 color .3s var(--n-bezier),
 border-color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 fill .3s var(--n-bezier);
 `,[R("button",`
 background: var(--n-button-color);
 color: var(--n-button-icon-color);
 border: var(--n-button-border);
 padding: 0;
 `,[F("base-icon",`
 font-size: var(--n-button-icon-size);
 `)]),zt("disabled",[R("hover",ye,Pe),J("&:hover",ye,Pe),J("&:active",`
 background: var(--n-item-color-pressed);
 color: var(--n-item-text-color-pressed);
 border: var(--n-item-border-pressed);
 `,[R("button",`
 background: var(--n-button-color-pressed);
 border: var(--n-button-border-pressed);
 color: var(--n-button-icon-color-pressed);
 `)]),R("active",`
 background: var(--n-item-color-active);
 color: var(--n-item-text-color-active);
 border: var(--n-item-border-active);
 `,[J("&:hover",`
 background: var(--n-item-color-active-hover);
 `)])]),R("disabled",`
 cursor: not-allowed;
 color: var(--n-item-text-color-disabled);
 `,[R("active, button",`
 background-color: var(--n-item-color-disabled);
 border: var(--n-item-border-disabled);
 `)])]),R("disabled",`
 cursor: not-allowed;
 `,[F("pagination-quick-jumper",`
 color: var(--n-jumper-text-color-disabled);
 `)]),R("simple",`
 display: flex;
 align-items: center;
 flex-wrap: nowrap;
 `,[F("pagination-quick-jumper",[F("input",`
 margin: 0;
 `)])])]);function Xt(e){var a;if(!e)return 10;const{defaultPageSize:r}=e;if(r!==void 0)return r;const s=(a=e.pageSizes)===null||a===void 0?void 0:a[0];return typeof s=="number"?s:(s==null?void 0:s.value)||10}function Yt(e,a,r,s){let g=!1,v=!1,w=1,u=a;if(a===1)return{hasFastBackward:!1,hasFastForward:!1,fastForwardTo:u,fastBackwardTo:w,items:[{type:"page",label:1,active:e===1,mayBeFastBackward:!1,mayBeFastForward:!1}]};if(a===2)return{hasFastBackward:!1,hasFastForward:!1,fastForwardTo:u,fastBackwardTo:w,items:[{type:"page",label:1,active:e===1,mayBeFastBackward:!1,mayBeFastForward:!1},{type:"page",label:2,active:e===2,mayBeFastBackward:!0,mayBeFastForward:!1}]};const c=1,o=a;let p=e,m=e;const i=(r-5)/2;m+=Math.ceil(i),m=Math.min(Math.max(m,c+r-3),o-2),p-=Math.floor(i),p=Math.max(Math.min(p,o-r+3),c+2);let f=!1,h=!1;p>c+2&&(f=!0),m<o-2&&(h=!0);const d=[];d.push({type:"page",label:1,active:e===1,mayBeFastBackward:!1,mayBeFastForward:!1}),f?(g=!0,w=p-1,d.push({type:"fast-backward",active:!1,label:void 0,options:s?Be(c+1,p-1):null})):o>=c+1&&d.push({type:"page",label:c+1,mayBeFastBackward:!0,mayBeFastForward:!1,active:e===c+1});for(let b=p;b<=m;++b)d.push({type:"page",label:b,mayBeFastBackward:!1,mayBeFastForward:!1,active:e===b});return h?(v=!0,u=m+1,d.push({type:"fast-forward",active:!1,label:void 0,options:s?Be(m+1,o-1):null})):m===o-2&&d[d.length-1].label!==o-1&&d.push({type:"page",mayBeFastForward:!0,mayBeFastBackward:!1,label:o-1,active:e===o-1}),d[d.length-1].label!==o&&d.push({type:"page",mayBeFastForward:!1,mayBeFastBackward:!1,label:o,active:e===o}),{hasFastBackward:g,hasFastForward:v,fastBackwardTo:w,fastForwardTo:u,items:d}}function Be(e,a){const r=[];for(let s=e;s<=a;++s)r.push({label:`${s}`,value:s});return r}const Gt=Object.assign(Object.assign({},q.props),{simple:Boolean,page:Number,defaultPage:{type:Number,default:1},itemCount:Number,pageCount:Number,defaultPageCount:{type:Number,default:1},showSizePicker:Boolean,pageSize:Number,defaultPageSize:Number,pageSizes:{type:Array,default(){return[10]}},showQuickJumper:Boolean,size:{type:String,default:"medium"},disabled:Boolean,pageSlot:{type:Number,default:9},selectProps:Object,prev:Function,next:Function,goto:Function,prefix:Function,suffix:Function,label:Function,displayOrder:{type:Array,default:["pages","size-picker","quick-jumper"]},to:Lt.propTo,showQuickJumpDropdown:{type:Boolean,default:!0},"onUpdate:page":[Function,Array],onUpdatePage:[Function,Array],"onUpdate:pageSize":[Function,Array],onUpdatePageSize:[Function,Array],onPageSizeChange:[Function,Array],onChange:[Function,Array]}),ra=$({name:"Pagination",props:Gt,slots:Object,setup(e){const{mergedComponentPropsRef:a,mergedClsPrefixRef:r,inlineThemeDisabled:s,mergedRtlRef:g}=ce(e),v=q("Pagination","-pagination",Kt,Qt,e,r),{localeRef:w}=jt("Pagination"),u=z(null),c=z(e.defaultPage),o=z(Xt(e)),p=he(de(e,"page"),c),m=he(de(e,"pageSize"),o),i=k(()=>{const{itemCount:t}=e;if(t!==void 0)return Math.max(1,Math.ceil(t/m.value));const{pageCount:l}=e;return l!==void 0?Math.max(l,1):1}),f=z("");le(()=>{e.simple,f.value=String(p.value)});const h=z(!1),d=z(!1),b=z(!1),T=z(!1),O=()=>{e.disabled||(h.value=!0,L())},X=()=>{e.disabled||(h.value=!1,L())},Y=()=>{d.value=!0,L()},G=()=>{d.value=!1,L()},Z=t=>{S(t)},I=k(()=>Yt(p.value,i.value,e.pageSlot,e.showQuickJumpDropdown));le(()=>{I.value.hasFastBackward?I.value.hasFastForward||(h.value=!1,b.value=!1):(d.value=!1,T.value=!1)});const ee=k(()=>{const t=w.value.selectionSuffix;return e.pageSizes.map(l=>typeof l=="number"?{label:`${l} / ${t}`,value:l}:l)}),te=k(()=>{var t,l;return((l=(t=a==null?void 0:a.value)===null||t===void 0?void 0:t.Pagination)===null||l===void 0?void 0:l.inputSize)||we(e.size)}),ae=k(()=>{var t,l;return((l=(t=a==null?void 0:a.value)===null||t===void 0?void 0:t.Pagination)===null||l===void 0?void 0:l.selectSize)||we(e.size)}),ne=k(()=>(p.value-1)*m.value),Q=k(()=>{const t=p.value*m.value-1,{itemCount:l}=e;return l!==void 0&&t>l-1?l-1:t}),V=k(()=>{const{itemCount:t}=e;return t!==void 0?t:(e.pageCount||1)*m.value}),K=Rt("Pagination",g,r);function L(){ue(()=>{var t;const{value:l}=u;l&&(l.classList.add("transition-disabled"),(t=u.value)===null||t===void 0||t.offsetWidth,l.classList.remove("transition-disabled"))})}function S(t){if(t===p.value)return;const{"onUpdate:page":l,onUpdatePage:A,onChange:_,simple:ie}=e;l&&P(l,t),A&&P(A,t),_&&P(_,t),c.value=t,ie&&(f.value=String(t))}function D(t){if(t===m.value)return;const{"onUpdate:pageSize":l,onUpdatePageSize:A,onPageSizeChange:_}=e;l&&P(l,t),A&&P(A,t),_&&P(_,t),o.value=t,i.value<p.value&&S(i.value)}function j(){if(e.disabled)return;const t=Math.min(p.value+1,i.value);S(t)}function re(){if(e.disabled)return;const t=Math.max(p.value-1,1);S(t)}function x(){if(e.disabled)return;const t=Math.min(I.value.fastForwardTo,i.value);S(t)}function oe(){if(e.disabled)return;const t=Math.max(I.value.fastBackwardTo,1);S(t)}function B(t){D(t)}function H(){const t=Number.parseInt(f.value);Number.isNaN(t)||(S(Math.max(1,Math.min(t,i.value))),e.simple||(f.value=""))}function E(){H()}function y(t){if(!e.disabled)switch(t.type){case"page":S(t.label);break;case"fast-backward":oe();break;case"fast-forward":x();break}}function W(t){f.value=t.replace(/\D+/g,"")}le(()=>{p.value,m.value,L()});const N=k(()=>{const{size:t}=e,{self:{buttonBorder:l,buttonBorderHover:A,buttonBorderPressed:_,buttonIconColor:ie,buttonIconColorHover:$e,buttonIconColorPressed:Te,itemTextColor:Oe,itemTextColorHover:Ne,itemTextColorPressed:Ae,itemTextColorActive:_e,itemTextColorDisabled:Ue,itemColor:Ve,itemColorHover:De,itemColorPressed:He,itemColorActive:Ee,itemColorActiveHover:We,itemColorDisabled:Je,itemBorder:qe,itemBorderHover:Ze,itemBorderPressed:Qe,itemBorderActive:Ke,itemBorderDisabled:Xe,itemBorderRadius:Ye,jumperTextColor:Ge,jumperTextColorDisabled:et,buttonColor:tt,buttonColorHover:at,buttonColorPressed:nt,[C("itemPadding",t)]:rt,[C("itemMargin",t)]:ot,[C("inputWidth",t)]:it,[C("selectWidth",t)]:st,[C("inputMargin",t)]:lt,[C("selectMargin",t)]:dt,[C("jumperFontSize",t)]:ut,[C("prefixMargin",t)]:ct,[C("suffixMargin",t)]:mt,[C("itemSize",t)]:pt,[C("buttonIconSize",t)]:ft,[C("itemFontSize",t)]:ht,[`${C("itemMargin",t)}Rtl`]:vt,[`${C("inputMargin",t)}Rtl`]:gt},common:{cubicBezierEaseInOut:bt}}=v.value;return{"--n-prefix-margin":ct,"--n-suffix-margin":mt,"--n-item-font-size":ht,"--n-select-width":st,"--n-select-margin":dt,"--n-input-width":it,"--n-input-margin":lt,"--n-input-margin-rtl":gt,"--n-item-size":pt,"--n-item-text-color":Oe,"--n-item-text-color-disabled":Ue,"--n-item-text-color-hover":Ne,"--n-item-text-color-active":_e,"--n-item-text-color-pressed":Ae,"--n-item-color":Ve,"--n-item-color-hover":De,"--n-item-color-disabled":Je,"--n-item-color-active":Ee,"--n-item-color-active-hover":We,"--n-item-color-pressed":He,"--n-item-border":qe,"--n-item-border-hover":Ze,"--n-item-border-disabled":Xe,"--n-item-border-active":Ke,"--n-item-border-pressed":Qe,"--n-item-padding":rt,"--n-item-border-radius":Ye,"--n-bezier":bt,"--n-jumper-font-size":ut,"--n-jumper-text-color":Ge,"--n-jumper-text-color-disabled":et,"--n-item-margin":ot,"--n-item-margin-rtl":vt,"--n-button-icon-size":ft,"--n-button-icon-color":ie,"--n-button-icon-color-hover":$e,"--n-button-icon-color-pressed":Te,"--n-button-color-hover":at,"--n-button-color":tt,"--n-button-color-pressed":nt,"--n-button-border":l,"--n-button-border-hover":A,"--n-button-border-pressed":_}}),M=s?Ie("pagination",k(()=>{let t="";const{size:l}=e;return t+=l[0],t}),N,e):void 0;return{rtlEnabled:K,mergedClsPrefix:r,locale:w,selfRef:u,mergedPage:p,pageItems:k(()=>I.value.items),mergedItemCount:V,jumperValue:f,pageSizeOptions:ee,mergedPageSize:m,inputSize:te,selectSize:ae,mergedTheme:v,mergedPageCount:i,startIndex:ne,endIndex:Q,showFastForwardMenu:b,showFastBackwardMenu:T,fastForwardActive:h,fastBackwardActive:d,handleMenuSelect:Z,handleFastForwardMouseenter:O,handleFastForwardMouseleave:X,handleFastBackwardMouseenter:Y,handleFastBackwardMouseleave:G,handleJumperInput:W,handleBackwardClick:re,handleForwardClick:j,handlePageItemClick:y,handleSizePickerChange:B,handleQuickJumperChange:E,cssVars:s?void 0:N,themeClass:M==null?void 0:M.themeClass,onRender:M==null?void 0:M.onRender}},render(){const{$slots:e,mergedClsPrefix:a,disabled:r,cssVars:s,mergedPage:g,mergedPageCount:v,pageItems:w,showSizePicker:u,showQuickJumper:c,mergedTheme:o,locale:p,inputSize:m,selectSize:i,mergedPageSize:f,pageSizeOptions:h,jumperValue:d,simple:b,prev:T,next:O,prefix:X,suffix:Y,label:G,goto:Z,handleJumperInput:I,handleSizePickerChange:ee,handleBackwardClick:te,handlePageItemClick:ae,handleForwardClick:ne,handleQuickJumperChange:Q,onRender:V}=this;V==null||V();const K=X||e.prefix,L=Y||e.suffix,S=T||e.prev,D=O||e.next,j=G||e.label;return n("div",{ref:"selfRef",class:[`${a}-pagination`,this.themeClass,this.rtlEnabled&&`${a}-pagination--rtl`,r&&`${a}-pagination--disabled`,b&&`${a}-pagination--simple`],style:s},K?n("div",{class:`${a}-pagination-prefix`},K({page:g,pageSize:f,pageCount:v,startIndex:this.startIndex,endIndex:this.endIndex,itemCount:this.mergedItemCount})):null,this.displayOrder.map(re=>{switch(re){case"pages":return n(ve,null,n("div",{class:[`${a}-pagination-item`,!S&&`${a}-pagination-item--button`,(g<=1||g>v||r)&&`${a}-pagination-item--disabled`],onClick:te},S?S({page:g,pageSize:f,pageCount:v,startIndex:this.startIndex,endIndex:this.endIndex,itemCount:this.mergedItemCount}):n(U,{clsPrefix:a},{default:()=>this.rtlEnabled?n(Fe,null):n(xe,null)})),b?n(ve,null,n("div",{class:`${a}-pagination-quick-jumper`},n(ge,{value:d,onUpdateValue:I,size:m,placeholder:"",disabled:r,theme:o.peers.Input,themeOverrides:o.peerOverrides.Input,onChange:Q}))," /"," ",v):w.map((x,oe)=>{let B,H,E;const{type:y}=x;switch(y){case"page":const N=x.label;j?B=j({type:"page",node:N,active:x.active}):B=N;break;case"fast-forward":const M=this.fastForwardActive?n(U,{clsPrefix:a},{default:()=>this.rtlEnabled?n(Ce,null):n(Me,null)}):n(U,{clsPrefix:a},{default:()=>n(ke,null)});j?B=j({type:"fast-forward",node:M,active:this.fastForwardActive||this.showFastForwardMenu}):B=M,H=this.handleFastForwardMouseenter,E=this.handleFastForwardMouseleave;break;case"fast-backward":const t=this.fastBackwardActive?n(U,{clsPrefix:a},{default:()=>this.rtlEnabled?n(Me,null):n(Ce,null)}):n(U,{clsPrefix:a},{default:()=>n(ke,null)});j?B=j({type:"fast-backward",node:t,active:this.fastBackwardActive||this.showFastBackwardMenu}):B=t,H=this.handleFastBackwardMouseenter,E=this.handleFastBackwardMouseleave;break}const W=n("div",{key:oe,class:[`${a}-pagination-item`,x.active&&`${a}-pagination-item--active`,y!=="page"&&(y==="fast-backward"&&this.showFastBackwardMenu||y==="fast-forward"&&this.showFastForwardMenu)&&`${a}-pagination-item--hover`,r&&`${a}-pagination-item--disabled`,y==="page"&&`${a}-pagination-item--clickable`],onClick:()=>{ae(x)},onMouseenter:H,onMouseleave:E},B);if(y==="page"&&!x.mayBeFastBackward&&!x.mayBeFastForward)return W;{const N=x.type==="page"?x.mayBeFastBackward?"fast-backward":"fast-forward":x.type;return x.type!=="page"&&!x.options?W:n(Wt,{to:this.to,key:N,disabled:r,trigger:"hover",virtualScroll:!0,style:{width:"60px"},theme:o.peers.Popselect,themeOverrides:o.peerOverrides.Popselect,builtinThemeOverrides:{peers:{InternalSelectMenu:{height:"calc(var(--n-option-height) * 4.6)"}}},nodeProps:()=>({style:{justifyContent:"center"}}),show:y==="page"?!1:y==="fast-backward"?this.showFastBackwardMenu:this.showFastForwardMenu,onUpdateShow:M=>{y!=="page"&&(M?y==="fast-backward"?this.showFastBackwardMenu=M:this.showFastForwardMenu=M:(this.showFastBackwardMenu=!1,this.showFastForwardMenu=!1))},options:x.type!=="page"&&x.options?x.options:[],onUpdateValue:this.handleMenuSelect,scrollable:!0,showCheckmark:!1},{default:()=>W})}}),n("div",{class:[`${a}-pagination-item`,!D&&`${a}-pagination-item--button`,{[`${a}-pagination-item--disabled`]:g<1||g>=v||r}],onClick:ne},D?D({page:g,pageSize:f,pageCount:v,itemCount:this.mergedItemCount,startIndex:this.startIndex,endIndex:this.endIndex}):n(U,{clsPrefix:a},{default:()=>this.rtlEnabled?n(xe,null):n(Fe,null)})));case"size-picker":return!b&&u?n(_t,Object.assign({consistentMenuWidth:!1,placeholder:"",showCheckmark:!1,to:this.to},this.selectProps,{size:i,options:h,value:f,disabled:r,theme:o.peers.Select,themeOverrides:o.peerOverrides.Select,onUpdateValue:ee})):null;case"quick-jumper":return!b&&c?n("div",{class:`${a}-pagination-quick-jumper`},Z?Z():It(this.$slots.goto,()=>[p.goto]),n(ge,{value:d,onUpdateValue:I,size:m,placeholder:"",disabled:r,theme:o.peers.Input,themeOverrides:o.peerOverrides.Input,onChange:Q})):null;default:return null}}),L?n("div",{class:`${a}-pagination-suffix`},L({page:g,pageSize:f,pageCount:v,startIndex:this.startIndex,endIndex:this.endIndex,itemCount:this.mergedItemCount})):null)}});export{xe as B,Ce as F,ra as N,Fe as a,Me as b,Xt as g,Qt as p};
