import{V as U,d as w,r as W,O as n,ak as z,o as x,c as p,a as F,u as E,F as K,q as G,C as H,g as J,a4 as Q,D as b,N as $,l as V,_ as X}from"./index.js";import{a as _}from"./FormItem.js";import{N as Y,a as Z}from"./Switch.js";import{N as ee}from"./Select.js";import{A as te}from"./AddOutline.js";import{N as ne}from"./Input.js";const se=Object.assign(Object.assign({},U.props),{trigger:String,xScrollable:Boolean,onScroll:Function,contentClass:String,contentStyle:[Object,String],size:Number,yPlacement:{type:String,default:"right"},xPlacement:{type:String,default:"bottom"}}),ae=w({name:"Scrollbar",props:se,setup(){const m=W(null);return Object.assign(Object.assign({},{scrollTo:(...f)=>{var u;(u=m.value)===null||u===void 0||u.scrollTo(f[0],f[1])},scrollBy:(...f)=>{var u;(u=m.value)===null||u===void 0||u.scrollBy(f[0],f[1])}}),{scrollbarInstRef:m})},render(){return n(z,Object.assign({ref:"scrollbarInstRef"},this.$props),this.$slots)}}),ve=ae,le={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},oe=F("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M368 368L144 144"},null,-1),re=F("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M368 144L144 368"},null,-1),ie=[oe,re],ue=w({name:"CloseOutline",render:function(v,f){return x(),p("svg",le,ie)}}),ce={class:"dynamic-config-form"},de={key:0},me=w({__name:"DynamicConfigForm",props:{schema:{type:Object,required:!0},modelValue:{type:Object,required:!0}},emits:["update:modelValue"],setup(m,{expose:v,emit:f}){const u=E(),c=m,T=()=>{const t=c.schema.required||[];for(const e in c.schema.properties){const s=c.schema.properties[e];if(t.includes(e)&&!c.modelValue[e])return u.error(`${s.title} 是必填项`),!1}return!0},h=(t,e)=>{j("update:modelValue",{...c.modelValue,[t]:e})},j=f,P=(t,e)=>{var s;if(t.anyOf){const l=t.anyOf.find(a=>a.type!=="null");l&&(t={...t,type:l.type,enum:l.enum,enumNames:l.enumNames})}if(t.$ref&&!t.type){const l=t.$ref.split("/");if(l[0]==="#"&&l[1]==="$defs"){const a=l[2],o=(s=c.schema.$defs)==null?void 0:s[a];o&&(t={...t,...o})}}return t},N=(t,e,s,l)=>{var o,r;const a={value:s,placeholder:(o=e.examples)!=null&&o[0]||e.default!==void 0&&e.default!==null?String(e.default):"",onUpdateValue:l,disabled:e.readOnly===!0};switch(t){case"string":return e.enum?n(ee,{...a,options:(r=e.enum)==null?void 0:r.map((g,i)=>{var d;return{label:((d=e.enumNames)==null?void 0:d[i])||g,value:g}})}):n(ne,{...a,type:y(e.title)?"password":"text",showPasswordOn:"click"});case"number":case"integer":return n(Z,{...a,min:e.minimum,max:e.maximum});case"boolean":return n(Y,a);default:return n(b,{content:`不支持的类型: ${t}`})}},y=t=>{const e=t.toLowerCase();return["password","token","secret","key"].some(s=>e.includes(s))},B=(t,e,s)=>{var g;const l=(g=e.items)==null?void 0:g.type;if(!l)return null;let a=c.modelValue[t]||[];const o=i=>{var k,C,I,S,O;const d=a[i],A=()=>{a.splice(i,1),h(t,a.length>0?a:void 0)},D=R=>{a[i]=R,h(t,a)},M={title:`${e.title}[${i}]`,type:l,minimum:(k=e.items)==null?void 0:k.minimum,maximum:(C=e.items)==null?void 0:C.maximum,default:(I=e.items)==null?void 0:I.default,enum:(S=e.items)==null?void 0:S.enum,enumNames:(O=e.items)==null?void 0:O.enumNames};return n("div",{style:{display:"flex",alignItems:"center",marginBottom:"8px",gap:"8px"}},[n("span",{style:{minWidth:"32px",color:"var(--n-text-color-3)"}},`[${i+1}]`),n("div",{style:{flex:1}},[N(l,M,d,D)]),n($,{type:"error",size:"tiny",quaternary:!0,onClick:A},{default:()=>n(V,null,{default:()=>n(ue)})})])},r=()=>{var d;const i=((d=e.items)==null?void 0:d.default)!==void 0?e.items.default:null;a.push(i),h(t,a)};return n(_,s,{default:()=>n("div",{style:{padding:"8px 0"}},[...a.length>0?a.map((i,d)=>o(d)):[n("div",{style:{color:"var(--n-text-color-3)",fontStyle:"italic",padding:"4px 0"}},"暂无数据")],n($,{type:"primary",size:"small",dashed:!0,style:{width:"100%",marginTop:"8px"},onClick:r},{default:()=>[n(V,null,{default:()=>n(te)})," 添加"]})])})},q=(t,e)=>{if(t.toLowerCase().includes("webhook")||t.toLowerCase().includes("websocket")){const s=t.toLowerCase().includes("webhook")?"http://":"ws://";return n("div",{style:{background:"rgba(var(--primary-color-rgb), 0.08)",borderRadius:"8px",padding:"12px",border:"1px solid rgba(var(--primary-color-rgb), 0.15)",backdropFilter:"blur(4px)",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.05)",transition:"all 0.3s ease",marginTop:"4px"},class:"webhook-url-container"},[n(b,{type:"info"},{default:()=>"完整地址格式如下，请根据实际情况替换 WebUI 服务地址"}),n("div",{style:{marginTop:"6px",fontFamily:"Consolas, monospace",padding:"8px",background:"rgba(var(--card-bg-color-rgb), 0.8)",borderRadius:"4px",border:"1px dashed rgba(var(--primary-color-rgb), 0.2)"}},[n(b,{strong:!0},{default:()=>`${s}<WebUI服务地址>${e}`})]),n("div",{style:{marginTop:"8px",color:"var(--n-text-color-2)"}},[n(b,null,{default:()=>"• 对外服务：请使用公网可访问的IP或域名"}),n("br"),n(b,null,{default:()=>"• 内部服务：请使用对应的内部可访问地址"}),n("br"),n(b,null,{default:()=>"• 详情可查阅 Kirara AI 官方使用手册"})])])}return n(b,e)},L=(t,e)=>{var o;let s=c.modelValue[t];if(e.hidden_unset&&(s==null||s===""))return null;s===void 0&&e.default&&(s=e.default,h(t,s));const l=(o=c.schema.required)==null?void 0:o.includes(t),a={label:e.title,required:l,feedback:e.description||""};return e=P(e),e.type==="array"&&e.items?B(t,e,a):e.textType?n(_,a,{default:()=>{var r;return q(t,s||((r=e.examples)==null?void 0:r[0])||e.default||null)}}):n(_,a,{default:()=>{var r;return N(e.type||"string",e,s||((r=e.examples)==null?void 0:r[0])||e.default||null,g=>h(t,g))}})};return v({validateForm:T}),(t,e)=>(x(),p("div",ce,[(x(!0),p(K,null,G(m.schema.properties,(s,l)=>(x(),J(Q(L(l,s)),{key:l}))),128)),Object.keys(m.schema.properties).length===0?(x(),p("p",de,"暂无配置，请关闭本窗口")):H("",!0)]))}});const _e=X(me,[["__scopeId","data-v-a4a2e1e3"]]);export{ue as C,_e as D,ve as N};
