# Git 忽略文件配置说明

## 概述

本项目的 `.gitignore` 文件已经配置为忽略以下类型的文件和目录：

## 忽略的文件类型

### 🖥️ 操作系统文件
- `.DS_Store` (macOS)
- `Thumbs.db` (Windows)
- 临时文件和缓存

### 🐍 Python 后端相关
- `__pycache__/` - Python 字节码缓存
- `*.pyc` - 编译的 Python 文件
- `.env` - 环境变量文件
- `venv/`, `env/` - 虚拟环境
- `dist/`, `build/` - 构建输出
- `.pytest_cache/` - 测试缓存
- `*.log` - 日志文件

### 🌐 Node.js 前端相关
- `node_modules/` - 依赖包
- `dist/` - 构建输出
- `.env.local` - 本地环境变量
- `*.tsbuildinfo` - TypeScript 构建信息
- `.cache/` - 缓存目录

### 🤖 Kirara AI 项目特定
- `data/` - 运行时数据目录
- `logs/` - 日志目录
- `config.yaml` - 配置文件
- `*.bak` - 备份文件
- `data/web/password.hash` - 密码哈希文件
- `backend/data/vector_db/` - 向量数据库

### 🔧 开发工具
- `.vscode/`, `.idea/` - 编辑器配置
- `*.swp`, `*.swo` - Vim 临时文件
- `.coverage` - 测试覆盖率报告

## 保留的目录结构

使用 `.gitkeep` 文件保持以下空目录：
- `backend/data/.gitkeep`
- `backend/logs/.gitkeep`
- `backend/plugins/.gitkeep`

## 重要说明

### 已移除的跟踪文件
以下文件已从 git 跟踪中移除（但仍保留在本地）：
- `data/config.yaml`
- `data/config.yaml.bak`
- `logs/log_2025-07-10.log`

### 配置文件处理
- 敏感配置文件（如 `.env`）被忽略
- 提供 `.env.example` 作为配置模板
- 运行时生成的配置文件被忽略

### 数据文件处理
- 所有运行时数据目录被忽略
- 数据库文件被忽略
- 上传的媒体文件被忽略

## 使用建议

### 添加新的忽略规则
如果需要忽略新的文件类型，请在相应的分类下添加：

```gitignore
# 在对应分类下添加
# ===== 项目特定文件 =====
your-new-pattern/
*.your-extension
```

### 检查忽略状态
```bash
# 检查文件是否被忽略
git check-ignore filename

# 查看所有被忽略的文件
git status --ignored
```

### 强制添加被忽略的文件
```bash
# 如果确实需要跟踪某个被忽略的文件
git add -f filename
```

## 注意事项

1. **敏感信息**: 确保包含密码、API 密钥等敏感信息的文件被正确忽略
2. **大文件**: 避免提交大型数据文件或二进制文件
3. **个人配置**: 个人开发环境的配置文件应该被忽略
4. **构建产物**: 所有构建生成的文件都应该被忽略

## 维护

定期检查和更新 `.gitignore` 文件，确保：
- 新增的文件类型被正确处理
- 不会意外提交敏感信息
- 保持仓库的整洁性
