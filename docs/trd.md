# 聊天机器人技术文档

## 项目背景

先前凭借已实现现有库应用，为方便用户使用，将已有功能对接企业微信聊天功能。用户可在企业微信聊天知识库。

还需考虑扩展性：
- **通道扩展**：后续迁移飞书，系统应尽可能少改动；
- **功能扩展**：除知识问答外，还可接入其它功能。

## 项目信息

| 类型 | 地址 |
|------|------|
| git代码库 | [打开链接](git) |
| 管理后台 | [https://common-nova.jx.ruyi.cn/chat/](https://common-nova.jx.ruyi.cn/chat/) |

## 名词解释

| 名词 | 解释 |
|------|------|
| 消息通道 | 连接用户和AI系统的通信桥梁，负责接收用户消息和发送AI回复。支持企业微信、钉钉、飞书等多种平台接入。 |
| LLM模型 | 大语言模型(Large Language Model)，提供智能对话和文本生成能力。系统支持多种LLM模型接入和切换。 |
| 工作流 | 定义AI处理用户消息的业务逻辑流程，包括消息解析、知识库查询、LLM调用、响应生成等步骤的组合。 |
| 触发规则 | 决定用户消息应该执行哪个工作流的匹配规则，支持关键词匹配、正则表达式、LLM智能分析等多种方式。 |

## 业务流程

### 1. 用户交互流程
```mermaid
sequenceDiagram
    participant User as 用户
    participant Platform as 消息平台
    participant System as AI系统
    participant LLM as LLM模型
    participant KB as 知识库

    User->>Platform: 发送消息
    Platform->>System: Webhook推送
    System->>System: 消息解析
    System->>System: 触发规则匹配
    System->>KB: 查询相关知识
    System->>LLM: 生成回复
    System->>Platform: 发送响应
    Platform->>User: 显示回复
```

### 2. 知识库切换流程
```mermaid
sequenceDiagram
    participant User as 用户
    participant System as AI系统
    participant Redis as Redis缓存
    participant KB as 知识库

    User->>System: "切换知识库123"
    System->>System: 正则匹配提取ID
    System->>Redis: 保存用户知识库设置
    System->>User: "✅ 已成功切换到知识库 123"
    
    Note over User,KB: 后续对话自动使用知识库123
    
    User->>System: "什么是人工智能？"
    System->>Redis: 获取用户知识库设置
    System->>KB: 查询知识库123
    System->>User: 基于知识库123的回复
```

## 技术架构

### 1. 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   消息平台      │    │   AI聊天系统    │    │   外部服务      │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 企业微信    │ │◄──►│ │ 消息适配器  │ │    │ │ LLM服务     │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 钉钉        │ │◄──►│ │ 工作流引擎  │ │◄──►│ │ 知识库服务  │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 飞书        │ │◄──►│ │ 分发规则    │ │    │ │ Redis缓存   │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2. 核心组件

#### 2.1 消息适配器 (Message Adapter)
- **功能**：处理不同平台的消息格式转换
- **支持平台**：企业微信、钉钉、飞书、Web界面
- **关键特性**：
  - 统一消息格式 (IMMessage)
  - Webhook接收处理
  - 平台API调用封装
  - 消息类型支持（文本、图片、文件、Markdown等）

#### 2.2 工作流引擎 (Workflow Engine)
- **功能**：定义和执行业务处理流程
- **核心概念**：
  - Block：最小处理单元
  - Workflow：Block的组合和编排
  - Input/Output：数据流转接口
- **内置工作流**：
  - RAG对话工作流
  - 知识库切换工作流
  - 通用消息发送工作流

#### 2.3 分发规则 (Dispatch Rules)
- **功能**：决定消息的处理路径
- **规则类型**：
  - LLM智能分发：使用大模型分析消息意图
  - RAG会话分发：基于用户知识库设置
  - 关键词匹配：简单文本匹配
  - 正则表达式：复杂模式匹配

#### 2.4 知识库管理
- **功能**：管理用户的知识库偏好设置
- **存储方式**：Redis键值存储
- **数据格式**：`rag_user_current_repo_id:{user_id} = {repo_id}`
- **过期策略**：30天自动过期

### 3. 技术栈

#### 3.1 后端技术
- **框架**：FastAPI + Python 3.8+
- **工作流引擎**：Kirara-AI
- **数据库**：Redis (缓存和会话存储)
- **消息队列**：异步任务处理
- **日志系统**：结构化日志记录

#### 3.2 前端技术
- **框架**：Vue 3 + TypeScript
- **UI组件**：Naive UI
- **构建工具**：Vite
- **状态管理**：Pinia
- **路由管理**：Vue Router

#### 3.3 部署架构
- **容器化**：Docker + Docker Compose
- **反向代理**：Nginx
- **进程管理**：Supervisor
- **监控告警**：系统状态监控

## 参考资料

### 开发文档
- [Kirara-AI工作流引擎文档](https://kirara-ai.readthedocs.io/)
- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [Vue 3官方文档](https://vuejs.org/)

### API文档
- [企业微信API文档](https://developer.work.weixin.qq.com/document/)
- [钉钉开放平台文档](https://open.dingtalk.com/)
- [飞书开放平台文档](https://open.feishu.cn/)

### 部署指南
- [Docker部署文档](./deployment.md)
- [环境配置说明](./configuration.md)
- [监控运维指南](./monitoring.md)

## 快速开始

### 1. 环境准备
```bash
# 克隆代码库
git clone <repository-url>
cd ai_chat

# 安装依赖
cd backend
pip install -r requirements.txt

cd ../frontend
npm install
```

### 2. 配置文件
```bash
# 复制配置模板
cp backend/config/.env.example backend/config/.env.dev

# 编辑配置文件
vim backend/config/.env.dev
```

### 3. 启动服务
```bash
# 启动后端服务
cd backend
python main.py

# 启动前端服务
cd frontend
npm run dev
```

### 4. 验证部署
- 访问管理后台：http://localhost:3000
- 测试API接口：http://localhost:8000/docs

## 开发指南

### 1. 添加新的消息通道

#### 步骤1：创建适配器
```python
# backend/app/adapters/new_platform/
├── __init__.py
├── adapter.py      # 主适配器类
├── delegates.py    # API调用封装
└── message.py      # 消息格式定义
```

#### 步骤2：实现适配器接口
```python
class NewPlatformAdapter(BaseAdapter):
    def __init__(self, config: dict):
        super().__init__(config)

    async def receive_message(self, webhook_data: dict) -> IMMessage:
        """接收并解析消息"""
        pass

    async def send_message(self, message: IMMessage) -> bool:
        """发送消息到平台"""
        pass
```

#### 步骤3：注册路由
```python
# backend/app/routers/webhook.py
@router.post("/webhook/new_platform/{token}")
async def new_platform_webhook(token: str, request: Request):
    # 处理新平台的webhook
    pass
```

### 2. 创建自定义工作流

#### 步骤1：定义Block
```python
class CustomBlock(BaseBlock):
    def __init__(self):
        super().__init__()
        self.input_schema = CustomInputSchema
        self.output_schema = CustomOutputSchema

    async def execute(self, input_data: CustomInputSchema) -> CustomOutputSchema:
        # 实现具体逻辑
        pass
```

#### 步骤2：组装工作流
```python
def create_custom_workflow() -> Workflow:
    workflow = Workflow("custom_workflow")

    # 添加Block
    workflow.add_block("step1", CustomBlock())
    workflow.add_block("step2", AnotherBlock())

    # 定义连接
    workflow.connect("step1", "step2")

    return workflow
```

#### 步骤3：注册工作流
```python
# 在插件中注册
workflow_registry.register("namespace", "workflow_name", workflow)
```

### 3. 添加分发规则

#### 步骤1：继承基类
```python
class CustomDispatchRule(BaseDispatchRule):
    def __init__(self, workflow_registry: WorkflowRegistry):
        super().__init__(workflow_registry, "target_workflow_id")
        self.priority = 5  # 设置优先级

    def match(self, message: IMMessage) -> bool:
        # 实现匹配逻辑
        return True
```

#### 步骤2：注册规则
```python
# 使用类方法注册
DispatchRule.register_rule_type(CustomDispatchRule)
```

## 配置说明

### 环境变量配置
```bash
# 基础配置
APP_ENV=development
DEBUG=true
LOG_LEVEL=INFO

# 数据库配置
KVDB_REDIS_HOST=localhost
KVDB_REDIS_PORT=6379
KVDB_REDIS_PASSWORD=
KVDB_REDIS_DB=0

# 企业微信配置
NOVA_WECOM_CORP_ID=your_corp_id
NOVA_WECOM_CORP_SECRET=your_secret
NOVA_WECOM_AGENT_ID=your_agent_id

# LLM配置
LLM_API_KEY=your_api_key
LLM_BASE_URL=https://api.openai.com/v1
```

### 工作流配置
```yaml
# config/workflows.yaml
workflows:
  rag:
    chat:
      enabled: true
      timeout: 30
      retry_count: 3

  im:
    send_msg:
      enabled: true
      rate_limit: 100
```

## 故障排查

### 常见问题

#### 1. 消息接收失败
**症状**：Webhook接收不到消息
**排查步骤**：
1. 检查Webhook URL配置是否正确
2. 验证平台回调设置
3. 查看服务器日志
4. 测试网络连通性

#### 2. 工作流执行异常
**症状**：消息处理失败或超时
**排查步骤**：
1. 检查工作流配置
2. 查看Block执行日志
3. 验证输入数据格式
4. 检查外部服务连接

#### 3. 知识库切换失败
**症状**：用户无法切换知识库
**排查步骤**：
1. 检查Redis连接状态
2. 验证知识库ID有效性
3. 查看分发规则匹配日志
4. 检查用户权限设置

### 日志分析
```bash
# 查看实时日志
tail -f logs/app.log

# 过滤错误日志
grep "ERROR" logs/app.log

# 查看特定用户的操作日志
grep "user_id:12345" logs/app.log
```

## 性能优化

### 1. 缓存策略
- **Redis缓存**：用户会话状态、知识库设置
- **内存缓存**：工作流定义、分发规则
- **CDN缓存**：静态资源文件

### 2. 异步处理
- **消息队列**：长时间任务异步执行
- **批量处理**：多个消息合并处理
- **连接池**：数据库连接复用

### 3. 监控指标
- **响应时间**：API接口响应延迟
- **吞吐量**：每秒处理消息数量
- **错误率**：失败请求比例
- **资源使用**：CPU、内存、磁盘使用率
