# AI Chat Project .gitignore

# ===== 通用文件 =====
# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 编辑器和IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 临时文件
*.tmp
*.temp
*.log
*.pid
*.seed
*.pid.lock

# ===== Python 后端 =====
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# conda
.conda/
conda-meta/

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# ===== Node.js 前端 =====
# Logs
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/

# Vite build output
dist/
dist-ssr/

# Rollup.js default build output
dist/

# Storybook build outputs
.out
.storybook-out
storybook-static

# Temporary folders
tmp/
temp/

# ===== 项目特定文件 =====
# Kirara AI 数据目录
data/
!data/.gitkeep

# 日志文件
logs/
*.log
!logs/.gitkeep

# 后端数据文件
backend/data/
backend/logs/
backend/uploads/

# 配置文件（包含敏感信息）
backend/.env
frontend/.env
.env.local
.env.production

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 向量数据库
backend/data/vector_db/
**/vector_db/

# 插件缓存
backend/plugins/__pycache__/
**/plugins/__pycache__/

# Kirara AI 配置（可能包含敏感信息）
backend/config/kirara_config.yaml
**/kirara_config.yaml
config.yaml
config.yaml.bak

# 上传的文件
uploads/

# 缓存目录
.cache/
cache/

# 备份文件
*.bak
*.backup
*.old

# Web 相关
data/web/
**/password.hash

# 压缩文件
*.zip
*.tar.gz
*.rar

# ===== Docker =====
# Docker 相关文件（如果不需要版本控制）
# Dockerfile
# docker-compose.yml
# .dockerignore

# ===== 其他 =====
# 测试文件
test_output/
test_results/

# 文档生成
docs/build/
docs/_build/

# 本地开发配置
local_config.py
local_settings.py
development.ini
