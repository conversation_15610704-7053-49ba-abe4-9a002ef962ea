# 数据库迁移命令

## 1. 导出当前数据库 (ai_chat2)

### 导出完整数据库（结构+数据）
```bash
pg_dump -h 192.168.20.87 -p 5432 -U root -d ai_chat2 -f ai_chat2_backup.sql
```

### 分别导出结构和数据
```bash
# 仅导出结构
pg_dump -h 192.168.20.87 -p 5432 -U root -d ai_chat2 --schema-only -f ai_chat2_schema.sql

# 仅导出数据
pg_dump -h 192.168.20.87 -p 5432 -U root -d ai_chat2 --data-only -f ai_chat2_data.sql
```

## 2. 创建新数据库和用户的SQL语句

```sql
-- 创建新用户 nova_chat
CREATE USER nova_chat WITH PASSWORD 'dNtHSpu1xd6HbhtLJEva';

-- 创建新数据库 nova_chat
CREATE DATABASE nova_chat OWNER nova_chat;

-- 授权给用户 nova_chat
GRANT ALL PRIVILEGES ON DATABASE nova_chat TO nova_chat;
```

## 3. 导入数据到新数据库

```bash
# 导入完整备份到新数据库
psql -h 192.168.20.87 -p 5432 -U nova_chat -d nova_chat -f ai_chat2_backup.sql
```

## 4. 设置数据库权限

连接到新数据库后执行：

```sql
-- 连接到新数据库
\c nova_chat;

-- 授权用户对所有表的权限
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO nova_chat;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO nova_chat;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO nova_chat;

-- 授权用户对未来创建的对象的权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO nova_chat;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO nova_chat;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO nova_chat;

-- 授权用户创建表的权限
GRANT CREATE ON SCHEMA public TO nova_chat;
```

## 5. 更新应用配置

更新 `.env` 文件中的数据库连接字符串：

```
DATABASE_URL=**************************************************************/nova_chat
```

## 执行顺序

1. 执行导出命令备份当前数据库
2. 以超级用户身份执行创建用户和数据库的SQL
3. 导入数据到新数据库
4. 设置权限
5. 更新应用配置文件
6. 重启应用服务

## 注意事项

- 请确保在执行前备份原数据库
- 创建用户和数据库需要超级用户权限
- 密码请根据实际情况修改
- 导入完成后请测试应用连接
