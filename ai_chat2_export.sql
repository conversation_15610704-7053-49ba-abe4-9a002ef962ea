--
-- PostgreSQL database dump for ai_chat2
-- Generated for migration to nova_chat
--

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: ai_chat2; Type: DATABASE; Schema: -; Owner: root
--

-- CREATE DATABASE ai_chat2 WITH TEMPLATE = template0 ENCODING = 'UTF8';

--
-- Name: public; Type: SCHEMA; Schema: -; Owner: root
--

-- CREATE SCHEMA public;

--
-- Name: SCHEMA public; Type: COMMENT; Schema: -; Owner: root
--

-- COMMENT ON SCHEMA public IS 'standard public schema';

SET default_tablespace = '';
SET default_table_access_method = heap;

--
-- Name: alembic_version; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE public.alembic_version (
    version_num character varying(32) NOT NULL
);

--
-- Name: app_configs; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE public.app_configs (
    id integer NOT NULL,
    key character varying(255) NOT NULL,
    value text,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

--
-- Name: app_configs_id_seq; Type: SEQUENCE; Schema: public; Owner: root
--

CREATE SEQUENCE public.app_configs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Name: app_configs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: root
--

ALTER SEQUENCE public.app_configs_id_seq OWNED BY public.app_configs.id;

--
-- Name: llm_request_traces; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE public.llm_request_traces (
    id character varying(36) NOT NULL,
    session_id character varying(255),
    user_id character varying(255),
    model character varying(255),
    provider character varying(255),
    request_data jsonb,
    response_data jsonb,
    tokens_used integer,
    cost numeric(10,6),
    duration_ms integer,
    status character varying(50),
    error_message text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

--
-- Name: user_guide_settings; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE public.user_guide_settings (
    id integer NOT NULL,
    user_id character varying(255) NOT NULL,
    guide_type character varying(100) NOT NULL,
    is_completed boolean DEFAULT false,
    current_step integer DEFAULT 1,
    completed_steps jsonb DEFAULT '[]'::jsonb,
    settings jsonb DEFAULT '{}'::jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

--
-- Name: user_guide_settings_id_seq; Type: SEQUENCE; Schema: public; Owner: root
--

CREATE SEQUENCE public.user_guide_settings_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Name: user_guide_settings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: root
--

ALTER SEQUENCE public.user_guide_settings_id_seq OWNED BY public.user_guide_settings.id;

--
-- Name: user_guide_steps; Type: TABLE; Schema: public; Owner: root
--

CREATE TABLE public.user_guide_steps (
    id integer NOT NULL,
    guide_type character varying(100) NOT NULL,
    step_number integer NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    content jsonb DEFAULT '{}'::jsonb,
    is_required boolean DEFAULT true,
    sort_order integer DEFAULT 0,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

--
-- Name: user_guide_steps_id_seq; Type: SEQUENCE; Schema: public; Owner: root
--

CREATE SEQUENCE public.user_guide_steps_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Name: user_guide_steps_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: root
--

ALTER SEQUENCE public.user_guide_steps_id_seq OWNED BY public.user_guide_steps.id;

--
-- Name: app_configs id; Type: DEFAULT; Schema: public; Owner: root
--

ALTER TABLE ONLY public.app_configs ALTER COLUMN id SET DEFAULT nextval('public.app_configs_id_seq'::regclass);

--
-- Name: user_guide_settings id; Type: DEFAULT; Schema: public; Owner: root
--

ALTER TABLE ONLY public.user_guide_settings ALTER COLUMN id SET DEFAULT nextval('public.user_guide_settings_id_seq'::regclass);

--
-- Name: user_guide_steps id; Type: DEFAULT; Schema: public; Owner: root
--

ALTER TABLE ONLY public.user_guide_steps ALTER COLUMN id SET DEFAULT nextval('public.user_guide_steps_id_seq'::regclass);

--
-- Data for Name: alembic_version; Type: TABLE DATA; Schema: public; Owner: root
--

INSERT INTO public.alembic_version (version_num) VALUES ('003_drop_config_items_table');

--
-- Data for Name: app_configs; Type: TABLE DATA; Schema: public; Owner: root
--

-- No data in app_configs table

--
-- Data for Name: llm_request_traces; Type: TABLE DATA; Schema: public; Owner: root
--

-- No data in llm_request_traces table

--
-- Data for Name: user_guide_settings; Type: TABLE DATA; Schema: public; Owner: root
--

-- No data in user_guide_settings table

--
-- Data for Name: user_guide_steps; Type: TABLE DATA; Schema: public; Owner: root
--

-- No data in user_guide_steps table

--
-- Name: app_configs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: root
--

SELECT pg_catalog.setval('public.app_configs_id_seq', 1, false);

--
-- Name: user_guide_settings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: root
--

SELECT pg_catalog.setval('public.user_guide_settings_id_seq', 1, false);

--
-- Name: user_guide_steps_id_seq; Type: SEQUENCE SET; Schema: public; Owner: root
--

SELECT pg_catalog.setval('public.user_guide_steps_id_seq', 1, false);

--
-- Name: alembic_version alembic_version_pkc; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY public.alembic_version
    ADD CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num);

--
-- Name: app_configs app_configs_key_key; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY public.app_configs
    ADD CONSTRAINT app_configs_key_key UNIQUE (key);

--
-- Name: app_configs app_configs_pkey; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY public.app_configs
    ADD CONSTRAINT app_configs_pkey PRIMARY KEY (id);

--
-- Name: llm_request_traces llm_request_traces_pkey; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY public.llm_request_traces
    ADD CONSTRAINT llm_request_traces_pkey PRIMARY KEY (id);

--
-- Name: user_guide_settings user_guide_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY public.user_guide_settings
    ADD CONSTRAINT user_guide_settings_pkey PRIMARY KEY (id);

--
-- Name: user_guide_settings user_guide_settings_user_id_guide_type_key; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY public.user_guide_settings
    ADD CONSTRAINT user_guide_settings_user_id_guide_type_key UNIQUE (user_id, guide_type);

--
-- Name: user_guide_steps user_guide_steps_guide_type_step_number_key; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY public.user_guide_steps
    ADD CONSTRAINT user_guide_steps_guide_type_step_number_key UNIQUE (guide_type, step_number);

--
-- Name: user_guide_steps user_guide_steps_pkey; Type: CONSTRAINT; Schema: public; Owner: root
--

ALTER TABLE ONLY public.user_guide_steps
    ADD CONSTRAINT user_guide_steps_pkey PRIMARY KEY (id);

--
-- Name: idx_llm_request_traces_created_at; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX idx_llm_request_traces_created_at ON public.llm_request_traces USING btree (created_at);

--
-- Name: idx_llm_request_traces_session_id; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX idx_llm_request_traces_session_id ON public.llm_request_traces USING btree (session_id);

--
-- Name: idx_llm_request_traces_user_id; Type: INDEX; Schema: public; Owner: root
--

CREATE INDEX idx_llm_request_traces_user_id ON public.llm_request_traces USING btree (user_id);

--
-- PostgreSQL database dump complete
--
