# BASE_URL 配置指南

## 概述

前端应用现在支持通过环境变量 `BASE_URL` 来配置应用的基础路径，默认值为 `/`。

## 配置方法

### 1. 环境变量配置

```bash
# 默认配置（根路径）
BASE_URL=/

# 子路径部署示例
BASE_URL=/chat/
BASE_URL=/ai-chat/
BASE_URL=/app/
```

### 2. 开发环境

```bash
# 方法一：在命令行中设置
BASE_URL=/chat/ npm run dev

# 方法二：创建 .env 文件
echo "BASE_URL=/chat/" > .env
npm run dev
```

### 3. 生产环境构建

```bash
# 方法一：在命令行中设置
BASE_URL=/chat/ npm run build

# 方法二：使用自定义构建脚本
npm run build:custom /chat/

# 方法三：使用 .env 文件
echo "BASE_URL=/chat/" > .env
npm run build
```

## 使用场景

### 根路径部署
```bash
BASE_URL=/
# 访问地址: https://example.com/
```

### 子路径部署
```bash
BASE_URL=/chat/
# 访问地址: https://example.com/chat/
```

### 多级子路径
```bash
BASE_URL=/apps/ai-chat/
# 访问地址: https://example.com/apps/ai-chat/
```

## 构建输出结构

构建文件会根据 BASE_URL 放置在不同的目录中：

```bash
# BASE_URL=/ 时（根路径）
dist/
├── index.html
├── assets/
│   ├── index.js
│   └── index.css
└── ...

# BASE_URL=/chat/ 时
dist/chat/
├── index.html
├── assets/
│   ├── index.js
│   └── index.css
└── ...

# BASE_URL=/ai-chat/ 时
dist/ai-chat/
├── index.html
├── assets/
│   ├── index.js
│   └── index.css
└── ...
```

## 验证配置

构建完成后，检查对应目录下的 `index.html` 文件中的资源路径：

```html
<!-- BASE_URL=/ 时 (dist/index.html) -->
<script src="/assets/index.js"></script>
<link href="/assets/index.css" rel="stylesheet">

<!-- BASE_URL=/chat/ 时 (dist/chat/index.html) -->
<script src="/chat/assets/index.js"></script>
<link href="/chat/assets/index.css" rel="stylesheet">
```

## 注意事项

1. **路径格式**: BASE_URL 应该以 `/` 开头和结尾（除了根路径 `/`）
2. **服务器配置**: 确保 Web 服务器正确配置了对应的路径映射
3. **API 路径**: 如果后端 API 也部署在子路径下，需要相应调整 API 基础路径

## 故障排除

### 资源加载失败
- 检查 BASE_URL 格式是否正确
- 确认 Web 服务器配置是否匹配
- 验证构建后的 HTML 文件中的路径

### 路由问题
- 确保前端路由配置与 BASE_URL 一致
- 检查 Vue Router 的 base 配置

### 开发环境问题
- 清除浏览器缓存
- 重启开发服务器
- 检查 .env 文件是否正确加载
