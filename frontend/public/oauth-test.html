<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .output {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>OAuth 测试页面</h1>
    
    <div class="section">
        <h2>1. 设置测试 State</h2>
        <button onclick="setTestState()">设置测试 State</button>
        <div id="stateOutput" class="output"></div>
    </div>
    
    <div class="section">
        <h2>2. 测试 OAuth 回调</h2>
        <button onclick="testCallback()">测试回调处理</button>
        <div id="callbackOutput" class="output"></div>
    </div>
    
    <div class="section">
        <h2>3. 检查 localStorage</h2>
        <button onclick="checkStorage()">检查存储</button>
        <div id="storageOutput" class="output"></div>
    </div>
    
    <div class="section">
        <h2>4. 测试 API 调用</h2>
        <button onclick="testAPI()">测试 OAuth API</button>
        <div id="apiOutput" class="output"></div>
    </div>

    <script>
        function setTestState() {
            const state = 'fjejouteunqwnj5ihzhnu';
            localStorage.setItem('oauth_state', state);
            document.getElementById('stateOutput').textContent = `已设置 state: ${state}`;
        }
        
        function testCallback() {
            const code = '17nZfpkcAqjPSXMjsbOHhrHnAzkPfLzaE88SYyFsjEo';
            const state = 'fjejouteunqwnj5ihzhnu';
            
            // 模拟回调处理逻辑
            const savedState = localStorage.getItem('oauth_state');
            
            let result = `接收到的参数:\n`;
            result += `code: ${code}\n`;
            result += `state: ${state}\n`;
            result += `保存的 state: ${savedState}\n`;
            result += `state 验证: ${state === savedState ? '通过' : '失败'}\n`;
            
            document.getElementById('callbackOutput').textContent = result;
        }
        
        function checkStorage() {
            const state = localStorage.getItem('oauth_state');
            const token = localStorage.getItem('token');
            
            let result = `localStorage 内容:\n`;
            result += `oauth_state: ${state}\n`;
            result += `token: ${token}\n`;
            
            document.getElementById('storageOutput').textContent = result;
        }
        
        async function testAPI() {
            try {
                const code = '17nZfpkcAqjPSXMjsbOHhrHnAzkPfLzaE88SYyFsjEo';
                const state = 'fjejouteunqwnj5ihzhnu';
                
                const response = await fetch('http://localhost:8080/chat-api/api/auth/oauth/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ code, state })
                });
                
                const data = await response.json();
                
                let result = `API 调用结果:\n`;
                result += `状态码: ${response.status}\n`;
                result += `响应: ${JSON.stringify(data, null, 2)}\n`;
                
                document.getElementById('apiOutput').textContent = result;
            } catch (error) {
                document.getElementById('apiOutput').textContent = `API 调用失败: ${error.message}`;
            }
        }
    </script>
</body>
</html>
