<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证清理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056CC;
        }
        .danger {
            background: #FF3B30;
        }
        .danger:hover {
            background: #CC2E24;
        }
        .success {
            background: #34C759;
        }
        .success:hover {
            background: #28A745;
        }
        .info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>认证清理功能测试</h1>
        
        <div class="section">
            <h3>1. 模拟设置认证数据</h3>
            <button onclick="setMockAuthData()">设置模拟认证数据</button>
            <div class="info">
                这将在 localStorage 中设置一些模拟的认证数据，用于测试清理功能。
            </div>
        </div>

        <div class="section">
            <h3>2. 检查当前认证数据</h3>
            <button onclick="checkAuthData()">检查认证数据</button>
            <div id="authStatus" class="info">
                点击按钮检查当前的认证数据状态
            </div>
        </div>

        <div class="section">
            <h3>3. 清理认证数据</h3>
            <button onclick="clearAuthData()" class="danger">清理认证数据</button>
            <button onclick="forceCleanAuth()" class="danger">强制清理所有数据</button>
            <div class="info">
                测试不同的清理方法是否能正确清理所有认证相关数据。
            </div>
        </div>

        <div class="section">
            <h3>4. 测试登录页清理</h3>
            <button onclick="testLoginPageCleanup()" class="success">模拟进入登录页</button>
            <div class="info">
                模拟用户进入登录页面时的清理行为。
            </div>
        </div>

        <div class="section">
            <h3>5. 操作日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="log" class="log">
                操作日志将显示在这里...
            </div>
        </div>
    </div>

    <script>
        // 日志功能
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 设置模拟认证数据
        function setMockAuthData() {
            const mockData = {
                token: 'mock_access_token_12345',
                user_info: JSON.stringify({
                    id: 'user123',
                    username: 'testuser',
                    email: '<EMAIL>'
                }),
                oauth_state: 'mock_oauth_state_67890',
                refresh_token: 'mock_refresh_token_abcde'
            };

            Object.entries(mockData).forEach(([key, value]) => {
                localStorage.setItem(key, value);
                log(`设置 localStorage.${key} = ${value.substring(0, 20)}...`);
            });

            // 设置一些 sessionStorage 数据
            sessionStorage.setItem('session_data', 'mock_session_data');
            log('设置 sessionStorage.session_data');

            log('✅ 模拟认证数据设置完成');
            checkAuthData();
        }

        // 检查认证数据
        function checkAuthData() {
            const authKeys = ['token', 'user_info', 'oauth_state', 'refresh_token', 'access_token'];
            const statusDiv = document.getElementById('authStatus');
            let status = '';

            // 检查 localStorage
            const localStorageData = [];
            authKeys.forEach(key => {
                const value = localStorage.getItem(key);
                if (value) {
                    localStorageData.push(`${key}: ${value.substring(0, 20)}...`);
                }
            });

            // 检查 sessionStorage
            const sessionStorageData = [];
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                sessionStorageData.push(key);
            }

            status += `<strong>localStorage 认证数据:</strong><br>`;
            if (localStorageData.length > 0) {
                status += localStorageData.join('<br>') + '<br><br>';
            } else {
                status += '无认证数据<br><br>';
            }

            status += `<strong>sessionStorage 数据:</strong><br>`;
            if (sessionStorageData.length > 0) {
                status += sessionStorageData.join(', ') + '<br>';
            } else {
                status += '无数据<br>';
            }

            statusDiv.innerHTML = status;
            log('🔍 检查认证数据完成');
        }

        // 清理认证数据（模拟应用中的清理逻辑）
        function clearAuthData() {
            log('🧹 开始清理认证数据...');
            
            const authKeys = ['token', 'user_info', 'oauth_state', 'refresh_token', 'access_token'];
            
            authKeys.forEach(key => {
                if (localStorage.getItem(key)) {
                    localStorage.removeItem(key);
                    log(`清理 localStorage.${key}`);
                }
            });

            if (sessionStorage.length > 0) {
                sessionStorage.clear();
                log('清理 sessionStorage');
            }

            log('✅ 认证数据清理完成');
            checkAuthData();
        }

        // 强制清理所有数据
        function forceCleanAuth() {
            log('💥 开始强制清理所有数据...');
            
            // 清理所有可能的认证相关 localStorage
            Object.keys(localStorage).forEach(key => {
                if (key.includes('token') || key.includes('auth') || key.includes('user') || key.includes('oauth')) {
                    localStorage.removeItem(key);
                    log(`强制清理 localStorage.${key}`);
                }
            });

            sessionStorage.clear();
            log('强制清理 sessionStorage');

            log('✅ 强制清理完成');
            checkAuthData();
        }

        // 测试登录页清理
        function testLoginPageCleanup() {
            log('🚪 模拟进入登录页面...');
            
            // 模拟登录页面的清理逻辑
            log('执行登录页面清理逻辑');
            clearAuthData();
            
            log('✅ 登录页面清理测试完成');
        }

        // 页面加载时初始化
        window.onload = function() {
            log('📄 测试页面加载完成');
            checkAuthData();
        };
    </script>
</body>
</html>
