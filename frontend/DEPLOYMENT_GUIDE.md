# 部署指南

## 构建输出结构

前端应用现在会根据 `BASE_URL` 将构建文件放置在不同的目录中，这样可以更好地组织多个部署环境的构建产物。

### 目录结构

```bash
dist/
├── root/          # BASE_URL=/ 的构建产物
├── chat/          # BASE_URL=/chat/ 的构建产物
├── ai-chat/       # BASE_URL=/ai-chat/ 的构建产物
└── ...            # 其他自定义路径的构建产物
```

## 部署方案

### 1. 根路径部署

**构建命令：**
```bash
BASE_URL=/ npm run build
# 或
./scripts/build-with-base.sh /
```

**部署：**
```bash
# 将 dist/root/ 目录的内容部署到 Web 服务器根目录
cp -r dist/root/* /var/www/html/
```

**访问地址：** `https://example.com/`

### 2. 子路径部署

**构建命令：**
```bash
BASE_URL=/chat/ npm run build
# 或
./scripts/build-with-base.sh /chat/
```

**部署：**
```bash
# 将 dist/chat/ 目录的内容部署到 Web 服务器的 /chat/ 路径
mkdir -p /var/www/html/chat/
cp -r dist/chat/* /var/www/html/chat/
```

**访问地址：** `https://example.com/chat/`

### 3. 多环境部署

可以同时构建多个环境的版本：

```bash
# 构建生产环境
BASE_URL=/prod/ npm run build

# 构建测试环境
BASE_URL=/test/ npm run build

# 构建开发环境
BASE_URL=/dev/ npm run build
```

然后部署到不同的路径：
```bash
cp -r dist/prod/* /var/www/html/prod/
cp -r dist/test/* /var/www/html/test/
cp -r dist/dev/* /var/www/html/dev/
```

## Web 服务器配置

### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name example.com;
    root /var/www/html;
    index index.html;

    # 根路径部署
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 子路径部署
    location /chat/ {
        alias /var/www/html/chat/;
        try_files $uri $uri/ /chat/index.html;
    }

    # API 代理（如果需要）
    location /chat-api/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### Apache 配置示例

```apache
<VirtualHost *:80>
    ServerName example.com
    DocumentRoot /var/www/html

    # 根路径部署
    <Directory "/var/www/html">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        # SPA 路由支持
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>

    # 子路径部署
    Alias /chat /var/www/html/chat
    <Directory "/var/www/html/chat">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /chat/index.html [L]
    </Directory>
</VirtualHost>
```

## Docker 部署

### Dockerfile 示例

```dockerfile
# 多阶段构建
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
ARG BASE_URL=/
ENV BASE_URL=$BASE_URL
RUN npm run build

# 生产镜像
FROM nginx:alpine

# 复制构建产物
ARG BASE_URL=/
RUN BASE_PATH=$(echo "$BASE_URL" | sed 's|^/||' | sed 's|/$||' || echo "root") && \
    mkdir -p /usr/share/nginx/html/$BASE_PATH
COPY --from=builder /app/dist/*/ /usr/share/nginx/html/

# 复制 Nginx 配置
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### Docker Compose 示例

```yaml
version: '3.8'

services:
  frontend:
    build:
      context: .
      args:
        BASE_URL: /chat/
    ports:
      - "80:80"
    environment:
      - BASE_URL=/chat/
```

## 自动化部署脚本

```bash
#!/bin/bash
# deploy.sh

set -e

BASE_URL=${1:-"/"}
DEPLOY_PATH=${2:-"/var/www/html"}

echo "🚀 开始部署前端应用..."
echo "📁 BASE_URL: $BASE_URL"
echo "📂 部署路径: $DEPLOY_PATH"

# 构建应用
echo "🔨 构建应用..."
BASE_URL="$BASE_URL" npm run build

# 计算目标目录
BASE_PATH=$(echo "$BASE_URL" | sed 's|^/||' | sed 's|/$||' || echo "root")
SOURCE_DIR="dist/$BASE_PATH"
TARGET_DIR="$DEPLOY_PATH"

if [ "$BASE_PATH" != "root" ]; then
    TARGET_DIR="$DEPLOY_PATH/$BASE_PATH"
fi

# 创建目标目录
echo "📁 创建目标目录: $TARGET_DIR"
mkdir -p "$TARGET_DIR"

# 部署文件
echo "📦 部署文件..."
cp -r "$SOURCE_DIR"/* "$TARGET_DIR"/

echo "✅ 部署完成！"
echo "🌐 访问地址: http://your-domain.com$BASE_URL"
```

## 注意事项

1. **路径一致性**: 确保构建时的 BASE_URL 与部署路径一致
2. **Web 服务器配置**: 正确配置 SPA 路由支持
3. **API 代理**: 如果前后端分离，需要配置 API 代理
4. **缓存策略**: 合理配置静态资源的缓存策略
5. **HTTPS**: 生产环境建议使用 HTTPS
