#!/bin/bash

# 构建脚本，支持自定义 BASE_URL
# 用法: ./scripts/build-with-base.sh [BASE_URL]

set -e

# 获取 BASE_URL 参数，默认为 /
BASE_URL=${1:-"/"}

echo "🚀 开始构建前端应用..."
echo "📁 BASE_URL: $BASE_URL"

# 设置环境变量并构建
export BASE_URL="$BASE_URL"

# 计算输出目录
BASE_PATH=$(echo "$BASE_URL" | sed 's|^/||' | sed 's|/$||')
if [ -z "$BASE_PATH" ]; then
    OUTPUT_DIR="dist"
else
    OUTPUT_DIR="dist/$BASE_PATH"
fi

# 清理之前的构建
echo "🧹 清理之前的构建..."
rm -rf dist

# 执行构建
echo "🔨 执行构建..."
npm run build

echo "✅ 构建完成！"
echo "📦 构建文件位于: ./$OUTPUT_DIR"
echo "🌐 应用基础路径: $BASE_URL"

# 显示构建结果
if [ -d "$OUTPUT_DIR" ]; then
    echo ""
    echo "📊 构建统计:"
    du -sh "$OUTPUT_DIR"
    echo ""
    echo "📄 主要文件:"
    find "$OUTPUT_DIR" -name "*.js" -o -name "*.css" -o -name "*.html" | head -10
fi
