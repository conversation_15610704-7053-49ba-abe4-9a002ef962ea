/**
 * 版本更新配置
 */

export interface UpdateConfig {
  // 是否启用版本更新检查
  enabled: boolean
  // 自动检查更新的间隔时间（毫秒）
  checkInterval: number
  // 是否在启动时自动检查更新
  autoCheckOnStartup: boolean
}

/**
 * 默认更新配置
 */
export const defaultUpdateConfig: UpdateConfig = {
  enabled: false, // 默认禁用版本更新
  checkInterval: 24 * 60 * 60 * 1000, // 24小时
  autoCheckOnStartup: false
}

/**
 * 从环境变量获取更新配置
 */
export function getUpdateConfig(): UpdateConfig {
  return {
    enabled: import.meta.env.VITE_UPDATE_CHECK_ENABLED === 'true' || false,
    checkInterval: parseInt(import.meta.env.VITE_UPDATE_CHECK_INTERVAL || '86400000'),
    autoCheckOnStartup: import.meta.env.VITE_UPDATE_AUTO_CHECK === 'true' || false
  }
}

/**
 * 检查是否启用版本更新
 */
export function isUpdateEnabled(): boolean {
  return getUpdateConfig().enabled
}
