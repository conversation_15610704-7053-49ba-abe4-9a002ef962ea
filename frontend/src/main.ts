import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'
import { useUserStore } from '@/stores/user'
import { handleError } from '@/utils/error-handler'

const app = createApp(App)

app.use(createPinia())
app.use(router)

// 初始化用户状态
const userStore = useUserStore()
userStore.loadUserFromStorage()

// 全局错误处理 - 只记录日志，不显示提示
app.config.errorHandler = (err, instance, info) => {
  console.error('Vue全局错误:', err, info)
  // 只记录日志，不显示错误提示
}

// 全局未捕获的Promise错误处理 - 只处理网络请求错误
window.addEventListener('unhandledrejection', (event) => {
  console.error('未捕获的Promise错误:', event.reason)

  // 只处理网络请求相关的错误
  const reason = event.reason
  const isNetworkError = reason && (
    reason.message?.includes('fetch') ||
    reason.message?.includes('network') ||
    reason.message?.includes('请求失败') ||
    reason.name === 'TypeError' && reason.message?.includes('Failed to fetch')
  )

  if (isNetworkError) {
    handleError(reason, {
      showMessage: true,
      showDialog: false,
      logError: true,
      defaultMessage: '网络请求失败'
    })
  }

  // 阻止默认的错误处理
  event.preventDefault()
})

app.mount('#app')


const oldFetch = window.fetch
window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {    
    let response = await oldFetch(input, init)
    if(router?.currentRoute?.value?.name != 'login' &&   router?.currentRoute?.value?.name!='oauth-callback' ) {
        if(response.status == 401) {
            router.push('/login')
        }
    }
    
    return response
}