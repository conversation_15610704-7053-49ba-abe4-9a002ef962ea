import { http } from '@/utils/http'

// 聊天消息接口
export interface ChatMessage {
  id: string
  content: string
  role: 'user' | 'assistant' | 'system'
  timestamp: number
  session_id?: string
  metadata?: Record<string, any>
}

// 聊天会话接口
export interface ChatSession {
  id: string
  title: string
  created_at: number
  updated_at: number
  message_count: number
  last_message?: string
}

// 发送消息请求
export interface SendMessageRequest {
  message: string
  session_id?: string
  user_id?: string
  username?: string
  metadata?: Record<string, any>
}

// 发送消息响应
export interface SendMessageResponse {
  message_id: string
  session_id: string
  status: 'sent' | 'processing' | 'completed' | 'error'
  response?: ChatMessage
}

// 聊天响应轮询
export interface ChatResponsePoll {
  message_id: string
  status: 'processing' | 'completed' | 'error'
  response?: ChatMessage
  error?: string
}

export const chatApi = {
  /**
   * 发送聊天消息
   */
  sendMessage(request: SendMessageRequest) {
    // 使用 v1 接口，会被路径重写中间件处理
    return http.post<any>('/v1/chat', request)
  },

  /**
   * 发送消息 (v2 接口)
   */
  sendMessageV2(request: SendMessageRequest) {
    return http.fetch('/v2/chat', {
      method: 'POST',
      body: JSON.stringify(request)
    }).then(res => res.json())
  },

  /**
   * 轮询消息响应
   */
  pollResponse(messageId: string) {
    return http.fetch(`/v2/chat/response/${messageId}`, {
      method: 'GET'
    }).then(res => res.json())
  },

  /**
   * 获取会话历史
   */
  getSessionHistory(sessionId: string) {
    return http.get<{ messages: ChatMessage[] }>(`/web/history/${sessionId}`)
  },

  /**
   * 获取所有会话列表
   */
  getSessions() {
    return http.get<{ sessions: ChatSession[] }>('/web/sessions')
  },

  /**
   * 创建新会话
   */
  createSession(title?: string) {
    return http.post<{ session: ChatSession }>('/web/sessions', { title })
  },

  /**
   * 删除会话
   */
  deleteSession(sessionId: string) {
    return http.delete(`/web/sessions/${sessionId}`)
  },

  /**
   * 清除会话历史
   */
  clearSession(sessionId: string) {
    return http.delete(`/web/history/${sessionId}`)
  },

  /**
   * 更新会话标题
   */
  updateSessionTitle(sessionId: string, title: string) {
    return http.put<{ session: ChatSession }>(`/web/sessions/${sessionId}`, { title })
  }
}
