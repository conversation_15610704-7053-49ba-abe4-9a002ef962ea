/**
 * 用户向导API服务
 */

import { request } from '@/utils/request'

export interface UserGuideStep {
  id?: number
  user_id: string
  step_key: string
  completed: boolean
  completed_at?: string
  metadata?: any
  created_at?: string
  updated_at?: string
}

export interface UserGuideSettings {
  id?: number
  user_id: string
  hide_guide: boolean
  settings?: any
  created_at?: string
  updated_at?: string
}

export interface UserGuideStepsResponse {
  success: boolean
  data: {
    user_id: string
    steps: Record<string, boolean>
  }
}

export interface UserGuideSettingsResponse {
  success: boolean
  data: UserGuideSettings
}

export interface ApiResponse {
  success: boolean
  message?: string
  error?: string
}

/**
 * 获取用户向导步骤状态
 */
export function getUserGuideSteps(userId: string = 'default'): Promise<UserGuideStepsResponse> {
  return request({
    url: '/web/user-guide/steps',
    method: 'GET',
    params: { user_id: userId }
  })
}

/**
 * 更新向导步骤状态
 */
export function updateGuideStep(
  stepKey: string,
  completed: boolean,
  userId: string = 'default',
  metadata?: any
): Promise<ApiResponse> {
  return request({
    url: `/web/user-guide/steps/${stepKey}`,
    method: 'PUT',
    data: {
      completed,
      user_id: userId,
      metadata
    }
  })
}

/**
 * 获取用户向导设置
 */
export function getUserGuideSettings(userId: string = 'default'): Promise<UserGuideSettingsResponse> {
  return request({
    url: '/web/user-guide/settings',
    method: 'GET',
    params: { user_id: userId }
  })
}

/**
 * 更新用户向导设置
 */
export function updateUserGuideSettings(
  hideGuide?: boolean,
  settings?: any,
  userId: string = 'default'
): Promise<ApiResponse> {
  return request({
    url: '/web/user-guide/settings',
    method: 'PUT',
    data: {
      user_id: userId,
      hide_guide: hideGuide,
      settings
    }
  })
}

/**
 * 获取用户的所有向导步骤详细信息
 */
export function getAllGuideSteps(userId: string = 'default'): Promise<{
  success: boolean
  data: {
    user_id: string
    steps: UserGuideStep[]
  }
}> {
  return request({
    url: '/web/user-guide/steps/all',
    method: 'GET',
    params: { user_id: userId }
  })
}

/**
 * 重置用户向导
 */
export function resetUserGuide(userId: string = 'default'): Promise<ApiResponse> {
  return request({
    url: '/web/user-guide/reset',
    method: 'POST',
    data: { user_id: userId }
  })
}

/**
 * 批量更新向导步骤
 */
export function batchUpdateGuideSteps(
  steps: Record<string, boolean>,
  userId: string = 'default'
): Promise<ApiResponse & {
  success_count?: number
  total_count?: number
}> {
  return request({
    url: '/web/user-guide/batch-update',
    method: 'POST',
    data: {
      user_id: userId,
      steps
    }
  })
}
