import { http } from '@/utils/http'

export interface UserInfo {
  id: string
  username: string
  email?: string
  name?: string
  avatar?: string
  roles?: string[]
  permissions?: string[]
}

export interface LoginResponse {
  access_token: string
  user_info?: UserInfo
}

export interface OAuthConfig {
  client_id: string
  redirect_uri: string
  authorize_url: string
  scope?: string
  response_type?: string
}

export const authApi = {
  /**
   * 检查是否首次访问
   */
  checkFirstTime() {
    return http.get<{ is_first_time: boolean }>('/auth/check-first-time')
  },

  /**
   * 登录或设置密码（旧版本，保留兼容性）
   */
  login(password: string) {
    return http.post<LoginResponse>('/auth/login', { password })
  },

  /**
   * 获取 OAuth2 配置
   */
  getOAuthConfig() {
    return http.get<OAuthConfig>('/auth/oauth/config')
  },

  /**
   * 使用授权码换取访问令牌
   */
  exchangeToken(code: string, state?: string) {
    return http.post<LoginResponse>('/auth/oauth/token', { code, state })
  },

  /**
   * 获取当前用户信息
   */
  getUserInfo() {
    return http.get<UserInfo>('/auth/oauth/userinfo')
  },

  /**
   * 获取当前用户详细信息（页面刷新时使用）
   */
  getMe() {
    return http.get<UserInfo>('/auth/oauth/me')
  },

  /**
   * 生成 OAuth2 授权 URL（异步获取配置）
   */
  async generateAuthUrl(state?: string): Promise<string> {
    try {
      // 从后端获取 OAuth 配置
      const config = await this.getOAuthConfig()

      const params = new URLSearchParams({
        client_id: config.client_id,
        redirect_uri: config.redirect_uri,
        response_type: config.response_type || 'code',
        scope: config.scope || 'openid profile email'
      })

      if (state) {
        params.append('state', state)
      }

      return `${config.authorize_url}?${params.toString()}`
    } catch (error) {
      console.error('获取 OAuth 配置失败:', error)
      throw new Error('无法生成授权 URL')
    }
  }
}