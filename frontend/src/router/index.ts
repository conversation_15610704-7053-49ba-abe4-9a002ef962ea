import { createRouter, createWebHistory } from 'vue-router'
import AppLayout from '@/layouts/AppLayout.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: AppLayout,
      children: [
        {
          path: '',
          redirect: '/guide'
        },
        {
          path: '/console',
          name: 'console',
          component: () => import('@/views/console/Console.vue')
        },
        {
          path: '/im',
          name: 'im',
          component: () => import('@/views/im/IMView.vue')
        },
        {
          path: '/im/adapters/:adapterType',
          name: 'im-adapter-detail',
          component: () => import('@/views/im/IMAdapterDetail.vue')
        },
        {
          path: '/im/platforms',
          name: 'im-platforms',
          component: () => import('@/views/workflow/WorkflowTemplates.vue'),
        },
        {
          path: '/llm',
          name: 'llm',
          component: () => import('@/views/llm/LLMView.vue')
        },
        {
          path: '/llm/backends',
          name: 'llm-backends',
          component: () => import('@/views/workflow/WorkflowTemplates.vue')
        },
        {
          path: '/llm/models',
          name: 'llm-models',
          component: () => import('@/views/workflow/WorkflowTemplates.vue')
        },
        {
          path: '/llm/chat',
          name: 'llm-chat',
          component: () => import('@/views/chat/ChatView.vue')
        },
        {
          path: '/chat',
          name: 'chat',
          component: () => import('@/views/chat/ChatView.vue')
        },
        {
          path: '/workflow',
          name: 'workflow',
          component: () => import('@/views/workflow/WorkflowList.vue')
        },
        {
          path: '/workflow/templates',
          name: 'workflow-templates',
          component: () => import('@/views/workflow/WorkflowTemplates.vue')
        },
        {
          path: '/workflow/dispatch-rules',
          name: 'workflow-dispatch-rules',
          component: () => import('@/views/workflow/DispatchRules.vue')
        },
        {
          path: '/workflow/editor/:id?',
          name: 'workflow-editor',
          component: () => import('@/views/workflow/WorkflowEditor.vue')
        },
        {
          path: '/plugins',
          name: 'plugins',
          component: () => import('@/views/plugins/PluginMarket.vue')
        },
        {
          path: '/plugins/market',
          name: 'plugin-market',
          component: () => import('@/views/plugins/PluginMarket.vue')
        },
        {
          path: '/memory',
          name: 'memory',
          component: () => import('@/views/workflow/WorkflowTemplates.vue')
        },
        {
          path: '/memory/search',
          name: 'memory-search',
          component: () => import('@/views/workflow/WorkflowTemplates.vue')
        },
        {
          path: '/guide',
          name: 'guide',
          component: () => import('@/views/guide/GuideView.vue')
        },
        {
          path: '/settings',
          name: 'settings',
          component: () => import('@/views/settings/BasicSettings.vue')
        },
        {
          path: '/media',
          name: 'media',
          component: () => import('@/views/media/MediaList.vue')
        },
        {
          path: '/tracing',
          name: 'tracing',
          meta: {
            title: '系统追踪',
            requiresAuth: true,
          },
          children: [
            {
              path: '',
              name: 'tracing-default',
              redirect: '/tracing/llm'
            },
            {
              path: 'llm',
              name: 'llm-tracing',
              component: () => import('@/views/tracing/llm/LLMTraceList.vue'),
              meta: {
                title: 'LLM请求追踪',
                requiresAuth: true
              }
            },
            {
              path: 'llm/detail/:traceId',
              name: 'llm-trace-detail',
              component: () => import('@/views/tracing/llm/LLMTraceDetail.vue'),
              meta: {
                title: 'LLM请求详情',
                requiresAuth: true
              }
            }
          ]
        }
      ]
    },
    {
      path: '/test/error',
      name: 'error-test',
      component: () => import('@/views/test/ErrorTest.vue'),
      meta: { title: '错误处理测试' }
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/login/LoginView.vue')
    },
    {
      path: '/callback',
      name: 'oauth-callback',
      component: () => import('@/views/login/OAuthCallback.vue')
    }
  ]
})

// 路由守卫
router.beforeEach(async (to, _from, next) => {
  const token = localStorage.getItem('token')
  console.log('路由跳转:', to.name)

  // 允许登录页面和 OAuth 回调页面无需认证
  const publicRoutes = ['login', 'oauth-callback']

  // 如果用户访问登录页，清理旧的认证信息
  if (to.name === 'login') {
    try {
      const { clearAuthOnLogin } = await import('@/utils/auth-cleanup')
      clearAuthOnLogin()
    } catch (error) {
      console.error('清理认证信息失败:', error)
    }

    next()
    return
  }

  if (!publicRoutes.includes(to.name as string) && !token) {
    next({ name: 'login' })
    return
  }

  // 如果有 token 但是需要刷新用户信息
  if (token && !publicRoutes.includes(to.name as string)) {
    try {
      // 动态导入用户状态管理
      const { useUserStore } = await import('@/stores/user')
      const userStore = useUserStore()

      // 如果没有用户信息，尝试从服务器获取
      if (!userStore.isLoggedIn) {
        console.log('刷新用户信息...')
        const success = await userStore.refreshUserInfo()
        if (!success) {
          console.log('用户信息刷新失败，跳转到登录页')
          next({ name: 'login' })
          return
        }
      }
    } catch (error) {
      console.error('用户信息验证失败:', error)
      next({ name: 'login' })
      return
    }
  }

  next()
})

export default router
