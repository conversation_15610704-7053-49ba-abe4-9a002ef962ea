import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { UserInfo } from '@/api/auth'
import { authApi } from '@/api/auth'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref<UserInfo | null>(null)
  const isLoggedIn = ref(false)
  const loading = ref(false)

  // 计算属性
  const userName = computed(() => userInfo.value?.name || userInfo.value?.username || '未知用户')
  const userAvatar = computed(() => userInfo.value?.avatar || '')
  const userRoles = computed(() => userInfo.value?.roles || [])
  const userPermissions = computed(() => userInfo.value?.permissions || [])

  // 方法
  const setUser = (user: UserInfo) => {
    userInfo.value = user
    isLoggedIn.value = true
    // 保存到 localStorage
    localStorage.setItem('user_info', JSON.stringify(user))
  }

  const clearUser = () => {
    userInfo.value = null
    isLoggedIn.value = false
    // 清除 localStorage
    localStorage.removeItem('user_info')
    localStorage.removeItem('token')
  }

  const loadUserFromStorage = () => {
    const token = localStorage.getItem('token')
    const storedUser = localStorage.getItem('user_info')
    
    if (token && storedUser) {
      try {
        const user = JSON.parse(storedUser)
        userInfo.value = user
        isLoggedIn.value = true
      } catch (error) {
        console.error('解析用户信息失败:', error)
        clearUser()
      }
    }
  }

  const refreshUserInfo = async () => {
    const token = localStorage.getItem('token')
    if (!token) {
      clearUser()
      return false
    }

    try {
      loading.value = true
      const user = await authApi.getMe()
      setUser(user)
      return true
    } catch (error) {
      console.error('刷新用户信息失败:', error)
      clearUser()
      return false
    } finally {
      loading.value = false
    }
  }

  const logout = () => {
    console.log('执行用户登出...')

    // 清除用户状态
    clearUser()

    // 清除所有可能的认证相关数据
    localStorage.removeItem('oauth_state')
    localStorage.removeItem('refresh_token')

    // 清除会话存储
    sessionStorage.clear()

    console.log('用户登出完成')

    // 可以在这里添加调用后端登出接口的逻辑
    // await authApi.logout()
  }

  // 检查用户是否有特定权限
  const hasPermission = (permission: string) => {
    return userPermissions.value.includes(permission)
  }

  // 检查用户是否有特定角色
  const hasRole = (role: string) => {
    return userRoles.value.includes(role)
  }

  return {
    // 状态
    userInfo,
    isLoggedIn,
    loading,
    
    // 计算属性
    userName,
    userAvatar,
    userRoles,
    userPermissions,
    
    // 方法
    setUser,
    clearUser,
    loadUserFromStorage,
    refreshUserInfo,
    logout,
    hasPermission,
    hasRole
  }
})
