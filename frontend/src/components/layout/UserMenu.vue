<template>
  <div class="user-menu">
    <n-dropdown
      :options="dropdownOptions"
      @select="handleSelect"
      trigger="click"
      placement="bottom-end"
    >
      <div class="user-info-trigger">
        <n-avatar
          round
          :size="32"
          :src="userStore.userAvatar"
          :fallback-src="defaultAvatar"
        >
          {{ userStore.userName.charAt(0).toUpperCase() }}
        </n-avatar>
        <span class="username">{{ userStore.userName }}</span>
        <n-icon size="16" class="dropdown-icon">
          <ChevronDownOutline />
        </n-icon>
      </div>
    </n-dropdown>
  </div>
</template>

<script setup lang="ts">
import { computed, h } from 'vue'
import { useRouter } from 'vue-router'
import { 
  NDropdown, 
  NAvatar, 
  NIcon, 
  useMessage,
  type DropdownOption 
} from 'naive-ui'
import { 
  ChevronDownOutline,
  PersonOutline,
  SettingsOutline,
  LogOutOutline,
  InformationCircleOutline
} from '@vicons/ionicons5'
import { useUserStore } from '@/stores/user'
import { clearAuthOnLogout } from '@/utils/auth-cleanup'

const router = useRouter()
const message = useMessage()
const userStore = useUserStore()

// 默认头像
const defaultAvatar = '/android-chrome-192x192.png'

// 渲染图标的辅助函数
const renderIcon = (icon: any) => {
  return () => h(NIcon, null, { default: () => h(icon) })
}

// 下拉菜单选项
const dropdownOptions = computed<DropdownOption[]>(() => [
  // {
  //   label: '个人信息',
  //   key: 'profile',
  //   icon: renderIcon(PersonOutline)
  // },
  // {
  //   label: '设置',
  //   key: 'settings',
  //   icon: renderIcon(SettingsOutline)
  // },
  // {
  //   type: 'divider',
  //   key: 'divider1'
  // },
  // {
  //   label: '关于',
  //   key: 'about',
  //   icon: renderIcon(InformationCircleOutline)
  // },
  // {
  //   type: 'divider',
  //   key: 'divider2'
  // },
  {
    label: '退出登录',
    key: 'logout',
    icon: renderIcon(LogOutOutline),
    props: {
      style: 'color: #ff4757;'
    }
  }
])

// 处理菜单选择
const handleSelect = (key: string) => {
  switch (key) {
    case 'profile':
      // 跳转到个人信息页面
      message.info('个人信息功能开发中...')
      break
    case 'settings':
      // 跳转到设置页面
      router.push('/settings')
      break
    case 'about':
      // 显示关于信息
      message.info('Kirara AI Chat v1.0.0')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 处理退出登录
const handleLogout = () => {
  try {
    // 使用统一的清理工具
    clearAuthOnLogout()

    // 显示退出成功消息
    message.success('已成功退出登录')

    // 跳转到登录页面
    router.push('/login')
  } catch (error) {
    console.error('退出登录失败:', error)
    message.error('退出登录失败，请重试')
  }
}
</script>

<style scoped>
.user-menu {
  display: flex;
  align-items: center;
  height: 100%;
}

.user-info-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.user-info-trigger:hover {
  background-color: var(--hover-color, rgba(0, 0, 0, 0.05));
}

.username {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-icon {
  color: var(--text-color-secondary);
  transition: transform 0.2s ease;
}

.user-info-trigger:hover .dropdown-icon {
  transform: rotate(180deg);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .username {
    display: none;
  }
  
  .user-info-trigger {
    padding: 6px;
  }
}
</style>
