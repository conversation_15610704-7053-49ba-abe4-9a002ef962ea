/**
 * 全局错误处理工具
 */

import { createDiscreteApi } from 'naive-ui'

// 创建独立的API实例
const { message, dialog } = createDiscreteApi(['message', 'dialog'])

/**
 * 错误类型定义
 */
export interface ApiError {
  status?: number
  message?: string
  error?: string
  detail?: string
  code?: string
}

/**
 * 错误处理配置
 */
export interface ErrorHandlerConfig {
  showMessage?: boolean  // 是否显示消息提示
  showDialog?: boolean   // 是否显示对话框
  logError?: boolean     // 是否记录错误日志
  defaultMessage?: string // 默认错误消息
}

/**
 * 默认错误处理配置
 */
const defaultConfig: ErrorHandlerConfig = {
  showMessage: true,
  showDialog: false,
  logError: true,
  defaultMessage: '操作失败，请稍后重试'
}

/**
 * 判断是否是后端API错误
 */
export function isBackendApiError(error: any): boolean {
  // 如果是字符串且包含HTTP状态码信息
  if (typeof error === 'string' && error.includes('请求失败 (')) {
    return true
  }

  // 如果是Error对象且消息包含HTTP状态码信息
  if (error instanceof Error && error.message.includes('请求失败 (')) {
    return true
  }

  // 如果是API错误响应对象
  if (error && typeof error === 'object') {
    // 有状态码的错误
    if (error.status && typeof error.status === 'number') {
      return true
    }

    // 有后端错误字段的错误
    if (error.error || error.message || error.detail) {
      return true
    }
  }

  // 网络错误
  if (error instanceof Error && (
    error.name === 'TypeError' && error.message.includes('fetch') ||
    error.message.includes('network') ||
    error.message.includes('网络')
  )) {
    return true
  }

  return false
}

/**
 * 提取错误消息
 */
export function extractErrorMessage(error: any): string {
  // 如果是字符串，直接返回
  if (typeof error === 'string') {
    return error
  }

  // 如果是Error对象
  if (error instanceof Error) {
    return error.message
  }

  // 如果是API错误响应
  if (error && typeof error === 'object') {
    // 尝试从不同字段提取错误消息
    const message = error.message || error.error || error.detail || error.msg
    if (message) {
      return typeof message === 'string' ? message : JSON.stringify(message)
    }

    // 如果有状态码，生成相应的错误消息
    if (error.status) {
      switch (error.status) {
        case 400:
          return '请求参数错误'
        case 401:
          return '未授权访问'
        case 403:
          return '权限不足'
        case 404:
          return '请求的资源不存在'
        case 500:
          return '服务器内部错误'
        case 502:
          return '网关错误'
        case 503:
          return '服务暂时不可用'
        default:
          return `请求失败 (${error.status})`
      }
    }
  }

  return '未知错误'
}

/**
 * 全局错误处理器
 */
export function handleError(error: any, config: ErrorHandlerConfig = {}) {
  const finalConfig = { ...defaultConfig, ...config }

  // 只处理后端API错误，忽略前端代码错误
  if (!isBackendApiError(error)) {
    // 前端代码错误只记录日志，不显示提示
    if (finalConfig.logError) {
      console.error('前端错误（不显示提示）:', {
        error,
        timestamp: new Date().toISOString()
      })
    }
    return ''
  }

  // 提取错误消息
  const errorMessage = extractErrorMessage(error) || finalConfig.defaultMessage!

  // 记录错误日志
  if (finalConfig.logError) {
    console.error('后端API错误:', {
      error,
      message: errorMessage,
      timestamp: new Date().toISOString()
    })
  }

  // 显示消息提示
  if (finalConfig.showMessage) {
    message.error(errorMessage)
  }

  // 显示对话框
  if (finalConfig.showDialog) {
    dialog.error({
      title: '错误',
      content: errorMessage,
      positiveText: '确定'
    })
  }

  return errorMessage
}

/**
 * 处理API错误的便捷方法（只处理后端API错误）
 */
export function handleApiError(error: any, defaultMessage?: string) {
  // 只处理后端API错误
  if (!isBackendApiError(error)) {
    console.error('非API错误，不显示提示:', error)
    return ''
  }

  return handleError(error, {
    showMessage: true,
    showDialog: false,
    logError: true,
    defaultMessage
  })
}

/**
 * 处理严重错误的便捷方法（显示对话框，只处理后端API错误）
 */
export function handleCriticalError(error: any, defaultMessage?: string) {
  // 只处理后端API错误
  if (!isBackendApiError(error)) {
    console.error('非API错误，不显示对话框:', error)
    return ''
  }

  return handleError(error, {
    showMessage: false,
    showDialog: true,
    logError: true,
    defaultMessage
  })
}

/**
 * 异步操作错误处理装饰器
 */
export function withErrorHandler<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  config?: ErrorHandlerConfig
): T {
  return (async (...args: any[]) => {
    try {
      return await fn(...args)
    } catch (error) {
      handleError(error, config)
      throw error
    }
  }) as T
}

/**
 * 创建带错误处理的异步函数
 */
export function createAsyncHandler<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  config?: ErrorHandlerConfig
) {
  return withErrorHandler(fn, config)
}
