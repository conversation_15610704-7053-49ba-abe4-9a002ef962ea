/**
 * 认证清理工具
 * 提供统一的认证信息清理功能
 */

import { useUserStore } from '@/stores/user'

/**
 * 清理所有认证相关的数据
 * @param reason 清理原因，用于日志记录
 */
export function clearAllAuthData(reason: string = '未知原因') {
  console.log(`清理认证数据 - 原因: ${reason}`)
  
  // 清理 localStorage 中的认证数据
  const authKeys = [
    'token',
    'user_info', 
    'oauth_state',
    'refresh_token',
    'access_token'
  ]
  
  authKeys.forEach(key => {
    if (localStorage.getItem(key)) {
      console.log(`清理 localStorage.${key}`)
      localStorage.removeItem(key)
    }
  })
  
  // 清理 sessionStorage
  if (sessionStorage.length > 0) {
    console.log('清理 sessionStorage')
    sessionStorage.clear()
  }
  
  // 清理用户状态
  try {
    const userStore = useUserStore()
    if (userStore.isLoggedIn) {
      console.log('清理用户状态')
      userStore.clearUser()
    }
  } catch (error) {
    console.error('清理用户状态失败:', error)
  }
  
  console.log('认证数据清理完成')
}

/**
 * 检查是否有残留的认证数据
 * @returns 是否有残留数据
 */
export function hasAuthData(): boolean {
  const authKeys = ['token', 'user_info', 'oauth_state', 'refresh_token', 'access_token']
  
  // 检查 localStorage
  const hasLocalStorageData = authKeys.some(key => localStorage.getItem(key) !== null)
  
  // 检查 sessionStorage
  const hasSessionStorageData = sessionStorage.length > 0
  
  // 检查用户状态
  let hasUserStoreData = false
  try {
    const userStore = useUserStore()
    hasUserStoreData = userStore.isLoggedIn
  } catch (error) {
    console.error('检查用户状态失败:', error)
  }
  
  return hasLocalStorageData || hasSessionStorageData || hasUserStoreData
}

/**
 * 强制清理所有认证数据（用于调试）
 */
export function forceCleanAuth() {
  console.log('强制清理所有认证数据...')
  
  // 清理所有 localStorage
  Object.keys(localStorage).forEach(key => {
    if (key.includes('token') || key.includes('auth') || key.includes('user') || key.includes('oauth')) {
      console.log(`强制清理 localStorage.${key}`)
      localStorage.removeItem(key)
    }
  })
  
  // 清理所有 sessionStorage
  sessionStorage.clear()
  
  // 清理用户状态
  try {
    const userStore = useUserStore()
    userStore.clearUser()
  } catch (error) {
    console.error('强制清理用户状态失败:', error)
  }
  
  console.log('强制清理完成')
}

/**
 * 在登录页面使用的清理函数
 */
export function clearAuthOnLogin() {
  clearAllAuthData('进入登录页面')
}

/**
 * 在登出时使用的清理函数
 */
export function clearAuthOnLogout() {
  clearAllAuthData('用户主动登出')
}

/**
 * 在令牌过期时使用的清理函数
 */
export function clearAuthOnTokenExpired() {
  clearAllAuthData('令牌过期')
}
