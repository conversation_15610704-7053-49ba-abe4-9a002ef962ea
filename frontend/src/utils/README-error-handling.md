# 前端错误处理系统

## 概述

本项目实现了一套智能的前端错误处理系统，**只处理后端API错误**，不会因为前端代码错误而打扰用户。系统能够自动识别错误类型，只对后端返回的错误显示友好的提示。

## 功能特性

### 1. 智能错误识别
- **只处理后端API错误**: 自动识别并只处理来自后端的错误
- **忽略前端代码错误**: 前端JavaScript错误只记录日志，不显示提示
- **网络错误处理**: 自动处理网络连接失败等问题

### 2. 后端错误类型识别
- **HTTP状态码错误**: 400、401、404、500等
- **API响应错误**: 包含error、message、detail字段的响应
- **网络连接错误**: fetch失败、连接超时等

### 3. 多级错误处理
- **静默处理**: 只记录日志，不显示提示
- **标准处理**: 显示错误消息提示
- **关键处理**: 显示错误对话框

### 4. 智能错误消息提取
- 自动从不同格式的错误响应中提取错误消息
- 根据HTTP状态码生成相应的错误提示
- 支持自定义错误消息

## 使用方法

### 1. 基础用法

#### HTTP请求（自动错误处理）
```typescript
import { http } from '@/utils/http'

// 所有HTTP请求都会自动处理错误
try {
  const data = await http.get('/api/users')
  // 处理成功响应
} catch (error) {
  // 错误已经被全局处理器处理，这里只需要记录日志
  console.error('请求失败:', error)
}
```

#### 手动错误处理
```typescript
import { handleApiError, handleCriticalError } from '@/utils/error-handler'

// 标准错误处理（显示消息提示）
try {
  await someOperation()
} catch (error) {
  handleApiError(error, '操作失败，请稍后重试')
}

// 关键错误处理（显示对话框）
try {
  await criticalOperation()
} catch (error) {
  handleCriticalError(error, '关键操作失败')
}
```

### 2. API包装器

#### 不同级别的错误处理
```typescript
import { httpWrapper, ApiWrapper } from '@/utils/api-wrapper'

// 静默处理（不显示错误提示）
const result1 = await httpWrapper.get.silent('/api/optional-data')

// 标准处理（显示错误消息）
const result2 = await httpWrapper.get.standard('/api/important-data', {}, '获取数据失败')

// 关键处理（显示错误对话框）
const result3 = await httpWrapper.get.critical('/api/critical-data', {}, '关键数据获取失败')
```

#### 重试机制
```typescript
import { ApiWrapper } from '@/utils/api-wrapper'

// 自动重试失败的请求
const result = await ApiWrapper.retry(
  () => http.get('/api/unstable-endpoint'),
  3,        // 最大重试次数
  1000,     // 重试间隔（毫秒）
  '获取数据失败' // 默认错误消息
)
```

### 3. 错误处理装饰器

```typescript
import { withErrorHandler, createAsyncHandler } from '@/utils/error-handler'

// 为异步函数添加错误处理
const safeAsyncFunction = withErrorHandler(async () => {
  // 可能抛出错误的代码
  await riskyOperation()
}, {
  showMessage: true,
  showDialog: false,
  logError: true,
  defaultMessage: '操作失败'
})

// 创建带错误处理的API方法
const safeApiCall = createAsyncHandler(
  async (id: string) => http.get(`/api/users/${id}`),
  { showMessage: true }
)
```

## 错误类型区分

### 🟢 会显示提示的错误（后端API错误）
- **HTTP状态码错误**: 400、401、403、404、500等
- **API响应错误**: 包含error、message、detail字段的后端响应
- **网络连接错误**: fetch失败、连接超时、DNS解析失败等

### 🔴 不会显示提示的错误（前端代码错误）
- **JavaScript运行时错误**: TypeError、ReferenceError等
- **Vue组件错误**: 组件渲染错误、生命周期错误等
- **前端逻辑错误**: 访问undefined属性、类型转换错误等

> **设计理念**: 只有后端API错误才需要用户知晓，前端代码错误应该通过开发调试解决，不应该打扰用户。

## 错误类型和处理

### HTTP状态码错误
- **400**: 请求参数错误
- **401**: 未授权访问（自动跳转到登录页）
- **403**: 权限不足
- **404**: 请求的资源不存在
- **500**: 服务器内部错误
- **502**: 网关错误
- **503**: 服务暂时不可用

### 网络错误
- 连接超时
- 网络不可达
- DNS解析失败

### 应用错误
- Vue组件错误
- JavaScript运行时错误
- 未捕获的Promise错误

## 配置选项

### ErrorHandlerConfig
```typescript
interface ErrorHandlerConfig {
  showMessage?: boolean    // 是否显示消息提示（默认: true）
  showDialog?: boolean     // 是否显示对话框（默认: false）
  logError?: boolean       // 是否记录错误日志（默认: true）
  defaultMessage?: string  // 默认错误消息
}
```

## 最佳实践

### 1. 组件中的错误处理
```typescript
// ✅ 推荐：让全局错误处理器处理错误
const fetchData = async () => {
  try {
    const data = await http.get('/api/data')
    // 处理成功响应
  } catch (error) {
    // 只记录日志，错误提示由全局处理器显示
    console.error('获取数据失败:', error)
  }
}

// ❌ 不推荐：重复的错误处理
const fetchData = async () => {
  try {
    const data = await http.get('/api/data')
  } catch (error) {
    message.error('获取数据失败') // 重复的错误提示
    console.error('获取数据失败:', error)
  }
}
```

### 2. 选择合适的错误处理级别
- **静默处理**: 用于可选的、非关键的数据获取
- **标准处理**: 用于大多数用户操作
- **关键处理**: 用于重要的业务操作，需要用户明确知晓

### 3. 自定义错误消息
```typescript
// 为不同的操作提供有意义的错误消息
await httpWrapper.post.standard('/api/users', userData, {}, '创建用户失败')
await httpWrapper.delete.critical('/api/users/123', {}, '删除用户失败，请联系管理员')
```

## 测试

访问 `/test/error` 页面可以测试各种错误处理场景。

## 注意事项

1. 全局错误处理器会自动处理所有HTTP请求错误
2. 避免在组件中重复显示错误提示
3. 对于关键操作，使用对话框级别的错误处理
4. 始终在catch块中记录错误日志，便于调试
5. 为用户操作提供有意义的错误消息
