/**
 * 错误处理测试工具
 */

import { handleApiError, isBackendApiError } from './error-handler'

/**
 * 测试错误类型识别
 */
export function testErrorTypeDetection() {
  console.log('=== 错误类型识别测试 ===')
  
  // 测试后端API错误（应该返回true）
  const backendErrors = [
    { status: 400, error: '请求参数错误' },
    { status: 500, message: '服务器内部错误' },
    { detail: '资源不存在' },
    new Error('请求失败 (404)'),
    '请求失败 (500)'
  ]
  
  console.log('后端API错误测试:')
  backendErrors.forEach((error, index) => {
    const isBackend = isBackendApiError(error)
    console.log(`  ${index + 1}. ${JSON.stringify(error)} -> ${isBackend ? '✅ 后端错误' : '❌ 非后端错误'}`)
  })
  
  // 测试前端代码错误（应该返回false）
  const frontendErrors = [
    new Error('Cannot read property of undefined'),
    new TypeError('obj.method is not a function'),
    new ReferenceError('variable is not defined'),
    'Some random error message',
    { someProperty: 'value' }
  ]
  
  console.log('\n前端代码错误测试:')
  frontendErrors.forEach((error, index) => {
    const isBackend = isBackendApiError(error)
    console.log(`  ${index + 1}. ${JSON.stringify(error)} -> ${isBackend ? '❌ 误判为后端错误' : '✅ 正确识别为前端错误'}`)
  })
}

/**
 * 测试错误处理行为
 */
export function testErrorHandlingBehavior() {
  console.log('\n=== 错误处理行为测试 ===')
  
  // 测试后端错误（应该显示提示）
  console.log('测试后端错误处理:')
  const backendError = { status: 400, error: '测试后端错误消息' }
  const result1 = handleApiError(backendError)
  console.log(`  后端错误处理结果: "${result1}" ${result1 ? '✅ 显示提示' : '❌ 未显示提示'}`)
  
  // 测试前端错误（不应该显示提示）
  console.log('\n测试前端错误处理:')
  const frontendError = new Error('测试前端JavaScript错误')
  const result2 = handleApiError(frontendError)
  console.log(`  前端错误处理结果: "${result2}" ${!result2 ? '✅ 未显示提示' : '❌ 错误显示了提示'}`)
}

/**
 * 运行所有测试
 */
export function runAllTests() {
  testErrorTypeDetection()
  testErrorHandlingBehavior()
  console.log('\n=== 测试完成 ===')
}
