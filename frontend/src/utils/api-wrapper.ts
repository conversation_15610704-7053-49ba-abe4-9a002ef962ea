/**
 * API包装器，提供统一的错误处理
 */

import { http } from './http'
import { handleApiError, handleCriticalError, withErrorHandler } from './error-handler'

/**
 * 包装API调用，提供不同级别的错误处理
 */
export class ApiWrapper {
  /**
   * 静默API调用 - 不显示错误提示，只记录日志
   */
  static async silent<T>(apiCall: () => Promise<T>): Promise<T | null> {
    try {
      return await apiCall()
    } catch (error) {
      console.error('静默API调用失败:', error)
      return null
    }
  }

  /**
   * 标准API调用 - 显示错误消息
   */
  static async standard<T>(apiCall: () => Promise<T>, defaultMessage?: string): Promise<T | null> {
    try {
      return await apiCall()
    } catch (error) {
      handleApiError(error, defaultMessage)
      return null
    }
  }

  /**
   * 关键API调用 - 显示错误对话框
   */
  static async critical<T>(apiCall: () => Promise<T>, defaultMessage?: string): Promise<T | null> {
    try {
      return await apiCall()
    } catch (error) {
      handleCriticalError(error, defaultMessage)
      return null
    }
  }

  /**
   * 重试API调用
   */
  static async retry<T>(
    apiCall: () => Promise<T>, 
    maxRetries: number = 3, 
    delay: number = 1000,
    defaultMessage?: string
  ): Promise<T | null> {
    let lastError: any
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await apiCall()
      } catch (error) {
        lastError = error
        if (i < maxRetries - 1) {
          console.warn(`API调用失败，${delay}ms后重试 (${i + 1}/${maxRetries}):`, error)
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }
    
    handleApiError(lastError, defaultMessage || `操作失败，已重试${maxRetries}次`)
    return null
  }
}

/**
 * 创建带错误处理的API方法
 */
export function createApiMethod<T extends (...args: any[]) => Promise<any>>(
  method: T,
  errorLevel: 'silent' | 'standard' | 'critical' = 'standard'
): T {
  return (async (...args: any[]) => {
    const apiCall = () => method(...args)
    
    switch (errorLevel) {
      case 'silent':
        return ApiWrapper.silent(apiCall)
      case 'critical':
        return ApiWrapper.critical(apiCall)
      default:
        return ApiWrapper.standard(apiCall)
    }
  }) as T
}

/**
 * HTTP方法的包装器
 */
export const httpWrapper = {
  /**
   * GET请求包装器
   */
  get: {
    silent: <T>(path: string, config?: any) => 
      ApiWrapper.silent(() => http.get<T>(path, config)),
    standard: <T>(path: string, config?: any, defaultMessage?: string) => 
      ApiWrapper.standard(() => http.get<T>(path, config), defaultMessage),
    critical: <T>(path: string, config?: any, defaultMessage?: string) => 
      ApiWrapper.critical(() => http.get<T>(path, config), defaultMessage),
  },

  /**
   * POST请求包装器
   */
  post: {
    silent: <T>(path: string, data?: any, config?: any) => 
      ApiWrapper.silent(() => http.post<T>(path, data, config)),
    standard: <T>(path: string, data?: any, config?: any, defaultMessage?: string) => 
      ApiWrapper.standard(() => http.post<T>(path, data, config), defaultMessage),
    critical: <T>(path: string, data?: any, config?: any, defaultMessage?: string) => 
      ApiWrapper.critical(() => http.post<T>(path, data, config), defaultMessage),
  },

  /**
   * PUT请求包装器
   */
  put: {
    silent: <T>(path: string, data?: any, config?: any) => 
      ApiWrapper.silent(() => http.put<T>(path, data, config)),
    standard: <T>(path: string, data?: any, config?: any, defaultMessage?: string) => 
      ApiWrapper.standard(() => http.put<T>(path, data, config), defaultMessage),
    critical: <T>(path: string, data?: any, config?: any, defaultMessage?: string) => 
      ApiWrapper.critical(() => http.put<T>(path, data, config), defaultMessage),
  },

  /**
   * DELETE请求包装器
   */
  delete: {
    silent: <T>(path: string, config?: any) => 
      ApiWrapper.silent(() => http.delete<T>(path, config)),
    standard: <T>(path: string, config?: any, defaultMessage?: string) => 
      ApiWrapper.standard(() => http.delete<T>(path, config), defaultMessage),
    critical: <T>(path: string, config?: any, defaultMessage?: string) => 
      ApiWrapper.critical(() => http.delete<T>(path, config), defaultMessage),
  }
}
