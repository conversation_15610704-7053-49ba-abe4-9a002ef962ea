/**
 * 请求工具函数
 */

import { http } from './http'

export interface RequestConfig {
  url: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  params?: Record<string, any>
  headers?: Record<string, string>
}

/**
 * 统一的请求函数
 */
export async function request<T = any>(config: RequestConfig): Promise<T> {
  const { url, method, data, params, headers } = config
  
  // 处理查询参数
  let finalUrl = url
  if (params && Object.keys(params).length > 0) {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value))
      }
    })
    finalUrl = `${url}?${searchParams.toString()}`
  }
  
  // 根据方法调用对应的http方法
  switch (method) {
    case 'GET':
      return http.get<T>(finalUrl, { headers })
    case 'POST':
      return http.post<T>(finalUrl, data, { headers })
    case 'PUT':
      return http.put<T>(finalUrl, data, { headers })
    case 'DELETE':
      return http.delete<T>(finalUrl, { headers })
    default:
      throw new Error(`Unsupported method: ${method}`)
  }
}
