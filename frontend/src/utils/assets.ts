/**
 * 资源路径工具函数
 * 处理动态 base 路径的资源引用
 */

/**
 * 获取资源的完整路径
 * @param path 相对于 public 目录的资源路径（不要以 / 开头）
 * @returns 包含 base 路径的完整资源路径
 */
export function getAssetUrl(path: string): string {
  // 确保路径不以 / 开头
  const cleanPath = path.startsWith('/') ? path.slice(1) : path
  
  // 使用 Vite 的 BASE_URL 环境变量
  return `${import.meta.env.BASE_URL}${cleanPath}`
}

/**
 * 获取图标资源路径
 * @param category 图标分类（如 'llm', 'im'）
 * @param name 图标名称
 * @param extension 文件扩展名（默认 'webp'）
 * @returns 完整的图标路径
 */
export function getIconUrl(category: string, name: string, extension: string = 'webp'): string {
  return getAssetUrl(`assets/icons/${category}/${name.toLowerCase()}.${extension}`)
}

/**
 * 获取 LLM 适配器图标路径
 * @param adapter 适配器名称
 * @returns LLM 图标路径
 */
export function getLLMIconUrl(adapter: string): string {
  return getIconUrl('llm', adapter, 'webp')
}

/**
 * 获取 IM 适配器图标路径
 * @param type IM 类型
 * @returns IM 图标路径
 */
export function getIMIconUrl(type: string): string {
  return getIconUrl('im', type, 'png')
}

/**
 * 获取 IM 适配器备用图标路径
 * @returns IM 备用图标路径
 */
export function getIMFallbackIconUrl(): string {
  return getIconUrl('im', 'fallback-im', 'svg')
}

/**
 * 获取静态资源路径
 * @param path 静态资源路径
 * @returns 完整的静态资源路径
 */
export function getStaticUrl(path: string): string {
  return getAssetUrl(path)
}

/**
 * 获取应用内页面路径（用于 window.open）
 * @param path 页面路径（不要以 / 开头）
 * @returns 包含 base 路径的完整页面路径
 */
export function getPageUrl(path: string): string {
  // 确保路径不以 / 开头
  const cleanPath = path.startsWith('/') ? path.slice(1) : path

  // 使用 Vite 的 BASE_URL 环境变量
  return `${import.meta.env.BASE_URL}${cleanPath}`
}
