/**
 * 版本处理测试工具
 */

import { version } from './version'

/**
 * 测试版本号标准化
 */
export function testVersionNormalization() {
  console.log('=== 版本号标准化测试 ===')
  
  const testCases = [
    // 正常版本号
    { input: '1.0.0', expected: '1.0.0' },
    { input: 'v1.0.0', expected: '1.0.0' },
    { input: '2.1.3', expected: '2.1.3' },
    
    // 开发版本号
    { input: 'dev-d3a351b', expected: '0.0.0-dev.d3a351b' },
    { input: 'dev-abc123', expected: '0.0.0-dev.abc123' },
    
    // 特殊情况
    { input: 'unknown', expected: '0.0.0' },
    { input: '', expected: '0.0.0' },
    { input: '1.0', expected: '1.0.0' },
    { input: '1', expected: '1.0.0' },
  ]
  
  testCases.forEach(({ input, expected }) => {
    const result = version.normalizeVersion(input)
    const status = result === expected ? '✅' : '❌'
    console.log(`  ${status} "${input}" -> "${result}" (期望: "${expected}")`)
  })
}

/**
 * 测试版本号比较
 */
export function testVersionComparison() {
  console.log('\n=== 版本号比较测试 ===')
  
  const testCases = [
    // 正常版本比较
    { v1: '2.0.0', v2: '1.0.0', expected: 1, desc: '2.0.0 > 1.0.0' },
    { v1: '1.0.0', v2: '2.0.0', expected: -1, desc: '1.0.0 < 2.0.0' },
    { v1: '1.0.0', v2: '1.0.0', expected: 0, desc: '1.0.0 = 1.0.0' },
    
    // 开发版本比较
    { v1: '1.0.0', v2: 'dev-d3a351b', expected: 1, desc: '1.0.0 > dev-d3a351b' },
    { v1: 'dev-d3a351b', v2: '1.0.0', expected: -1, desc: 'dev-d3a351b < 1.0.0' },
    { v1: 'dev-abc123', v2: 'dev-d3a351b', expected: 0, desc: 'dev版本之间比较' },
    
    // 特殊情况
    { v1: 'unknown', v2: '1.0.0', expected: -1, desc: 'unknown < 1.0.0' },
    { v1: '1.0.0', v2: 'unknown', expected: 1, desc: '1.0.0 > unknown' },
  ]
  
  testCases.forEach(({ v1, v2, expected, desc }) => {
    const result = version.compare(v1, v2)
    const status = result === expected ? '✅' : '❌'
    console.log(`  ${status} ${desc} -> ${result} (期望: ${expected})`)
  })
}

/**
 * 测试版本号验证
 */
export function testVersionValidation() {
  console.log('\n=== 版本号验证测试 ===')
  
  const testCases = [
    { input: '1.0.0', expected: true },
    { input: 'dev-d3a351b', expected: true },
    { input: 'unknown', expected: true },
    { input: '', expected: true },
    { input: 'invalid-version-format', expected: true }, // 会被标准化为 0.0.0
  ]
  
  testCases.forEach(({ input, expected }) => {
    const result = version.isValid(input)
    const status = result === expected ? '✅' : '❌'
    console.log(`  ${status} "${input}" -> ${result ? '有效' : '无效'} (期望: ${expected ? '有效' : '无效'})`)
  })
}

/**
 * 运行所有版本测试
 */
export function runAllVersionTests() {
  console.log('🧪 开始运行版本处理测试...')
  testVersionNormalization()
  testVersionComparison()
  testVersionValidation()
  console.log('\n=== 版本测试完成 ===')
}

/**
 * 测试当前环境的版本处理
 */
export function testCurrentEnvironment() {
  console.log('\n=== 当前环境版本测试 ===')
  
  const currentVersion = version.getCurrentVersion()
  const normalized = version.normalizeVersion(currentVersion)
  const isValid = version.isValid(currentVersion)
  
  console.log(`当前版本: "${currentVersion}"`)
  console.log(`标准化后: "${normalized}"`)
  console.log(`是否有效: ${isValid ? '✅ 有效' : '❌ 无效'}`)
  
  // 测试与一些常见版本的比较
  const testVersions = ['1.0.0', '2.0.0', '0.1.0']
  testVersions.forEach(testVer => {
    const comparison = version.compare(testVer, currentVersion)
    const relation = comparison > 0 ? '>' : comparison < 0 ? '<' : '='
    console.log(`${testVer} ${relation} ${currentVersion}`)
  })
}
