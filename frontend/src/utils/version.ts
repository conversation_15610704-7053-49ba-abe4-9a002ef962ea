import { compare as semverCompare, valid as semver<PERSON><PERSON>d, coerce as semver<PERSON><PERSON>rce } from 'semver'

export const version = {
  /**
   * 获取当前前端版本
   */
  getCurrentVersion(): string {
    return import.meta.env.VITE_APP_VERSION.replace(/^v/, '') || '0.0.0'
  },

  /**
   * 标准化版本号，处理开发版本等特殊格式
   */
  normalizeVersion(version: string): string {
    // 移除前缀 v
    version = version.replace(/^v/, '')

    // 如果是开发版本 (dev-xxxxx)，转换为 0.0.0-dev.xxxxx 格式
    if (version.startsWith('dev-')) {
      const hash = version.substring(4)
      return `0.0.0-dev.${hash}`
    }

    // 如果是 unknown 或空字符串，返回 0.0.0
    if (!version || version === 'unknown') {
      return '0.0.0'
    }

    // 尝试使用 semver.coerce 来标准化版本号
    const coerced = semverCoerce(version)
    if (coerced) {
      return coerced.version
    }

    // 如果无法标准化，返回 0.0.0
    return '0.0.0'
  },

  /**
   * 比较两个版本号
   * @param version1 版本号1
   * @param version2 版本号2
   * @returns 如果 version1 > version2 返回 1，如果 version1 < version2 返回 -1，如果相等返回 0
   */
  compare(version1: string, version2: string): number {
    try {
      // 标准化版本号
      const normalizedV1 = this.normalizeVersion(version1)
      const normalizedV2 = this.normalizeVersion(version2)

      // 验证版本号格式
      if (!semverValid(normalizedV1) || !semverValid(normalizedV2)) {
        console.warn(`版本号格式无效: ${version1} -> ${normalizedV1}, ${version2} -> ${normalizedV2}`)
        return 0
      }

      const result = semverCompare(normalizedV1, normalizedV2)
      return result === null ? 0 : result
    } catch (error) {
      console.error('版本号比较错误', { version1, version2, error })
      return 0
    }
  },

  /**
   * 检查版本号是否有效
   */
  isValid(version: string): boolean {
    const normalized = this.normalizeVersion(version)
    return semverValid(normalized) !== null
  }
}