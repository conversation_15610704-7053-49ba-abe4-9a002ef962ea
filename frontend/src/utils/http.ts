import { createDiscrete<PERSON>pi } from 'naive-ui'

const BASE_URL = '/chat-api/api'

// 创建独立的消息API实例，不依赖组件上下文
const { message } = createDiscreteApi(['message'])

class Http {

  async fetch(path: string, config: RequestInit): Promise<Response> {
    try {
      let actualPath = path
      let headers: Record<string, any> = {}
      if (!path.startsWith('http://') && !path.startsWith('https://')) {
        actualPath = `${BASE_URL}${path}`
        headers = {
          'Content-Type': 'application/json',
          ...headers,
          ...config.headers,
          ...this.getAuthHeader(),
        }
        config = {
          ...config,
          credentials: 'include'
        }
      }
      return await fetch(actualPath, {
        ...config,
        headers: headers,
      })
    } catch (error) {
      // 只处理网络错误，不处理其他类型的错误
      if (error instanceof Error && (error.name === 'TypeError' || error.message.includes('fetch'))) {
        const errorMessage = '网络连接失败，请检查网络状态'
        message.error(errorMessage)
        throw new Error(errorMessage)
      }

      // 其他错误只记录日志
      console.error('Fetch请求错误:', error)
      throw error
    }
  }

  private async request<T>(path: string, config: RequestInit): Promise<T> {
    try {
      let actualPath = path
      let headers: Record<string, any> = {}
      if (!path.startsWith('http://') && !path.startsWith('https://')) {
        actualPath = `${BASE_URL}${path}`
        headers = {
          'Content-Type': 'application/json',
          ...headers,
          ...config.headers,
          ...this.getAuthHeader(),
        }
        config = {
          ...config,
          credentials: 'include'
        }
      }
      const response = await fetch(actualPath, {
        ...config,
        headers: headers,
      })

      const data = await response.json()

      if (!response.ok) {
        const errorMessage = data.error || data.message || data.detail || `请求失败 (${response.status})`
        // 只显示后端返回的错误
        message.error(errorMessage)
        throw new Error(errorMessage)
      }

      return data as T
    } catch (error) {
      // 只处理网络错误和HTTP错误，不处理前端代码错误
      if (error instanceof Error) {
        // 如果是已经处理过的HTTP错误，直接抛出
        if (error.message.includes('请求失败 (')) {
          throw error
        }

        // 如果是网络错误（fetch失败、连接超时等）
        if (error.name === 'TypeError' || error.message.includes('fetch')) {
          const errorMessage = '网络连接失败，请检查网络状态'
          message.error(errorMessage)
          throw new Error(errorMessage)
        }
      }

      // 其他错误（如JSON解析错误等）只记录日志，不显示提示
      console.error('HTTP请求处理错误:', error)
      throw error
    }
  }

  getAuthToken(): string | null {
    return localStorage.getItem('token')
  }

  private getAuthHeader(): HeadersInit {
    const token = this.getAuthToken()
    return token ? { Authorization: `Bearer ${token}` } : {}
  }

  get<T>(path: string, config: Omit<RequestInit, 'method'> = {}) {
    return this.request<T>(path, { ...config, method: 'GET' })
  }

  post<T>(path: string, data?: any, config: Omit<RequestInit, 'method' | 'body'> = {}) {
    return this.request<T>(path, {
      ...config,
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  put<T>(path: string, data?: any, config: Omit<RequestInit, 'method' | 'body'> = {}) {
    return this.request<T>(path, {
      ...config,
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  delete<T>(path: string, config: Omit<RequestInit, 'method'> = {}) {
    return this.request<T>(path, { ...config, method: 'DELETE' })
  }

  url(path: string) {
    return `${BASE_URL}${path}`
  }

  // WebSocket URL 生成方法
  wsUrl(path: string) {
    // 获取当前协议和主机
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const host = window.location.host

    // 在开发环境中，使用代理路径
    if (import.meta.env.DEV) {
      return `${protocol}//${host}${BASE_URL}${path}`
    }

    // 生产环境中，直接使用当前主机
    return `${protocol}//${host}${BASE_URL}${path}`
  }
}

export const http = new Http() 