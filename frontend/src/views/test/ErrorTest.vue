<template>
  <div class="error-test-container">
    <n-card title="错误处理测试">
      <n-space vertical>
        <n-alert type="info">
          这个页面用于测试全局错误处理功能。点击下面的按钮来触发不同类型的错误。
        </n-alert>

        <n-divider>HTTP错误测试</n-divider>
        
        <n-space>
          <n-button @click="test400Error" type="warning">
            测试400错误
          </n-button>
          <n-button @click="test401Error" type="warning">
            测试401错误
          </n-button>
          <n-button @click="test404Error" type="warning">
            测试404错误
          </n-button>
          <n-button @click="test500Error" type="error">
            测试500错误
          </n-button>
        </n-space>

        <n-divider>网络错误测试</n-divider>
        
        <n-space>
          <n-button @click="testNetworkError" type="warning">
            测试网络错误
          </n-button>
          <n-button @click="testTimeoutError" type="warning">
            测试超时错误
          </n-button>
        </n-space>

        <n-divider>前端代码错误测试（不应显示提示）</n-divider>

        <n-space>
          <n-button @click="testJavaScriptError" type="warning">
            测试JavaScript错误
          </n-button>
          <n-button @click="testUndefinedError" type="warning">
            测试访问undefined属性
          </n-button>
          <n-button @click="testTypeError" type="warning">
            测试类型错误
          </n-button>
        </n-space>

        <n-divider>自定义错误测试</n-divider>

        <n-space>
          <n-button @click="testCustomError" type="info">
            测试自定义错误消息
          </n-button>
          <n-button @click="testSilentError" type="info">
            测试静默错误（不显示提示）
          </n-button>
          <n-button @click="testCriticalError" type="error">
            测试严重错误（显示对话框）
          </n-button>
        </n-space>

        <n-divider>API包装器测试</n-divider>
        
        <n-space>
          <n-button @click="testApiWrapperStandard" type="primary">
            测试标准API包装器
          </n-button>
          <n-button @click="testApiWrapperSilent" type="default">
            测试静默API包装器
          </n-button>
          <n-button @click="testApiWrapperCritical" type="error">
            测试关键API包装器
          </n-button>
        </n-space>

        <n-divider>自动化测试</n-divider>

        <n-space>
          <n-button @click="runAutomatedTests" type="primary">
            运行错误处理测试（查看控制台）
          </n-button>
          <n-button @click="runVersionTests" type="info">
            运行版本处理测试（查看控制台）
          </n-button>
        </n-space>

        <n-divider>结果显示</n-divider>

        <n-card v-if="lastResult" :title="`最后一次测试结果 (${lastTestType})`">
          <n-code :code="JSON.stringify(lastResult, null, 2)" language="json" />
        </n-card>
      </n-space>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { 
  NCard, 
  NButton, 
  NSpace, 
  NDivider, 
  NAlert, 
  NCode,
  useMessage 
} from 'naive-ui'
import { http } from '@/utils/http'
import { handleApiError, handleCriticalError } from '@/utils/error-handler'
import { ApiWrapper, httpWrapper } from '@/utils/api-wrapper'
import { runAllTests } from '@/utils/test-error-handling'

const message = useMessage()
const lastResult = ref<any>(null)
const lastTestType = ref<string>('')

// 设置测试结果
const setResult = (result: any, testType: string) => {
  lastResult.value = result
  lastTestType.value = testType
}

// HTTP错误测试
const test400Error = async () => {
  try {
    await http.post('/test/400-error', { invalid: 'data' })
  } catch (error) {
    setResult(error, '400错误测试')
  }
}

const test401Error = async () => {
  try {
    await http.get('/test/401-error')
  } catch (error) {
    setResult(error, '401错误测试')
  }
}

const test404Error = async () => {
  try {
    await http.get('/test/non-existent-endpoint')
  } catch (error) {
    setResult(error, '404错误测试')
  }
}

const test500Error = async () => {
  try {
    await http.post('/test/500-error')
  } catch (error) {
    setResult(error, '500错误测试')
  }
}

// 网络错误测试
const testNetworkError = async () => {
  try {
    await http.get('http://invalid-domain-that-does-not-exist.com/api')
  } catch (error) {
    setResult(error, '网络错误测试')
  }
}

const testTimeoutError = async () => {
  try {
    // 模拟超时 - 请求一个很慢的端点
    await http.get('/test/slow-endpoint')
  } catch (error) {
    setResult(error, '超时错误测试')
  }
}

// 前端代码错误测试（这些错误不应该显示提示）
const testJavaScriptError = () => {
  try {
    // 故意触发JavaScript错误
    throw new Error('这是一个前端JavaScript错误')
  } catch (error) {
    handleApiError(error)
    setResult(error, 'JavaScript错误测试')
  }
}

const testUndefinedError = () => {
  try {
    // 访问undefined的属性
    const obj: any = undefined
    console.log(obj.someProperty.nestedProperty)
  } catch (error) {
    handleApiError(error)
    setResult(error, 'Undefined错误测试')
  }
}

const testTypeError = () => {
  try {
    // 类型错误
    const num: any = null
    num.toFixed(2)
  } catch (error) {
    handleApiError(error)
    setResult(error, '类型错误测试')
  }
}

// 自定义错误测试
const testCustomError = () => {
  // 模拟后端API错误
  const customError = {
    status: 400,
    error: '这是一个模拟的后端错误消息'
  }
  handleApiError(customError)
  setResult(customError, '自定义错误测试')
}

const testSilentError = async () => {
  const result = await ApiWrapper.silent(async () => {
    throw new Error('这是一个静默错误，不会显示提示')
  })
  setResult({ result, error: '静默错误' }, '静默错误测试')
}

const testCriticalError = () => {
  const criticalError = new Error('这是一个严重错误，会显示对话框')
  handleCriticalError(criticalError)
  setResult(criticalError, '严重错误测试')
}

// API包装器测试
const testApiWrapperStandard = async () => {
  const result = await httpWrapper.get.standard('/test/non-existent', {}, '标准API包装器测试失败')
  setResult(result, 'API包装器标准测试')
}

const testApiWrapperSilent = async () => {
  const result = await httpWrapper.get.silent('/test/non-existent')
  setResult(result, 'API包装器静默测试')
}

const testApiWrapperCritical = async () => {
  const result = await httpWrapper.get.critical('/test/non-existent', {}, '关键API包装器测试失败')
  setResult(result, 'API包装器关键测试')
}

// 运行自动化测试
const runAutomatedTests = () => {
  console.clear()
  console.log('🧪 开始运行错误处理自动化测试...')
  runAllTests()
  message.success('自动化测试已运行，请查看浏览器控制台输出')
}
</script>

<style scoped>
.error-test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}
</style>
