<template>
  <div class="chat-container">
    <!-- 侧边栏 - 会话列表 -->
    <div class="chat-sidebar">
      <div class="sidebar-header">
        <h3>聊天会话</h3>
        <n-button @click="createNewSession" type="primary" size="small">
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          新建会话
        </n-button>
      </div>
      
      <div class="session-list">
        <div 
          v-for="session in sessions" 
          :key="session.id"
          class="session-item"
          :class="{ active: currentSessionId === session.id }"
          @click="selectSession(session.id)"
        >
          <div class="session-title">{{ session.title || '新会话' }}</div>
          <div class="session-meta">
            <span class="message-count">{{ session.message_count }} 条消息</span>
            <span class="last-time">{{ formatTime(session.updated_at) }}</span>
          </div>
          <div class="session-actions">
            <n-button 
              @click.stop="deleteSession(session.id)" 
              type="error" 
              size="tiny"
              quaternary
            >
              <template #icon>
                <n-icon><TrashOutline /></n-icon>
              </template>
            </n-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主聊天区域 -->
    <div class="chat-main">
      <!-- 聊天头部 -->
      <div class="chat-header">
        <h3>{{ currentSession?.title || '选择一个会话开始聊天' }}</h3>
        <div class="chat-actions">
          <n-button @click="clearCurrentSession" size="small" quaternary>
            <template #icon>
              <n-icon><RefreshOutline /></n-icon>
            </template>
            清空会话
          </n-button>
        </div>
      </div>

      <!-- 消息列表 -->
      <div class="message-list" ref="messageListRef">
        <div v-if="!currentSessionId" class="empty-state">
          <n-empty description="请选择一个会话开始聊天" />
        </div>
        
        <div v-else>
          <div 
            v-for="message in messages" 
            :key="message.id"
            class="message-item"
            :class="{ 'user-message': message.role === 'user', 'assistant-message': message.role === 'assistant' }"
          >
            <div class="message-avatar">
              <n-avatar v-if="message.role === 'user'" round>
                <n-icon><PersonOutline /></n-icon>
              </n-avatar>
              <n-avatar v-else round color="#18a058">
                <n-icon><ChatbubbleEllipsesOutline /></n-icon>
              </n-avatar>
            </div>
            
            <div class="message-content">
              <div class="message-header">
                <span class="message-role">{{ message.role === 'user' ? '用户' : 'AI助手' }}</span>
                <span class="message-time">{{ formatTime(message.timestamp) }}</span>
              </div>
              <div class="message-text">{{ message.content }}</div>
            </div>
          </div>

          <!-- 加载中消息 -->
          <div v-if="isLoading" class="message-item assistant-message">
            <div class="message-avatar">
              <n-avatar round color="#18a058">
                <n-icon><ChatbubbleEllipsesOutline /></n-icon>
              </n-avatar>
            </div>
            <div class="message-content">
              <div class="message-header">
                <span class="message-role">AI助手</span>
              </div>
              <div class="message-text">
                <n-spin size="small" />
                <span style="margin-left: 8px;">正在思考中...</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input">
        <div class="input-container">
          <n-input
            v-model:value="inputMessage"
            type="textarea"
            placeholder="输入您的消息..."
            :autosize="{ minRows: 1, maxRows: 4 }"
            @keydown.enter.prevent="handleEnterKey"
            :disabled="isLoading"
          />
          <n-button 
            @click="sendMessage" 
            type="primary" 
            :disabled="!inputMessage.trim() || isLoading"
            :loading="isLoading"
          >
            <template #icon>
              <n-icon><SendOutline /></n-icon>
            </template>
            发送
          </n-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, computed } from 'vue'
import { 
  NButton, 
  NIcon, 
  NEmpty, 
  NAvatar, 
  NInput, 
  NSpin,
  useMessage,
  useDialog
} from 'naive-ui'
import { 
  AddOutline, 
  TrashOutline, 
  RefreshOutline, 
  PersonOutline, 
  ChatbubbleEllipsesOutline,
  SendOutline 
} from '@vicons/ionicons5'
import { chatApi, type ChatMessage, type ChatSession } from '@/api/chat'
import { useUserStore } from '@/stores/user'

// 用户状态管理
const userStore = useUserStore()

// 响应式数据
const sessions = ref<ChatSession[]>([])
const messages = ref<ChatMessage[]>([])
const currentSessionId = ref<string>('')
const inputMessage = ref('')
const isLoading = ref(false)
const messageListRef = ref<HTMLElement>()

// 固定的会话ID，用户名从用户状态获取
const FIXED_SESSION_ID = "wx_sunyubao-1040326"

// 工具
const message = useMessage()
const dialog = useDialog()

// 计算属性
const currentSession = computed(() => 
  sessions.value.find(s => s.id === currentSessionId.value)
)

// 生命周期
onMounted(() => {
  loadSessions()
})

// 方法
const loadSessions = async () => {
  try {
    const response = await chatApi.getSessions()
    sessions.value = response.sessions || []

    // 如果有会话，默认选择第一个
    if (sessions.value.length > 0 && !currentSessionId.value) {
      selectSession(sessions.value[0].id)
    }
  } catch (error) {
    // 错误已由全局错误处理器处理，这里只需要记录日志
    console.error('Load sessions error:', error)
  }
}

const selectSession = async (sessionId: string) => {
  currentSessionId.value = sessionId
  await loadMessages(sessionId)
}

const loadMessages = async (sessionId: string) => {
  try {
    const response = await chatApi.getSessionHistory(sessionId)
    messages.value = response.messages || []
    await nextTick()
    scrollToBottom()
  } catch (error) {
    message.error('加载消息历史失败')
    console.error('Load messages error:', error)
  }
}

const createNewSession = async () => {
  try {
    const response = await chatApi.createSession()
    sessions.value.unshift(response.session)
    selectSession(response.session.id)
    message.success('创建新会话成功')
  } catch (error) {
    message.error('创建会话失败')
    console.error('Create session error:', error)
  }
}

const deleteSession = async (sessionId: string) => {
  dialog.warning({
    title: '确认删除',
    content: '确定要删除这个会话吗？此操作不可恢复。',
    positiveText: '删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await chatApi.deleteSession(sessionId)
        sessions.value = sessions.value.filter(s => s.id !== sessionId)
        
        if (currentSessionId.value === sessionId) {
          currentSessionId.value = ''
          messages.value = []
          
          // 选择下一个会话
          if (sessions.value.length > 0) {
            selectSession(sessions.value[0].id)
          }
        }
        
        message.success('删除会话成功')
      } catch (error) {
        message.error('删除会话失败')
        console.error('Delete session error:', error)
      }
    }
  })
}

const clearCurrentSession = async () => {
  if (!currentSessionId.value) return
  
  dialog.warning({
    title: '确认清空',
    content: '确定要清空当前会话的所有消息吗？此操作不可恢复。',
    positiveText: '清空',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await chatApi.clearSession(currentSessionId.value)
        messages.value = []
        message.success('清空会话成功')
      } catch (error) {
        message.error('清空会话失败')
        console.error('Clear session error:', error)
      }
    }
  })
}

const sendMessage = async () => {
  if (!inputMessage.value.trim() || isLoading.value) return

  const messageText = inputMessage.value.trim()
  inputMessage.value = ''
  isLoading.value = true

  // 添加用户消息到界面
  const userMessage: ChatMessage = {
    id: Date.now().toString(),
    content: messageText,
    role: 'user',
    timestamp: Date.now(),
    session_id: ''
  }
  messages.value.push(userMessage)

  await nextTick()
  scrollToBottom()

  try {
    // 发送消息到后端，带上用户信息
    const response = await chatApi.sendMessage({
      message: messageText,
      session_id: userStore.userInfo?.username,
      username: userStore.userInfo?.name,
      metadata: {
        user_email: userStore.userInfo?.email,
        user_roles: userStore.userRoles,
        timestamp: Date.now()
      }
    })
    
    // 处理后端返回的消息格式
    if (response.result === "SUCCESS" && response.message && response.message.length > 0) {
      // 为每条消息创建单独的AI响应
      response.message.forEach((messageContent: string, index: number) => {
        const aiMessage: ChatMessage = {
          id: `${Date.now()}-${index}`,
          content: messageContent,
          role: 'assistant',
          timestamp: Date.now() + index, // 确保每条消息有不同的时间戳
          session_id: currentSessionId.value
        }

        messages.value.push(aiMessage)
      })
    } else if (response.result === "ERROR") {
      // 处理错误情况
      message.error(response.message?.[0] || '发送消息失败')
    } else {
      // 处理其他情况
      message.error('未收到有效的AI回复')
    }
    
    // 更新会话列表
    await loadSessions()
    
  } catch (error) {
    // 错误已由全局错误处理器处理
    console.error('Send message error:', error)
  } finally {
    isLoading.value = false
    await nextTick()
    scrollToBottom()
  }
}

const pollForResponse = async (messageId: string) => {
  const maxAttempts = 30 // 最多轮询30次
  let attempts = 0
  
  const poll = async () => {
    try {
      const response = await chatApi.pollResponse(messageId)
      
      // 处理后端返回的消息格式
      if (response.result === "SUCCESS" && response.message && response.message.length > 0) {
        // 为每条消息创建单独的AI响应
        response.message.forEach((messageContent: string, index: number) => {
          const aiMessage: ChatMessage = {
            id: `${Date.now()}-${index}`,
            content: messageContent,
            role: 'assistant',
            timestamp: Date.now() + index, // 确保每条消息有不同的时间戳
            session_id: FIXED_SESSION_ID
          }

          messages.value.push(aiMessage)
        })
        return true
      } else if (response.result === "ERROR") {
        message.error(response.message?.[0] || '获取响应失败')
        return true
      } else if (attempts < maxAttempts) {
        attempts++
        setTimeout(poll, 1000) // 1秒后重试
        return false
      } else {
        message.error('响应超时')
        return true
      }
    } catch (error) {
      console.error('Poll response error:', error)
      if (attempts < maxAttempts) {
        attempts++
        setTimeout(poll, 1000)
        return false
      } else {
        message.error('获取响应失败')
        return true
      }
    }
  }
  
  await poll()
}

const handleEnterKey = (event: KeyboardEvent) => {
  if (!event.shiftKey) {
    sendMessage()
  }
}

const scrollToBottom = () => {
  if (messageListRef.value) {
    messageListRef.value.scrollTop = messageListRef.value.scrollHeight
  }
}

const formatTime = (timestamp: number) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 24小时内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString()
  }
}
</script>

<style scoped>
.chat-container {
  display: flex;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 侧边栏样式 */
.chat-sidebar {
  width: 300px;
  background-color: white;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.session-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.session-item {
  padding: 12px;
  margin-bottom: 4px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.session-item:hover {
  background-color: #f0f0f0;
}

.session-item.active {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
}

.session-title {
  font-weight: 500;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.session-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
}

.session-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.session-item:hover .session-actions {
  opacity: 1;
}

/* 主聊天区域样式 */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: white;
}

.chat-header {
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
}

.chat-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px 24px;
  background-color: #fafafa;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.message-item {
  display: flex;
  margin-bottom: 16px;
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.message-avatar {
  margin-right: 12px;
  flex-shrink: 0;
}

.message-content {
  flex: 1;
  max-width: calc(100% - 60px);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.message-role {
  font-weight: 500;
  font-size: 14px;
}

.message-time {
  font-size: 12px;
  color: #999;
}

.message-text {
  background-color: white;
  padding: 12px 16px;
  border-radius: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  word-wrap: break-word;
  white-space: pre-wrap;
  line-height: 1.5;
}

.user-message {
  flex-direction: row-reverse;
}

.user-message .message-avatar {
  margin-right: 0;
  margin-left: 12px;
}

.user-message .message-content {
  text-align: right;
}

.user-message .message-text {
  background-color: #1890ff;
  color: white;
}

.assistant-message .message-text {
  background-color: white;
  border: 1px solid #e0e0e0;
}

/* 输入区域样式 */
.chat-input {
  padding: 16px 24px;
  border-top: 1px solid #e0e0e0;
  background-color: white;
}

.input-container {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.input-container .n-input {
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-container {
    flex-direction: column;
  }

  .chat-sidebar {
    width: 100%;
    height: 200px;
  }

  .message-list {
    padding: 12px 16px;
  }

  .chat-input {
    padding: 12px 16px;
  }

  .chat-header {
    padding: 12px 16px;
  }
}
</style>
