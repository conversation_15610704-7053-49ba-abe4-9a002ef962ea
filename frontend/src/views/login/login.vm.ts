import { ref } from 'vue'
import { authApi } from '@/api/auth'
import { clearAuthOnLogin } from '@/utils/auth-cleanup'

export function useLoginViewModel() {
  const loading = ref(false)

  // 检查登录状态并清理旧令牌
  const checkLoginStatus = () => {
    // 使用统一的清理工具清理认证数据
    clearAuthOnLogin()

    // 可选：如果 URL 中有特殊参数表示需要强制重新登录
    const urlParams = new URLSearchParams(window.location.search)
    const forceLogin = urlParams.get('force_login')

    if (forceLogin) {
      console.log('检测到强制登录参数，已清理所有认证数据')
    }
  }

  // 处理 OAuth2 登录
  const handleOAuthLogin = async () => {
    loading.value = true
    try {
      // 生成随机 state 参数用于防止 CSRF 攻击
      const state = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
      localStorage.setItem('oauth_state', state)

      // 从后端获取配置并生成授权 URL
      const authUrl = await authApi.generateAuthUrl(state)
      window.location.href = authUrl
    } catch (error) {
      console.error('OAuth 登录失败:', error)
      loading.value = false
    }
  }

  return {
    loading,
    handleOAuthLogin,
    checkLoginStatus
  }
}