<template>
  <div class="oauth-callback">
    <div class="callback-container">
      <div class="loading-content">
        <n-spin size="large" />
        <h3>正在处理登录...</h3>
        <p>请稍候，我们正在验证您的身份</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { NSpin, useMessage } from 'naive-ui'
import { authApi } from '@/api/auth'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const route = useRoute()
const message = useMessage()
const userStore = useUserStore()

onMounted(async () => {
  try {
    // 获取 URL 中的授权码和状态
    const code = route.query.code as string
    const state = route.query.state as string
    const error = route.query.error as string

    console.log('OAuth 回调参数:', { code, state, error })

    // 检查是否有错误
    if (error) {
      console.error('OAuth 授权错误:', error)
      message.error(`授权失败：${error}`)
      router.push('/login')
      return
    }

    if (!code) {
      console.error('缺少授权码')
      message.error('授权失败：缺少授权码')
      router.push('/login')
      return
    }

    // 验证 state 参数（防止 CSRF 攻击）
    const savedState = localStorage.getItem('oauth_state')
    console.log('State 验证:', { receivedState: state, savedState })

    if (state && savedState && state !== savedState) {
      console.error('State 验证失败')
      message.error('授权失败：状态验证失败')
      router.push('/login')
      return
    }

    // 清除保存的 state
    localStorage.removeItem('oauth_state')

    console.log('开始交换访问令牌...')
    // 使用授权码换取访问令牌
    const { access_token, user_info } = await authApi.exchangeToken(code, state)

    console.log('访问令牌获取成功')
    // 保存令牌
    localStorage.setItem('token', access_token)

    // 获取并保存用户信息
    if (user_info) {
      console.log('保存用户信息:', user_info)
      userStore.setUser(user_info)
    }

    // 跳转到主页
    router.push('/')

  } catch (error: any) {
    console.error('OAuth 回调处理失败:', error)
    message.error(`登录失败：${error.message || '请重试'}`)
    router.push('/login')
  }
})
</script>

<style scoped>
.oauth-callback {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.callback-container {
  background: white;
  border-radius: 12px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 400px;
  width: 100%;
}

.loading-content h3 {
  margin: 1.5rem 0 0.5rem 0;
  color: #333;
  font-weight: 600;
}

.loading-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}
</style>
