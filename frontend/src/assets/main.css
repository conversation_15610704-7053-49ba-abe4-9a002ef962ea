@import './base.css';

:root {
  /* 主色调 */
  --primary-color: #4080ff;
  --primary-color-hover: #6699ff;
  --primary-color-rgb: 64, 128, 255;
  
  /* 文本颜色 */
  --text-color: #333639;
  --text-color-secondary: #606266;
  --text-color-tertiary: #909399;
  
  /* 背景颜色 */
  --bg-color: #f5f7fa;
  --card-bg-color: #ffffff;
  --sidebar-bg-color: #ffffff;
  
  /* 边框颜色 */
  --border-color: #e5e7eb;
  
  /* 状态颜色 */
  --success-color: #18a058;
  --warning-color: #f0a020;
  --error-color: #d03050;
  --info-color: #5c6ac4;
  
  /* 圆角 */
  --border-radius: 8px;
  --border-radius-small: 4px;
  
  /* 阴影 */
  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  --box-shadow-hover: 0 6px 16px rgba(0, 0, 0, 0.12);
  
  /* 过渡 */
  --transition-duration: 0.2s;
  --transition-timing-function: ease;
  --n-border-color: rgba(0, 0, 0, 0.1);

  --n-padding-md: 20px;
  
  --n-window-width: 1000px;
  --n-window-height: calc(100vh - 100px);

  --sidebar-title-height: 56px;
}

.dark {
  /* 主色调 */
  --primary-color: #4080ff;
  --primary-color-hover: #6699ff;
  --primary-color-rgb: 64, 128, 255;
  /* 文本颜色 */
  --text-color: #e5e7eb;
  --text-color-secondary: #c9cdd4;
  --text-color-tertiary: #8b949e;
  
  /* 背景颜色 */
  --bg-color: #121212;
  --card-bg-color: #1e1e1e;
  --sidebar-bg-color: #1e1e1e;
  
  /* 边框颜色 */
  --border-color: #333639;
  
  /* 状态颜色 */
  --success-color: #63e2b7;
  --warning-color: #f3a769;
  --error-color: #e88080;
  --info-color: #7a89d0;
  
  /* 阴影 */
  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  --box-shadow-hover: 0 6px 16px rgba(0, 0, 0, 0.3);
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC', 'Microsoft YaHei', sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color var(--transition-duration) var(--transition-timing-function);
  margin: 0;
  padding: 0;
}

#app {
  margin: 0 auto;
  font-weight: normal;
  color: var(--text-color);
}

a {
  text-decoration: none;
  color: var(--primary-color);
  transition: color var(--transition-duration) var(--transition-timing-function);
}

@media (hover: hover) {
  a:hover {
    color: var(--primary-color-hover);
  }
}

.container-window-style {
  max-width: var(--n-window-width);
  margin: 0 auto;
  padding: var(--n-padding-md);
  padding-top: 100px;
  transition: all 0.2s ease;
}

/* 卡片样式 */
.n-card {
  border-radius: var(--border-radius);
  transition: box-shadow var(--transition-duration) var(--transition-timing-function),
              transform var(--transition-duration) var(--transition-timing-function);
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
}

/* 按钮样式 */
.n-button {
  border-radius: var(--border-radius-small);
  transition: background-color var(--transition-duration) var(--transition-timing-function),
              color var(--transition-duration) var(--transition-timing-function),
              border-color var(--transition-duration) var(--transition-timing-function),
              box-shadow var(--transition-duration) var(--transition-timing-function);
}

/* 标签样式 */
.n-tag {
  border-radius: 16px;
  padding: 0 10px;
  transition: background-color var(--transition-duration) var(--transition-timing-function);
}

/* 输入框样式 */
.n-input {
  border-radius: var(--border-radius-small);
  transition: all 0.3s ease;
}

.n-input:hover, .n-input:focus-within {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
  border-color: var(--primary-color);
}

/* 选择器样式 */
.n-select {
  border-radius: var(--border-radius-small);
  transition: all 0.3s ease;
}

.n-select:hover, .n-select:focus-within {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

/* 日期选择器样式 */
.n-date-picker {
  border-radius: var(--border-radius-small);
  transition: all 0.3s ease;
}

.n-date-picker:hover, .n-date-picker:focus-within {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

/* 模态框样式 */
.n-modal {
  border-radius: var(--border-radius);
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

/* 通用容器卡片样式 */
.config-section,
.instance-card,
.adapter-card,
.media-card,
.search-card {
  border-radius: var(--border-radius);
  background-color: var(--card-bg-color);
  box-shadow: var(--box-shadow);
  transition: all var(--transition-duration) var(--transition-timing-function);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.config-section:hover,
.instance-card:hover,
.adapter-card:hover,
.media-card:hover {
  box-shadow: var(--box-shadow-hover);
}

/* 表格样式 */
.n-data-table {
  border-radius: var(--border-radius);
}

.n-data-table-th {
  background-color: var(--bg-color);
  font-weight: 500;
}

/* 表单样式 */
.n-form-item-label {
  font-weight: 500;
}

/* 空状态样式 */
.empty-state {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  animation: fade-in 0.5s ease;
  padding: 2rem;
}

.empty-icon {
  margin-bottom: 20px;
  animation: float 3s ease-in-out infinite;
}

.primary-button {
  margin-top: 1rem;
  height: 40px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-hover) 100%);
  border: none;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 通用操作按钮样式 */
.action-button {
  transition: all 0.3s ease;
}

.action-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 背景图案 */
.bg {
  background-image: radial-gradient(color-mix(in oklab, oklch(.13 .028 261.692) 5%, transparent) 1px, transparent 0);
  background-size: 10px 10px;
}

/* 渐变文本 */
.gradient-text {
  background: linear-gradient(to right, var(--primary-color), var(--primary-color-hover));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 全局动画 */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-up {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-left {
  from { 
    opacity: 0;
    transform: translateX(-20px);
  }
  to { 
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0); }
}

@keyframes container-appear {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式调整 */
@media (max-width: 992px) {
  .n-modal {
    width: 90% !important;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 16px;
  }
  
  .n-card-header {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .n-form-item {
    margin-bottom: 1rem;
  }
}