// 全局类型声明文件
// 用于修复第三方库的类型问题

// 修复 ToggleEvent 类型缺失的问题
declare interface ToggleEvent extends Event {
  newState: 'open' | 'closed';
}

// 修复 Vue Ref 类型问题
declare module '@vue/reactivity' {
  interface Ref<T = any> {
    value: T;
  }
}

// 扩展 Window 接口
declare interface Window {
  // 可以在这里添加全局的 window 属性
}

// 扩展 process.env 类型
declare namespace NodeJS {
  interface ProcessEnv {
    BASE_URL?: string;
    NODE_ENV?: 'development' | 'production' | 'test';
  }
}
