import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import { execSync } from 'child_process'

// 获取 Git 信息
function getGitVersion(): string {
  try {
    let tag = '';
    try {
      tag = execSync('git describe --tags --exact-match').toString().trim();
    } catch (e) {
      // 如果没有找到 tag，则忽略错误
    }

    if (tag) {
      return tag;
    }

    const commitHash = execSync('git rev-parse --short HEAD').toString().trim();
    return `dev-${commitHash}`;
  } catch (e) {
    return 'unknown';
  }
}

// 获取构建输出目录
function getBuildOutDir(): string {
  const baseUrl = process.env.BASE_URL || '/chat/'
  // 移除开头和结尾的斜杠，转换为目录名
  const basePath = baseUrl.replace(/^\/+|\/+$/g, '')

  // 如果是根路径，直接返回 dist
  if (!basePath) {
    return 'dist'
  }

  return `dist/${basePath}`
}

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  // Added proxy configuration
  server: {
    proxy: {
      '/chat-api': 'http://127.0.0.1:8080',
      '/chat-api/api/tracing/ws': {
        target: 'ws://127.0.0.1:8080',
        ws: true,
        changeOrigin: true,
      },
      '/chat-api/api/system/logs': {
        target: 'ws://127.0.0.1:8080',
        ws: true,
        changeOrigin: true,
      }
    }
  },
  base: process.env.BASE_URL || '/chat/',
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@comfyorg/litegraph/dist/css/litegraph.css': path.resolve(__dirname, 'node_modules/@comfyorg/litegraph/dist/css/litegraph.css'),
    }
  },
  build: {
    outDir: getBuildOutDir(),
    rollupOptions: {
      output: {
        entryFileNames: `assets/[name].js`,
        chunkFileNames: `assets/[name].js`,
        assetFileNames: `assets/[name].[ext]`,
        manualChunks: {
          'cryptojs': ['crypto-js']
        }
      }

    }
  },
  define: {
    'import.meta.env.VITE_APP_VERSION': JSON.stringify(getGitVersion()),
    'import.meta.env.VITE_UPDATE_CHECK_ENABLED': JSON.stringify(process.env.VITE_UPDATE_CHECK_ENABLED || 'false'),
    'import.meta.env.VITE_UPDATE_CHECK_INTERVAL': JSON.stringify(process.env.VITE_UPDATE_CHECK_INTERVAL || '86400000'),
    'import.meta.env.VITE_UPDATE_AUTO_CHECK': JSON.stringify(process.env.VITE_UPDATE_AUTO_CHECK || 'false')
  }
})
