/// <reference types="vite/client" />

// 修复 ToggleEvent 类型缺失的问题
declare interface ToggleEvent extends Event {
  newState: 'open' | 'closed';
}

// 扩展 process.env 类型以支持 BASE_URL
declare namespace NodeJS {
  interface ProcessEnv {
    BASE_URL?: string;
  }
}

// 扩展 Vite 环境变量类型
interface ImportMetaEnv {
  readonly BASE_URL: string;
  readonly VITE_APP_VERSION?: string;
  readonly VITE_UPDATE_CHECK_ENABLED?: string;
  readonly VITE_UPDATE_CHECK_INTERVAL?: string;
  readonly VITE_UPDATE_AUTO_CHECK?: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
