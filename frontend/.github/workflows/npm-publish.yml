name: NPM Publish

on:
  push:
    tags:
      - 'v*'

jobs:
  publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          registry-url: 'https://registry.npmjs.org'
      
      - name: Setup Yarn
        run: npm install -g yarn
      
      - name: Install dependencies
        run: yarn install --frozen-lockfile
      
      - name: Build
        run: yarn build
      
      - name: Publish to NPM
        run: |
          if [[ ${{ github.ref }} == refs/tags/v* ]]; then
            version=$(echo ${{ github.ref }} | sed 's/refs\/tags\/v//')
            if [[ $version == *beta* ]]; then
              echo "Publishing as beta version"
              yarn publish --tag beta
            else
              echo "Publishing as stable version"
              yarn publish
            fi
          else
            echo "Not a tag, skipping publish"
          fi
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }} 