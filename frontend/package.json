{"name": "kirara-ai-<PERSON>ui", "version": "0.1.0", "author": "lss233", "license": "MIT", "publishConfig": {"access": "public"}, "files": ["dist"], "scripts": {"dev": "vite", "build": "run-p type-check build-only", "preview": "vite preview", "build-only": "vite build", "build:custom": "./scripts/build-with-base.sh", "type-check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@comfyorg/litegraph": "^0.8.71", "@vicons/ionicons5": "^0.12.0", "@vue-flow/core": "^1.42.1", "cors": "^2.8.5", "crypto-js": "^4.1.1", "echarts": "^5.6.0", "express": "^5.1.0", "naive-ui": "^2.34.3", "pinia": "^2.0.35", "uuid": "^11.0.5", "vue": "^3.2.47", "vue-echarts": "^7.0.3", "vue-router": "^4.1.6"}, "devDependencies": {"@lobehub/icons-static-webp": "^1.24.0", "@rushstack/eslint-patch": "^1.2.0", "@tsconfig/node18": "^2.0.0", "@types/node": "^18.16.3", "@types/semver": "^7.5.8", "@vitejs/plugin-vue": "^4.2.1", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/tsconfig": "^0.3.2", "eslint": "^8.39.0", "eslint-plugin-vue": "^9.11.0", "markdown-it": "^14.1.0", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "typescript": "~5.0.4", "vite": "^4.3.4", "vue-tsc": "^1.6.4"}}