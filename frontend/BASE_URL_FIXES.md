# BASE_URL 修复总结

## 🎯 修复目标

确保前端应用在不同的 BASE_URL 配置下都能正确工作，包括：
- 静态资源路径
- WebSocket 连接
- 页面跳转
- 构建输出目录

## ✅ 已修复的问题

### 1. 静态资源路径

**问题**: 图标等静态资源使用硬编码路径，在子路径部署时无法正确加载。

**修复**:
- 创建了 `src/utils/assets.ts` 工具函数
- 修复了 `LLMView.vue` 和 `IMView.vue` 中的图标路径
- 使用 `import.meta.env.BASE_URL` 动态生成路径

**修复的文件**:
```typescript
// src/utils/assets.ts
export function getAssetUrl(path: string): string {
  const cleanPath = path.startsWith('/') ? path.slice(1) : path
  return `${import.meta.env.BASE_URL}${cleanPath}`
}

// src/views/llm/LLMView.vue
const getAdapterIcon = (adapter: string) => {
  return `${import.meta.env.BASE_URL}assets/icons/llm/${adapter.toLowerCase()}.webp`
}

// src/views/im/IMView.vue
const getAdapterIconUrl = (type: string) => {
  return `${import.meta.env.BASE_URL}assets/icons/im/${type}.png`
}
```

### 2. WebSocket 连接

**问题**: WebSocket 连接使用错误的协议和路径，导致连接失败。

**修复**:
- 在 `http.ts` 中添加了 `wsUrl()` 方法
- 修复了协议转换（HTTP → WS, HTTPS → WSS）
- 更新了 `console.vm.ts` 和 `tracing.vm.ts`

**修复的文件**:
```typescript
// src/utils/http.ts
wsUrl(path: string) {
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
  const host = window.location.host
  
  if (import.meta.env.DEV) {
    return `${protocol}//${host}${BASE_URL}${path}`
  }
  
  return `${protocol}//${host}${BASE_URL}${path}`
}

// src/views/console/viewmodels/console.vm.ts
const wsUrl = http.wsUrl('/system/logs')

// src/views/tracing/tracing.vm.ts
const wsUrl = http.wsUrl('/tracing/ws')
```

### 3. 页面跳转

**问题**: `window.open()` 使用硬编码路径，在子路径部署时跳转到错误的 URL。

**修复**:
- 创建了 `getPageUrl()` 工具函数
- 修复了 `WorkflowList.vue` 中的页面跳转

**修复的文件**:
```typescript
// src/utils/assets.ts
export function getPageUrl(path: string): string {
  const cleanPath = path.startsWith('/') ? path.slice(1) : path
  return `${import.meta.env.BASE_URL}${cleanPath}`
}

// src/views/workflow/WorkflowList.vue
import { getPageUrl } from '@/utils/assets'

const handleCreate = () => {
  window.open(getPageUrl('workflow/editor'), '_blank')
}

const handleEdit = (row: Workflow) => {
  window.open(getPageUrl(`workflow/editor/${row.group_id}:${row.workflow_id}`), '_blank')
}
```

### 4. 构建输出目录

**问题**: 所有 BASE_URL 的构建都输出到同一个 `dist/` 目录，无法区分不同的部署环境。

**修复**:
- 修改了 `vite.config.ts` 中的 `getBuildOutDir()` 函数
- 根据 BASE_URL 动态设置输出目录
- 更新了构建脚本

**修复的文件**:
```typescript
// vite.config.ts
function getBuildOutDir(): string {
  const baseUrl = process.env.BASE_URL || '/chat/'
  const basePath = baseUrl.replace(/^\/+|\/+$/g, '')
  
  // 如果是根路径，直接返回 dist
  if (!basePath) {
    return 'dist'
  }
  
  return `dist/${basePath}`
}
```

**构建输出结构**:
```
dist/
├── index.html          # BASE_URL=/ 的构建产物
├── assets/
└── ...

dist/chat/
├── index.html          # BASE_URL=/chat/ 的构建产物
├── assets/
└── ...

dist/ai-chat/
├── index.html          # BASE_URL=/ai-chat/ 的构建产物
├── assets/
└── ...
```

### 5. TypeScript 类型定义

**问题**: `import.meta.env.BASE_URL` 缺少类型定义。

**修复**:
- 更新了 `env.d.ts` 文件
- 添加了 Vite 环境变量的类型定义

**修复的文件**:
```typescript
// env.d.ts
interface ImportMetaEnv {
  readonly BASE_URL: string;
  readonly VITE_APP_VERSION?: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
```

## 🛠️ 工具函数

创建了统一的工具函数来处理各种路径需求：

```typescript
// src/utils/assets.ts

// 基础资源路径
export function getAssetUrl(path: string): string

// 图标路径
export function getIconUrl(category: string, name: string, extension?: string): string
export function getLLMIconUrl(adapter: string): string
export function getIMIconUrl(type: string): string
export function getIMFallbackIconUrl(): string

// 静态资源路径
export function getStaticUrl(path: string): string

// 页面路径（用于 window.open）
export function getPageUrl(path: string): string
```

## 🧪 测试

创建了 WebSocket 连接测试页面：
- `frontend/websocket-test.html`
- 可以测试认证和 WebSocket 连接
- 帮助调试连接问题

## 📋 使用指南

### 构建不同环境

```bash
# 根路径部署
BASE_URL=/ npm run build

# 子路径部署
BASE_URL=/chat/ npm run build
BASE_URL=/ai-chat/ npm run build

# 使用构建脚本
./scripts/build-with-base.sh /
./scripts/build-with-base.sh /chat/
./scripts/build-with-base.sh /ai-chat/
```

### 在代码中使用

```typescript
// 静态资源
import { getAssetUrl, getIconUrl } from '@/utils/assets'
const iconUrl = getIconUrl('llm', 'openai', 'webp')

// WebSocket 连接
import { http } from '@/utils/http'
const wsUrl = http.wsUrl('/system/logs')

// 页面跳转
import { getPageUrl } from '@/utils/assets'
window.open(getPageUrl('workflow/editor'), '_blank')

// 路由跳转（自动处理 BASE_URL）
router.push('/settings')
```

## ✨ 总结

现在前端应用完全支持动态 BASE_URL 配置，可以：
- 部署到任意子路径
- 正确加载所有静态资源
- 建立正确的 WebSocket 连接
- 进行正确的页面跳转
- 生成独立的构建产物

所有路径都通过工具函数统一管理，确保一致性和可维护性。
