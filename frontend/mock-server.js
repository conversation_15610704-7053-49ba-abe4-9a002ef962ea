// 简单的模拟聊天服务器
const express = require('express')
const cors = require('cors')
const app = express()

app.use(cors())
app.use(express.json())

// 模拟数据
let sessions = [
  {
    id: 'session-1',
    title: '测试会话 1',
    created_at: Date.now() - 86400000,
    updated_at: Date.now() - 3600000,
    message_count: 5,
    last_message: '你好，我是AI助手'
  }
]

let messages = {
  'session-1': [
    {
      id: 'msg-1',
      content: '你好',
      role: 'user',
      timestamp: Date.now() - 3600000,
      session_id: 'session-1'
    },
    {
      id: 'msg-2',
      content: '你好！我是AI助手，很高兴为您服务。有什么我可以帮助您的吗？',
      role: 'assistant',
      timestamp: Date.now() - 3599000,
      session_id: 'session-1'
    }
  ]
}

let messageIdCounter = 3

// 获取会话列表
app.get('/chat-api/v1/chat/sessions', (req, res) => {
  res.json({ sessions })
})

// 创建新会话
app.post('/chat-api/v1/chat/sessions', (req, res) => {
  const { title } = req.body
  const newSession = {
    id: `session-${Date.now()}`,
    title: title || '新会话',
    created_at: Date.now(),
    updated_at: Date.now(),
    message_count: 0,
    last_message: ''
  }
  sessions.unshift(newSession)
  messages[newSession.id] = []
  res.json({ session: newSession })
})

// 获取会话历史
app.get('/chat-api/v1/chat/history/:sessionId', (req, res) => {
  const { sessionId } = req.params
  const sessionMessages = messages[sessionId] || []
  res.json({ messages: sessionMessages })
})

// 发送消息
app.post('/chat-api/v1/chat', (req, res) => {
  const { message, session_id } = req.body
  
  if (!session_id || !messages[session_id]) {
    return res.status(400).json({ error: 'Invalid session_id' })
  }
  
  // 添加用户消息
  const userMessage = {
    id: `msg-${messageIdCounter++}`,
    content: message,
    role: 'user',
    timestamp: Date.now(),
    session_id
  }
  messages[session_id].push(userMessage)
  
  // 模拟AI响应
  setTimeout(() => {
    const aiResponse = {
      id: `msg-${messageIdCounter++}`,
      content: generateAIResponse(message),
      role: 'assistant',
      timestamp: Date.now(),
      session_id
    }
    messages[session_id].push(aiResponse)
    
    // 更新会话信息
    const session = sessions.find(s => s.id === session_id)
    if (session) {
      session.updated_at = Date.now()
      session.message_count = messages[session_id].length
      session.last_message = aiResponse.content
    }
  }, 1000)
  
  // 立即返回响应
  const aiResponse = {
    id: `msg-${messageIdCounter++}`,
    content: generateAIResponse(message),
    role: 'assistant',
    timestamp: Date.now(),
    session_id
  }
  messages[session_id].push(aiResponse)
  
  // 更新会话信息
  const session = sessions.find(s => s.id === session_id)
  if (session) {
    session.updated_at = Date.now()
    session.message_count = messages[session_id].length
    session.last_message = aiResponse.content
  }
  
  res.json({
    message_id: userMessage.id,
    session_id,
    status: 'completed',
    response: aiResponse
  })
})

// 删除会话
app.delete('/chat-api/v1/chat/sessions/:sessionId', (req, res) => {
  const { sessionId } = req.params
  sessions = sessions.filter(s => s.id !== sessionId)
  delete messages[sessionId]
  res.json({ success: true })
})

// 清空会话历史
app.delete('/chat-api/v1/chat/history/:sessionId', (req, res) => {
  const { sessionId } = req.params
  if (messages[sessionId]) {
    messages[sessionId] = []
    const session = sessions.find(s => s.id === sessionId)
    if (session) {
      session.message_count = 0
      session.last_message = ''
    }
  }
  res.json({ success: true })
})

// 生成AI响应
function generateAIResponse(userMessage) {
  const responses = [
    '这是一个很有趣的问题！让我来为您详细解答。',
    '我理解您的意思。根据我的知识，我可以为您提供以下信息：',
    '感谢您的提问。这个话题确实值得深入探讨。',
    '您提到的这个问题很重要。让我从几个角度来分析：',
    '我很乐意帮助您解决这个问题。',
    '这是一个常见的疑问，我来为您详细说明一下。',
    '根据您的描述，我建议您可以考虑以下几个方面：',
    '您的问题让我想到了几个相关的要点。'
  ]
  
  if (userMessage.includes('你好') || userMessage.includes('hello')) {
    return '你好！我是AI助手，很高兴为您服务。有什么我可以帮助您的吗？'
  }
  
  if (userMessage.includes('谢谢') || userMessage.includes('感谢')) {
    return '不客气！如果您还有其他问题，随时可以问我。'
  }
  
  if (userMessage.includes('再见') || userMessage.includes('拜拜')) {
    return '再见！祝您生活愉快，有需要随时找我聊天。'
  }
  
  const randomResponse = responses[Math.floor(Math.random() * responses.length)]
  return `${randomResponse}\n\n关于"${userMessage}"，我需要更多的上下文信息才能给出更准确的回答。您能提供更多详细信息吗？`
}

const PORT = 8080
app.listen(PORT, () => {
  console.log(`Mock chat server running on http://localhost:${PORT}`)
  console.log('Available endpoints:')
  console.log('- GET /chat-api/v1/chat/sessions')
  console.log('- POST /chat-api/v1/chat/sessions')
  console.log('- GET /chat-api/v1/chat/history/:sessionId')
  console.log('- POST /chat-api/v1/chat')
  console.log('- DELETE /chat-api/v1/chat/sessions/:sessionId')
  console.log('- DELETE /chat-api/v1/chat/history/:sessionId')
})
