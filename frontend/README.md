<div align="center">
  <h1>Kirara Agent Framework WebUI</h1>
</div>

<div align="center">
  <a href="https://github.com/DarkSkyTeam/chatgpt-for-bot-webui">
    <img src="https://img.shields.io/github/stars/DarkSkyTeam/chatgpt-for-bot-webui?style=social" alt="Stars">
  </a>
  <a href="https://github.com/DarkSkyTeam/chatgpt-for-bot-webui">
    <img src="https://img.shields.io/github/forks/DarkSkyTeam/chatgpt-for-bot-webui?style=social" alt="Forks">
  </a>
  <a href="https://github.com/DarkSkyTeam/chatgpt-for-bot-webui">
    <img src="https://img.shields.io/github/issues/DarkSkyTeam/chatgpt-for-bot-webui" alt="Issues">
  </a>
  <a href="https://github.com/DarkSkyTeam/chatgpt-for-bot-webui">
    <img src="https://img.shields.io/github/license/DarkSkyTeam/chatgpt-for-bot-webui" alt="License">
  </a>
</div>

## 项目介绍

本项目是 [Kirara Agent Framework](https://github.com/lss233/chatgpt-mirai-qq-bot) 的前端管理面板，旨在提供一个用户友好的界面，方便用户管理和配置文件。  

🚧 **当前正在施工中，欢迎有兴趣的同学与开发！** 🚧


## 截图展示

<!-- 这里可以添加一些项目截图 -->
<!-- 例如: -->
![Screenshot 1](docs/screenshots/1.png)
![Screenshot 2](docs/screenshots/2.png)

## 配置说明

### 环境变量

项目支持通过环境变量进行配置：

- `BASE_URL`: 应用的基础路径，默认为 `/`
  - 开发环境：`BASE_URL=/`
  - 生产环境示例：`BASE_URL=/chat/`

### 配置方法

1. 复制环境变量模板：
   ```bash
   cp .env.example .env
   ```

2. 编辑 `.env` 文件，设置所需的环境变量：
   ```bash
   # 设置应用基础路径
   BASE_URL=/your-path/
   ```

3. 重新启动开发服务器或重新构建项目

## 如何参与开发

我们非常欢迎您参与到 Kirara Agent Framework WebUI 的开发中！在参与开发之前，请务必先进行讨论，以确保您的贡献能够更好地融入到项目中。

1.  **参与讨论**：在开始开发之前，请先在 issue 或 QQ 群：701933732 中讨论，提出您的想法和建议。
2.  **发起 Pull Request**：在充分讨论后，您可以 fork 本仓库，进行开发，并在完成后发起 Pull Request。
3.  **等待合并**：我们会尽快审核您的 Pull Request，并在确认无误后将其合并到主分支中。

## License

本项目采用 [MIT License](LICENSE) 开源协议。

## Credits

*   感谢 [Vite](https://vitejs.dev/) 提供前端构建工具
*   感谢 [Vue 3](https://vuejs.org/) 提供前端框架
*   感谢 [Naive UI](https://www.naiveui.com/) 提供 UI 组件
*   感谢 [LiteGraph](https://github.com/comfyorg/litegraph.js) 提供流程图编辑器
*   感谢 [LobeHub Icons](https://github.com/LobeHub/lobe-hub-icons) 提供 LLM 图标
*   感谢所有为本项目做出贡献的开发者
