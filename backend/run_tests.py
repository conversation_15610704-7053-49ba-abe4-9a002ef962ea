#!/usr/bin/env python3
"""
测试运行器
运行所有测试文件
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def run_test(test_file: str, verbose: bool = False) -> bool:
    """运行单个测试文件"""
    print(f"\n{'='*60}")
    print(f"运行测试: {test_file}")
    print(f"{'='*60}")
    
    try:
        cmd = [sys.executable, test_file]
        if verbose:
            result = subprocess.run(cmd, cwd=Path(__file__).parent, check=True)
        else:
            result = subprocess.run(cmd, cwd=Path(__file__).parent, 
                                  capture_output=True, text=True, check=True)
            print(result.stdout)
            if result.stderr:
                print("STDERR:", result.stderr)
        
        print(f"✅ {test_file} 测试通过")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ {test_file} 测试失败")
        if not verbose:
            print("STDOUT:", e.stdout)
            print("STDERR:", e.stderr)
        return False
    except Exception as e:
        print(f"❌ {test_file} 运行出错: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description='运行测试')
    parser.add_argument('--test', '-t', help='运行指定的测试文件')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--list', '-l', action='store_true', help='列出所有测试文件')
    args = parser.parse_args()
    
    # 获取所有测试文件
    tests_dir = Path(__file__).parent / "tests"
    test_files = list(tests_dir.glob("test_*.py"))
    
    if args.list:
        print("可用的测试文件:")
        for test_file in test_files:
            print(f"  - {test_file.name}")
        return
    
    if args.test:
        # 运行指定测试
        test_name = args.test
        if not test_name.endswith('.py'):
            test_name = f"{test_name}.py"
        if not test_name.startswith('test_'):
            test_name = f"test_{test_name}"

        test_path = tests_dir / test_name

        if not test_path.exists():
            print(f"❌ 测试文件不存在: {test_name}")
            print(f"查找路径: {test_path}")
            return
        
        success = run_test(str(test_path), args.verbose)
        sys.exit(0 if success else 1)
    
    # 运行所有测试
    print(f"发现 {len(test_files)} 个测试文件")
    
    passed = 0
    failed = 0
    
    for test_file in sorted(test_files):
        if run_test(str(test_file), args.verbose):
            passed += 1
        else:
            failed += 1
    
    print(f"\n{'='*60}")
    print(f"测试总结")
    print(f"{'='*60}")
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📊 总计: {passed + failed}")
    
    if failed > 0:
        print(f"\n⚠️  有 {failed} 个测试失败")
        sys.exit(1)
    else:
        print(f"\n🎉 所有测试都通过了！")
        sys.exit(0)


if __name__ == "__main__":
    main()
