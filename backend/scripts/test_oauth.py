#!/usr/bin/env python3
"""
OAuth 测试脚本
用于测试 OAuth 配置和连接
"""

import asyncio
import httpx
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.config import settings

async def test_oauth_config():
    """测试 OAuth 配置端点"""
    print("=== 测试 OAuth 配置端点 ===")
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(
                f"http://localhost:8080/api/auth/oauth/config"
            )
            
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.text}")
            
            if response.status_code == 200:
                config = response.json()
                print("✅ OAuth 配置获取成功")
                print(f"Client ID: {config.get('client_id')}")
                print(f"授权 URL: {config.get('authorize_url')}")
                print(f"重定向 URI: {config.get('redirect_uri')}")
            else:
                print("❌ OAuth 配置获取失败")
                
    except Exception as e:
        print(f"❌ 请求失败: {e}")

async def test_oauth_server_connectivity():
    """测试 OAuth 服务器连通性"""
    print("\n=== 测试 OAuth 服务器连通性 ===")
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            # 测试令牌端点
            print(f"测试令牌端点: {settings.OIDC_TOKEN_URL}")
            response = await client.get(settings.OIDC_TOKEN_URL)
            print(f"令牌端点状态码: {response.status_code}")
            
            # 测试用户信息端点
            print(f"测试用户信息端点: {settings.OIDC_USERINFO_URL}")
            response = await client.get(settings.OIDC_USERINFO_URL)
            print(f"用户信息端点状态码: {response.status_code}")
            
            # 测试授权端点
            print(f"测试授权端点: {settings.OIDC_AUTHORIZATION_URL}")
            response = await client.get(settings.OIDC_AUTHORIZATION_URL)
            print(f"授权端点状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ OAuth 服务器连接失败: {e}")

async def test_permission_service():
    """测试权限服务"""
    print("\n=== 测试权限服务 ===")
    
    try:
        from app.services.user_permission import UserPermissionService
        
        service = UserPermissionService()
        # 使用测试令牌和用户ID
        result = await service.is_admin("test_token", "test_user")
        print(f"权限检查结果: {result}")
        print("✅ 权限服务测试完成")
        
    except Exception as e:
        print(f"❌ 权限服务测试失败: {e}")

def print_config():
    """打印当前配置"""
    print("=== 当前 OAuth 配置 ===")
    print(f"Client ID: {settings.OIDC_CLIENT_ID}")
    print(f"授权 URL: {settings.OIDC_AUTHORIZATION_URL}")
    print(f"令牌 URL: {settings.OIDC_TOKEN_URL}")
    print(f"用户信息 URL: {settings.OIDC_USERINFO_URL}")
    print(f"重定向 URI: {settings.OIDC_REDIRECT_URL}")
    print(f"IAM API URL: {settings.IAM_API_BASE_URL}")

async def main():
    """主函数"""
    print("OAuth 测试脚本")
    print("=" * 50)
    
    print_config()
    
    await test_oauth_config()
    await test_oauth_server_connectivity()
    await test_permission_service()
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    asyncio.run(main())
