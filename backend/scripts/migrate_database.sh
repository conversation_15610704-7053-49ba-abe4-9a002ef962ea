#!/bin/bash

# 数据库迁移脚本
# 从 ai_chat2 迁移到 nova_chat

set -e  # 遇到错误立即退出

# 数据库配置
OLD_DB_HOST="*************"
OLD_DB_PORT="5432"
OLD_DB_USER="root"
OLD_DB_NAME="ai_chat2"

NEW_DB_HOST="*************"
NEW_DB_PORT="5432"
NEW_DB_USER="nova_chat"
NEW_DB_NAME="nova_chat"
NEW_DB_PASSWORD="dNtHSpu1xd6HbhtLJEva"

# 备份文件路径
BACKUP_DIR="./backups"
BACKUP_FILE="$BACKUP_DIR/ai_chat2_backup_$(date +%Y%m%d_%H%M%S).sql"
SCHEMA_FILE="$BACKUP_DIR/ai_chat2_schema_$(date +%Y%m%d_%H%M%S).sql"
DATA_FILE="$BACKUP_DIR/ai_chat2_data_$(date +%Y%m%d_%H%M%S).sql"

echo "==================================="
echo "数据库迁移脚本"
echo "从 $OLD_DB_NAME 迁移到 $NEW_DB_NAME"
echo "==================================="

# 创建备份目录
mkdir -p $BACKUP_DIR

echo "步骤 1: 导出原数据库..."

# 导出完整数据库
echo "导出完整数据库到: $BACKUP_FILE"
pg_dump -h $OLD_DB_HOST -p $OLD_DB_PORT -U $OLD_DB_USER -d $OLD_DB_NAME -f $BACKUP_FILE

# 导出数据库结构
echo "导出数据库结构到: $SCHEMA_FILE"
pg_dump -h $OLD_DB_HOST -p $OLD_DB_PORT -U $OLD_DB_USER -d $OLD_DB_NAME --schema-only -f $SCHEMA_FILE

# 导出数据
echo "导出数据到: $DATA_FILE"
pg_dump -h $OLD_DB_HOST -p $OLD_DB_PORT -U $OLD_DB_USER -d $OLD_DB_NAME --data-only -f $DATA_FILE

echo "步骤 2: 创建新数据库和用户..."

# 创建新用户和数据库的SQL
cat > $BACKUP_DIR/create_db_user.sql << EOF
-- 创建新用户
CREATE USER $NEW_DB_USER WITH PASSWORD '$NEW_DB_PASSWORD';

-- 创建新数据库
CREATE DATABASE $NEW_DB_NAME OWNER $NEW_DB_USER;

-- 授权给用户
GRANT ALL PRIVILEGES ON DATABASE $NEW_DB_NAME TO $NEW_DB_USER;
EOF

echo "执行创建数据库和用户的SQL..."
echo "请手动执行以下命令（需要超级用户权限）："
echo "psql -h $NEW_DB_HOST -p $NEW_DB_PORT -U postgres -f $BACKUP_DIR/create_db_user.sql"

echo ""
echo "步骤 3: 设置数据库权限..."

# 创建权限设置SQL
cat > $BACKUP_DIR/set_permissions.sql << EOF
-- 连接到新数据库后执行
-- 授权用户对所有表的权限
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO $NEW_DB_USER;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO $NEW_DB_USER;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO $NEW_DB_USER;

-- 授权用户对未来创建的对象的权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO $NEW_DB_USER;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO $NEW_DB_USER;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO $NEW_DB_USER;

-- 授权用户创建表的权限
GRANT CREATE ON SCHEMA public TO $NEW_DB_USER;
EOF

echo "步骤 4: 导入数据到新数据库..."
echo "创建数据库和用户后，请执行以下命令导入数据："
echo "psql -h $NEW_DB_HOST -p $NEW_DB_PORT -U $NEW_DB_USER -d $NEW_DB_NAME -f $BACKUP_FILE"
echo ""
echo "然后设置权限："
echo "psql -h $NEW_DB_HOST -p $NEW_DB_PORT -U postgres -d $NEW_DB_NAME -f $BACKUP_DIR/set_permissions.sql"

echo ""
echo "==================================="
echo "迁移准备完成！"
echo "==================================="
echo "备份文件位置："
echo "- 完整备份: $BACKUP_FILE"
echo "- 结构备份: $SCHEMA_FILE"
echo "- 数据备份: $DATA_FILE"
echo "- 创建脚本: $BACKUP_DIR/create_db_user.sql"
echo "- 权限脚本: $BACKUP_DIR/set_permissions.sql"
echo ""
echo "新数据库连接信息："
echo "DATABASE_URL=postgresql://$NEW_DB_USER:$NEW_DB_PASSWORD@$NEW_DB_HOST:$NEW_DB_PORT/$NEW_DB_NAME"
echo ""
echo "请按照上述步骤手动执行数据库创建和导入操作。"
