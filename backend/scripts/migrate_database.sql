-- 数据库迁移脚本
-- 从 ai_chat2 迁移到 nova_chat

-- =====================================================
-- 第一步：创建新用户和数据库
-- =====================================================

-- 创建新用户 nova_chat（请替换为安全的密码）
CREATE USER nova_chat WITH PASSWORD 'dNtHSpu1xd6HbhtLJEva';

-- 创建新数据库 nova_chat
CREATE DATABASE nova_chat OWNER nova_chat;

-- 授权给用户 nova_chat
GRANT ALL PRIVILEGES ON DATABASE nova_chat TO nova_chat;

-- =====================================================
-- 第二步：连接到新数据库并设置权限
-- =====================================================

-- 连接到新数据库（需要手动执行）
-- \c nova_chat;

-- 授权用户对所有表的权限
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO nova_chat;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO nova_chat;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO nova_chat;

-- 授权用户对未来创建的对象的权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO nova_chat;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO nova_chat;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO nova_chat;

-- 授权用户创建表的权限
GRANT CREATE ON SCHEMA public TO nova_chat;

-- =====================================================
-- 注意事项：
-- =====================================================

-- 1. 执行此脚本前，请先导出原数据库：
--    pg_dump -h 192.168.20.87 -p 5432 -U root -d ai_chat2 -f ai_chat2_backup.sql

-- 2. 创建数据库和用户后，导入数据：
--    psql -h 192.168.20.87 -p 5432 -U nova_chat -d nova_chat -f ai_chat2_backup.sql

-- 3. 更新应用配置文件中的数据库连接字符串：
--    DATABASE_URL=**************************************************************/nova_chat
