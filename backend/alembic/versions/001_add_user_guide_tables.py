"""Add user guide tables

Revision ID: 001_add_user_guide_tables
Revises: 4a364dbb8dab
Create Date: 2025-07-21 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '001_add_user_guide_tables'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # 创建用户向导步骤表
    op.create_table('user_guide_steps',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('user_id', sa.String(length=64), nullable=False, server_default='default'),
        sa.Column('step_key', sa.String(length=64), nullable=False),
        sa.Column('completed', sa.<PERSON>(), nullable=False, server_default='false'),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('extra_data', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 创建用户向导设置表
    op.create_table('user_guide_settings',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('user_id', sa.String(length=64), nullable=False, server_default='default'),
        sa.Column('hide_guide', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('settings', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 创建索引
    op.create_index('idx_user_guide_steps_user_id', 'user_guide_steps', ['user_id'], unique=False)
    op.create_index('idx_user_guide_steps_step_key', 'user_guide_steps', ['step_key'], unique=False)
    op.create_index('idx_user_guide_steps_user_step', 'user_guide_steps', ['user_id', 'step_key'], unique=True)
    
    op.create_index('idx_user_guide_settings_user_id', 'user_guide_settings', ['user_id'], unique=True)


def downgrade() -> None:
    """Downgrade schema."""
    # 删除索引
    op.drop_index('idx_user_guide_settings_user_id', table_name='user_guide_settings')
    op.drop_index('idx_user_guide_steps_user_step', table_name='user_guide_steps')
    op.drop_index('idx_user_guide_steps_step_key', table_name='user_guide_steps')
    op.drop_index('idx_user_guide_steps_user_id', table_name='user_guide_steps')
    
    # 删除表
    op.drop_table('user_guide_settings')
    op.drop_table('user_guide_steps')
