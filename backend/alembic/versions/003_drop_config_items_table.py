"""Drop config_items table

Revision ID: 003_drop_config_items_table
Revises: 002_add_app_configs_table
Create Date: 2025-07-22 14:50:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = '003_drop_config_items_table'
down_revision: Union[str, None] = '002_add_app_configs_table'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # 删除 config_items 表
    op.drop_table('config_items')


def downgrade() -> None:
    """Downgrade schema."""
    # 重新创建 config_items 表（如果需要回滚）
    op.create_table('config_items',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('config_key', sa.String(length=255), nullable=False),
        sa.Column('config_value', sa.Text(), nullable=True),
        sa.Column('config_type', sa.String(length=32), nullable=False, server_default='string'),
        sa.Column('parent_key', sa.String(length=255), nullable=True),
        sa.Column('sort_order', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('description', sa.String(length=500), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 重新创建唯一约束
    op.create_unique_constraint('config_items_config_key_key', 'config_items', ['config_key'])
