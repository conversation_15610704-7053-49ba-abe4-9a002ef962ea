"""Update app_configs table structure for simplified configuration management

Revision ID: 002_add_app_configs_table
Revises: 001_add_user_guide_tables
Create Date: 2025-07-22 14:45:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = '002_add_app_configs_table'
down_revision: Union[str, None] = '001_add_user_guide_tables'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # 修改现有的 app_configs 表结构

    # 1. 重命名 config_type 列为 config_field
    op.alter_column('app_configs', 'config_type', new_column_name='config_field')

    # 2. 修改 config_field 列的长度
    op.alter_column('app_configs', 'config_field',
                   type_=sa.String(length=100),
                   existing_type=sa.String(length=64))

    # 3. 修改 config_data 列为可空
    op.alter_column('app_configs', 'config_data', nullable=True)

    # 4. 修改 description 列的长度
    op.alter_column('app_configs', 'description',
                   type_=sa.String(length=500),
                   existing_type=sa.String(length=255))

    # 5. 重命名唯一约束
    op.drop_constraint('app_configs_config_type_key', 'app_configs', type_='unique')
    op.create_unique_constraint('app_configs_config_field_key', 'app_configs', ['config_field'])


def downgrade() -> None:
    """Downgrade schema."""
    # 恢复原始表结构

    # 1. 恢复唯一约束名称
    op.drop_constraint('app_configs_config_field_key', 'app_configs', type_='unique')
    op.create_unique_constraint('app_configs_config_type_key', 'app_configs', ['config_field'])

    # 2. 恢复 description 列的长度
    op.alter_column('app_configs', 'description',
                   type_=sa.String(length=255),
                   existing_type=sa.String(length=500))

    # 3. 恢复 config_data 列为非空
    op.alter_column('app_configs', 'config_data', nullable=False)

    # 4. 恢复 config_field 列的长度
    op.alter_column('app_configs', 'config_field',
                   type_=sa.String(length=64),
                   existing_type=sa.String(length=100))

    # 5. 重命名 config_field 列为 config_type
    op.alter_column('app_configs', 'config_field', new_column_name='config_type')
