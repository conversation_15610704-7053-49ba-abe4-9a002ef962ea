-- PostgreSQL建表语句
-- 基于SQLite表结构转换而来

-- 创建用户向导步骤表
CREATE TABLE IF NOT EXISTS user_guide_steps (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(64) NOT NULL DEFAULT 'default',
    step_key VARCHAR(64) NOT NULL,
    completed BOOLEAN NOT NULL DEFAULT FALSE,
    completed_at TIMESTAMP WITH TIME ZONE,
    extra_data TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建用户向导设置表
CREATE TABLE IF NOT EXISTS user_guide_settings (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(64) NOT NULL DEFAULT 'default',
    hide_guide BOOLEAN NOT NULL DEFAULT FALSE,
    settings TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_guide_steps_user_id ON user_guide_steps(user_id);
CREATE INDEX IF NOT EXISTS idx_user_guide_steps_step_key ON user_guide_steps(step_key);
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_guide_steps_user_step ON user_guide_steps(user_id, step_key);
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_guide_settings_user_id ON user_guide_settings(user_id);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为user_guide_steps表创建更新时间触发器
DROP TRIGGER IF EXISTS update_user_guide_steps_updated_at ON user_guide_steps;
CREATE TRIGGER update_user_guide_steps_updated_at
    BEFORE UPDATE ON user_guide_steps
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 为user_guide_settings表创建更新时间触发器
DROP TRIGGER IF EXISTS update_user_guide_settings_updated_at ON user_guide_settings;
CREATE TRIGGER update_user_guide_settings_updated_at
    BEFORE UPDATE ON user_guide_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 添加表注释
COMMENT ON TABLE user_guide_steps IS '用户向导步骤表';
COMMENT ON COLUMN user_guide_steps.id IS '主键ID';
COMMENT ON COLUMN user_guide_steps.user_id IS '用户ID，默认为default';
COMMENT ON COLUMN user_guide_steps.step_key IS '步骤键名';
COMMENT ON COLUMN user_guide_steps.completed IS '是否已完成';
COMMENT ON COLUMN user_guide_steps.completed_at IS '完成时间';
COMMENT ON COLUMN user_guide_steps.extra_data IS '额外的元数据，JSON格式';
COMMENT ON COLUMN user_guide_steps.created_at IS '创建时间';
COMMENT ON COLUMN user_guide_steps.updated_at IS '更新时间';

COMMENT ON TABLE user_guide_settings IS '用户向导设置表';
COMMENT ON COLUMN user_guide_settings.id IS '主键ID';
COMMENT ON COLUMN user_guide_settings.user_id IS '用户ID，默认为default';
COMMENT ON COLUMN user_guide_settings.hide_guide IS '是否隐藏向导';
COMMENT ON COLUMN user_guide_settings.settings IS '其他设置，JSON格式';
COMMENT ON COLUMN user_guide_settings.created_at IS '创建时间';
COMMENT ON COLUMN user_guide_settings.updated_at IS '更新时间';
