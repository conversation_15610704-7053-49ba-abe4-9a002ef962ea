-- PostgreSQL简化建表语句

-- 创建用户向导步骤表
CREATE TABLE user_guide_steps (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(64) NOT NULL DEFAULT 'default',
    step_key VARCHAR(64) NOT NULL,
    completed B<PERSON><PERSON><PERSON>N NOT NULL DEFAULT FALSE,
    completed_at TIMESTAMP WITH TIME ZONE,
    extra_data TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建用户向导设置表
CREATE TABLE user_guide_settings (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(64) NOT NULL DEFAULT 'default',
    hide_guide BOOLEAN NOT NULL DEFAULT FALSE,
    settings TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_user_guide_steps_user_id ON user_guide_steps(user_id);
CREATE INDEX idx_user_guide_steps_step_key ON user_guide_steps(step_key);
CREATE UNIQUE INDEX idx_user_guide_steps_user_step ON user_guide_steps(user_id, step_key);
CREATE UNIQUE INDEX idx_user_guide_settings_user_id ON user_guide_settings(user_id);
