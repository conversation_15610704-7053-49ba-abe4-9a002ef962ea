"""
用户向导步骤数据库模型
"""

from datetime import datetime
from typing import Dict, Any
import json

from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean
from sqlalchemy.dialects.postgresql import UUID
import sqlalchemy as sa
import uuid

# 使用kirara_ai的Base类
try:
    from kirara_ai.database.manager import Base
except ImportError:
    # 如果无法导入，创建一个本地的Base
    from sqlalchemy.ext.declarative import declarative_base
    Base = declarative_base()


class UserGuideStep(Base):
    """用户向导步骤模型"""
    __tablename__ = 'user_guide_steps'

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(String(64), nullable=False, server_default='default')
    step_key = Column(String(64), nullable=False)
    completed = Column(Boolean, nullable=False, server_default='false')
    completed_at = Column(DateTime(timezone=True), nullable=True)
    extra_data = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP'))
    updated_at = Column(DateTime(timezone=True), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP'), onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f"<UserGuideStep(user_id='{self.user_id}', step_key='{self.step_key}', completed={self.completed})>"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'step_key': self.step_key,
            'completed': self.completed,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'metadata': json.loads(self.extra_data) if self.extra_data else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserGuideStep':
        """从字典创建实例"""
        instance = cls()
        for key, value in data.items():
            if key in ['created_at', 'updated_at', 'completed_at'] and value:
                if isinstance(value, str):
                    value = datetime.fromisoformat(value.replace('Z', '+00:00'))
            elif key == 'metadata' and value:
                if isinstance(value, dict):
                    value = json.dumps(value)
                # 将metadata映射到extra_data字段
                key = 'extra_data'
            
            if hasattr(instance, key):
                setattr(instance, key, value)
        
        return instance


class UserGuideSettings(Base):
    """用户向导设置模型"""
    __tablename__ = 'user_guide_settings'

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(String(64), nullable=False, server_default='default')
    hide_guide = Column(Boolean, nullable=False, server_default='false')
    settings = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP'))
    updated_at = Column(DateTime(timezone=True), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP'), onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f"<UserGuideSettings(user_id='{self.user_id}', hide_guide={self.hide_guide})>"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'hide_guide': self.hide_guide,
            'settings': json.loads(self.settings) if self.settings else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserGuideSettings':
        """从字典创建实例"""
        instance = cls()
        for key, value in data.items():
            if key in ['created_at', 'updated_at'] and value:
                if isinstance(value, str):
                    value = datetime.fromisoformat(value.replace('Z', '+00:00'))
            elif key == 'settings' and value:
                if isinstance(value, dict):
                    value = json.dumps(value)
            
            if hasattr(instance, key):
                setattr(instance, key, value)
        
        return instance
