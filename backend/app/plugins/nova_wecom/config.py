"""
Nova企微插件配置
"""

from pydantic import BaseModel, ConfigDict, Field
from typing import Any, Optional
from kirara_ai.ioc.container import DependencyContainer


class NovaWecomConfig(BaseModel):
    """Nova企微插件配置"""

    # 是否启用插件
    enabled: bool = Field(description="是否启用Nova企微插件")

    # 企业微信配置
    app_id: str = Field(title="应用ID", description="见微信侧显示")
    secret: str = Field(title="应用Secret", description="见微信侧显示")
    token: str = Field(title="Token", description="与微信侧填写保持一致")
    encoding_aes_key: str = Field(title="EncodingAESKey", description="请通过微信侧随机生成")
    corp_id: Optional[str] = Field(
        title="企业ID",
        description="企业微信后台显示的企业ID，微信公众号等场景无需填写。"
    )

    # Webhook配置
    webhook_url: str = Field(
        title="微信端回调地址",
        description="供微信端请求的 Webhook URL，填写在微信端"
    )

    # 调试模式
    debug: bool = Field(description="是否开启调试模式")

    model_config = ConfigDict(extra="allow")

    def __init__(self, **kwargs: Any):
        # 如果 agent_id 存在，则自动使用 agent_id 作为 app_id
        if "agent_id" in kwargs:
            kwargs["app_id"] = str(kwargs["agent_id"])
        super().__init__(**kwargs)


def get_nova_wecom_config(container: DependencyContainer) -> NovaWecomConfig:
    """获取Nova企微配置"""
    from app.plugins.nova_wecom.config import NovaWecomConfig as AbsoluteNovaWecomConfig
    return container.resolve(AbsoluteNovaWecomConfig)
