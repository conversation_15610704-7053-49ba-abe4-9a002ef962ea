"""
Nova企微插件
"""

from kirara_ai.logger import get_logger
from kirara_ai.plugin_manager.plugin import Plugin
from kirara_ai.ioc.container import DependencyContainer

from .config import NovaWecomConfig, get_nova_wecom_config


class NovaWecomPlugin(Plugin):
    """Nova企微插件"""
    
    def __init__(self):
        self.logger = get_logger("NovaWecomPlugin")
        self.config: NovaWecomConfig = None
        
    def on_load(self, container: DependencyContainer):
        """插件加载时调用"""
        self.logger.info("Nova企微插件加载完成")
        
    def on_start(self, container: DependencyContainer):
        """插件启动时调用"""
        try:
            # 获取配置
            self.config = get_nova_wecom_config(container)
            
            if not self.config.enabled:
                self.logger.info("Nova企微插件未启用")
                return
                
            self.logger.info("Nova企微插件启动完成")
            self.logger.info(f"Webhook地址: {self.config.webhook_url}")
            
            if self.config.debug:
                self.logger.debug("Nova企微插件调试模式已开启")
                
        except Exception as e:
            self.logger.error(f"Nova企微插件启动失败: {e}")
            raise
    
    def on_stop(self):
        """插件停止时调用"""
        self.logger.info("Nova企微插件已停止")
