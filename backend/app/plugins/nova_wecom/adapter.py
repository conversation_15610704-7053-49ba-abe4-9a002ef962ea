import asyncio
import os
import json
import base64
from io import Bytes<PERSON>
from typing import List, Optional, Any

import aiohttp
from fastapi import HTTPException, Request, Response
from starlette.routing import Route
from wechatpy.client import BaseWeChatClient
from wechatpy.enterprise.client.api.user import <PERSON><PERSON><PERSON><PERSON>ser
from wechatpy.replies import create_reply

from wechatpy.exceptions import InvalidSignatureException
from wechatpy.messages import BaseMessage

from kirara_ai.im.adapter import IMAdapter
from kirara_ai.im.message import (
    FileElement, ImageMessage, IMMessage, MessageElement, TextMessage, VideoElement,
    VoiceMessage
)
from app.plugins.nova_wecom.message import MarkdownMessage
from kirara_ai.im.sender import ChatSender
from kirara_ai.logger import get_logger
from kirara_ai.web.app import WebServer
from kirara_ai.workflow.core.dispatch.dispatcher import WorkflowDispatcher

from .delegates import CorpWechatApiDelegate, PublicWechatApiDelegate, WechatApiDelegate
from .config import NovaWecomConfig

from app.utils.lock import get_lock, Redlock
from app.utils.cache_utils import get_cache_client

WECOM_TEMP_DIR = os.path.join(os.getcwd(), 'data', 'temp', 'wecom')

# 保留WecomConfig作为兼容性别名
WecomConfig = NovaWecomConfig


class WeComUtils:
    """企业微信相关的工具类"""

    def __init__(self, client: BaseWeChatClient):
        self.client = client
        self.logger = get_logger("WeComUtils")

    @property
    def access_token(self) -> Optional[str]:
        return self.client.access_token

    async def download_and_save_media(self, media_id: str, file_name: str) -> Optional[str]:
        """下载并保存媒体文件到本地"""
        file_path = os.path.join(WECOM_TEMP_DIR, file_name)
        try:
            media_data = await self.download_media(media_id)
            if media_data:
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                with open(file_path, "wb") as f:
                    f.write(media_data)
                return file_path
        except Exception as e:
            self.logger.error(f"Failed to save media: {str(e)}")
        return None

    async def download_media(self, media_id: str) -> Optional[bytes]:
        """下载企业微信的媒体文件"""
        url = f"https://qyapi.weixin.qq.com/cgi-bin/media/get?access_token={self.access_token}&media_id={media_id}"
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        return await response.read()
                    self.logger.error(
                        f"Failed to download media: {response.status}")
        except Exception as e:
            self.logger.error(f"Failed to download media: {str(e)}")
        return None


class NovaWecomAdapter(IMAdapter):
    """企业微信适配器"""

    dispatcher: WorkflowDispatcher
    web_server: WebServer

    def __init__(self, config: NovaWecomConfig):
        self.wecom_utils = None
        self.wecom_user_client:WeChatUser = None
        self.api_delegate: Optional[WechatApiDelegate] = None
        self.config = config
        
        self.app = self.web_server.app

        self.logger = get_logger("Wecom-Adapter")
        self.is_running = False
        self.reply_tasks: dict[str, asyncio.Task] = {}

        # 根据配置选择合适的API代理
        self.setup_wechat_api()

    def setup_wechat_api(self):
        """根据配置设置微信API代理"""
        if self.config.corp_id:
            self.api_delegate = CorpWechatApiDelegate()
        else:
            self.api_delegate = PublicWechatApiDelegate()

        self.api_delegate.setup_api(self.config)

        # 设置工具类
        self.wecom_utils = WeComUtils(self.api_delegate.client)
        self.wecom_user_client = WeChatUser(self.api_delegate.client)

    def setup_routes(self):
        webhook_url = self.config.webhook_url
        # unregister old route if exists
        for route in self.app.routes:
            if isinstance(route, Route) and route.path == webhook_url:
                self.app.routes.remove(route)

        @self.app.get(webhook_url)
        async def handle_check_request(request: Request):
            """处理 GET 请求"""
            if not self.is_running:
                self.logger.warning("Wecom-Adapter is not running, skipping check request.")
                raise HTTPException(status_code=404)
            
            assert self.api_delegate is not None

            signature = request.query_params.get("msg_signature", "")
            if not signature:
                signature = request.query_params.get("signature", "")
            timestamp = request.query_params.get("timestamp", "")
            nonce = request.query_params.get("nonce", "")
            echo_str = request.query_params.get("echostr", "")

            try:
                echo_str = self.api_delegate.check_signature(
                    signature, timestamp, nonce, echo_str
                )
                return Response(content=echo_str, media_type="text/plain")
            except InvalidSignatureException:
                self.logger.error("failed to check signature, please check your settings.")
                raise HTTPException(status_code=403)

        @self.app.post(webhook_url)
        async def handle_message(request: Request):
            """处理 POST 请求"""
            if not self.is_running:
                self.logger.warning("Wecom-Adapter is not running, skipping message request.")
                raise HTTPException(status_code=404)
            
            assert self.api_delegate is not None
            assert self.wecom_utils is not None
            
            signature = request.query_params.get("msg_signature", "")
            if not signature:
                signature = request.query_params.get("signature", "")
            timestamp = request.query_params.get("timestamp", "")
            nonce = request.query_params.get("nonce", "")
            try:
                msg_str = self.api_delegate.decrypt_message(
                    await request.body(), signature, timestamp, nonce
                )
            except InvalidSignatureException:
                self.logger.error("failed to check signature, please check your settings.")
                raise HTTPException(status_code=403)
            msg: BaseMessage = self.api_delegate.parse_message(msg_str)

            # redis锁
            msg_id = msg.id
            lock: Redlock = get_lock(key=f"wecom_msg_lock:{msg_id}",auto_release_time=300)
            if lock.acquire(timeout=0.01) is False:
                self.logger.debug(f"skip processing due to duplicate msgid: {msg.id}")
                reply = "消息处理中，请稍候..."
                return Response(content=create_reply(reply, msg, render=True), media_type="text/xml")

            # 预处理媒体消息
            media_path = None
            if msg.type in ["voice", "video", "file"]:
                media_id = msg.media_id
                file_name = f"temp_{msg.type}_{media_id}.{msg.type}"
                media_path = await self.wecom_utils.download_and_save_media(media_id, file_name)

            # 转换消息
            message = await self.convert_to_message(msg, media_path)
            self.reply_tasks[msg.id] = asyncio.Future() # type: ignore
            
            message.sender.raw_metadata["reply"] = self.reply_tasks[msg.id] # type: ignore
            # 分发消息
            asyncio.create_task(self.dispatcher.dispatch(self, message))
            reply = await message.sender.raw_metadata["reply"]
            del message.sender.raw_metadata["reply"]
            content = create_reply(reply, msg, render=True)
            lock.release()
            return Response(content=content, media_type="text/xml")

    async def convert_to_message(self, raw_message: BaseMessage, media_path: Optional[str] = None) -> IMMessage:
        """将企业微信消息转换为统一消息格式"""
        # 企业微信应用似乎没有群聊的概念，所以这里只能用单聊
        sender = self._get_message_sender(raw_message)

        message_elements: List[MessageElement] = []
        raw_message_dict = raw_message.__dict__

        if raw_message.type == "text":
            message_elements.append(TextMessage(text=raw_message.content))
        elif raw_message.type == "image":
            message_elements.append(ImageMessage(url=raw_message.image))
        elif raw_message.type == "voice" and media_path:
            message_elements.append(VoiceMessage(url=media_path))
        elif raw_message.type == "video" and media_path:
            message_elements.append(VideoElement(path=media_path))
        elif raw_message.type == "file" and media_path:
            message_elements.append(FileElement(path=media_path))
        elif raw_message.type == "location":
            location_text = f"[Location] {raw_message.label} (X: {raw_message.location_x}, Y: {raw_message.location_y})"
            message_elements.append(TextMessage(text=location_text))
        elif raw_message.type == "link":
            link_text = f"[Link] {raw_message.title}: {raw_message.description} ({raw_message.url})"
            message_elements.append(TextMessage(text=link_text))
        else:
            message_elements.append(TextMessage(
                text=f"Unsupported message type: {raw_message.type}"))

        return IMMessage(
            sender=sender,
            message_elements=message_elements,
            raw_message=raw_message_dict,
        )
    
    def _get_message_sender(self, raw_message: BaseMessage) -> ChatSender:
        user_id:str = raw_message.source
        user_data = self._get_user_by_user_id(user_id=user_id)
        display_name = user_data.get('name') if isinstance(user_data, dict) else user_id
        return ChatSender.from_c2c_chat(user_id, display_name=display_name)
    
    def _get_user_by_user_id(self, user_id):
        """从企微获取用户信息，带缓存"""
        client = get_cache_client()
        cache_key = f"wecom_user_data:{user_id}"
        user_data = client.get(cache_key)
        if user_data:
            return json.loads(user_data)
        
        user_data = self.wecom_user_client.get(user_id=user_id)
        self.logger.debug(f"调用企业微信用户信息接口, user_id={user_id}, user_data={user_data}")
        if user_data:
            client.set(cache_key, json.dumps(user_data), ex=86400)
        return user_data

    async def _send_text(self, user_id: str, text: str):
        """发送文本消息"""
        assert self.api_delegate is not None
        try:
            return await self.api_delegate.send_text(self.config.app_id, user_id, text)
        except Exception as e:
            self.logger.error(f"Failed to send text message: {e}")
            raise e
    
    async def _send_markdown(self, user_id: str, markdown: str):
        """发送Markdown消息"""
        assert self.api_delegate is not None
        try:
            return await self.api_delegate.send_markdown(self.config.app_id, user_id, markdown)
        except Exception as e:
            self.logger.error(f"Failed to send markdown message: {e}")
            raise e

    async def _send_media(self, user_id: str, media_data: str, media_type: str):
        """发送媒体消息的通用方法"""
        assert self.api_delegate is not None
        try:
            media_bytes = BytesIO(base64.b64decode(media_data))
            return await self.api_delegate.send_media(self.config.app_id, user_id, media_type, media_bytes)
        except Exception as e:
            self.logger.error(f"Failed to send {media_type} message: {e}")
            raise e

    async def send_message(self, message: IMMessage, recipient: ChatSender):
        """发送消息到企业微信"""
        user_id = recipient.user_id
        res = None

        try:
            for element in message.message_elements:
                if isinstance(element, TextMessage) and element.text:
                    res = await self._send_text(user_id, element.text)
                elif isinstance(element, MarkdownMessage) and element.markdown:
                    res = await self._send_markdown(user_id, element.markdown)
                elif isinstance(element, ImageMessage) and element.url:
                    res = await self._send_media(user_id, element.url, "image")
                elif isinstance(element, VoiceMessage) and element.url:
                    res = await self._send_media(user_id, element.url, "voice")
                elif isinstance(element, VideoElement) and element.path:
                    res = await self._send_media(user_id, element.path, "video")
                elif isinstance(element, FileElement) and element.path:
                    res = await self._send_media(user_id, element.path, "file")
            if res:
                print(res)
            if recipient.raw_metadata and "reply" in recipient.raw_metadata:
                recipient.raw_metadata["reply"].set_result(None)
        except Exception as e:
            if 'Error code: 48001' in str(e):
                # 未开通主动回复能力
                if recipient.raw_metadata and "reply" in recipient.raw_metadata:
                    self.logger.warning("未开通主动回复能力，将采用被动回复消息 API，此模式下只能回复一条消息。")
                    recipient.raw_metadata["reply"].set_result(message.content)
                else:
                    self.logger.warning("未开通主动回复能力，且不在上下文中，无法发送消息。")


    async def start(self):
        self.setup_wechat_api()
        self.setup_routes()
        self.is_running = True
        self.logger.info("Wecom-Adapter 启动成功")

    async def stop(self):
        self.is_running = False
        self.logger.info("Wecom-Adapter 停止成功")