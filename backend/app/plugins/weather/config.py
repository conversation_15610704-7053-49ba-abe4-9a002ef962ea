"""
天气查询插件配置 - 使用高德地图API
"""
from pydantic import BaseModel, Field

from kirara_ai.ioc.container import DependencyContainer


class WeatherConfig(BaseModel):
    """天气插件配置类"""

    # API 配置
    api_key: str = Field(
        default="48b136f258e235923e6ea97556fa77b7",
        description="高德地图API密钥"
    )

    api_base_url: str = Field(
        default="https://restapi.amap.com/v3",
        description="高德地图API基础URL"
    )

    timeout: int = Field(
        default=10,
        description="请求超时时间（秒）"
    )

    # 默认配置
    extensions: str = Field(
        default="all",
        description="天气信息扩展：base=实况天气, all=预报天气"
    )

    output: str = Field(
        default="json",
        description="返回格式：json或xml"
    )

    # 缓存配置
    cache_enabled: bool = Field(
        default=False,
        description="是否启用缓存"
    )

    cache_duration: int = Field(
        default=600,
        description="缓存时间（秒），默认10分钟"
    )

    @property
    def is_api_configured(self) -> bool:
        """检查API是否已配置"""
        return self.api_key != 'your_amap_api_key_here' and bool(self.api_key)

    def get_weather_url(self) -> str:
        """获取天气查询URL"""
        return f"{self.api_base_url}/weather/weatherInfo"

    def get_geocode_url(self) -> str:
        """获取地理编码URL（用于获取城市的adcode）"""
        return f"{self.api_base_url}/geocode/geo"


def get_weather_config(container:DependencyContainer) -> WeatherConfig:
    """获取天气插件配置"""
    from app.plugins.weather.config import WeatherConfig as AbsoluteWeatherConfig
    return container.resolve(AbsoluteWeatherConfig)
