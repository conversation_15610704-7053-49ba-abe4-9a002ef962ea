"""
天气查询服务
处理天气数据的获取和格式化
"""
import aiohttp
import time
import re
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from kirara_ai.logger import get_logger
from kirara_ai.ioc.container import DependencyContainer
from .config import get_weather_config


class WeatherCache:
    """简单的内存缓存"""

    def __init__(self, container: DependencyContainer):
        self._cache: Dict[str, Dict] = {}
        self.logger = get_logger("WeatherCache")
        self.container = container
        self.weather_config = get_weather_config(container)
    
    def get(self, key: str) -> Optional[Dict]:
        """获取缓存数据"""
        if not self.weather_config.cache_enabled:
            return None

        if key in self._cache:
            data = self._cache[key]
            if time.time() - data['timestamp'] < self.weather_config.cache_duration:
                self.logger.debug(f"缓存命中: {key}")
                return data['value']
            else:
                # 缓存过期，删除
                del self._cache[key]
                self.logger.debug(f"缓存过期: {key}")

        return None
    
    def set(self, key: str, value: Dict) -> None:
        """设置缓存数据"""
        if self.weather_config.cache_enabled:
            self._cache[key] = {
                'value': value,
                'timestamp': time.time()
            }
            self.logger.debug(f"缓存设置: {key}")


class WeatherService:
    """天气查询服务"""

    def __init__(self, container: DependencyContainer):
        self.logger = get_logger("WeatherService")
        self.container = container
        self.weather_config = get_weather_config(container)
        self.cache = WeatherCache(container)
        self.session: Optional[aiohttp.ClientSession] = None

    def parse_date_from_query(self, query: str) -> Optional[str]:
        """从查询中解析日期"""
        today = datetime.now()

        # 日期关键词映射
        date_patterns = {
            r'明(天|日)的明(天|日)': today + timedelta(days=2),
            r'大后天': today + timedelta(days=3),
            r'后天': today + timedelta(days=2),
            r'明天|明日': today + timedelta(days=1),
            r'今天|今日|今晚': today,
        }

        for pattern, target_date in date_patterns.items():
            if re.search(pattern, query):
                return target_date.strftime('%Y-%m-%d')

        # 匹配具体日期格式，如 "7月16日"、"7-16"、"2025-07-16"
        date_formats = [
            r'(\d{1,2})月(\d{1,2})日?',  # 7月16日
            r'(\d{1,2})-(\d{1,2})',     # 7-16
            r'(\d{4})-(\d{1,2})-(\d{1,2})',  # 2025-07-16
        ]

        for pattern in date_formats:
            match = re.search(pattern, query)
            if match:
                groups = match.groups()
                if len(groups) == 2:  # 月日格式
                    month, day = groups
                    target_date = today.replace(month=int(month), day=int(day))
                    return target_date.strftime('%Y-%m-%d')
                elif len(groups) == 3:  # 年月日格式
                    year, month, day = groups
                    target_date = datetime(int(year), int(month), int(day))
                    return target_date.strftime('%Y-%m-%d')

        return None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.weather_config.timeout)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def get_current_weather(self, city: str) -> Dict[str, Any]:
        """获取当前天气"""
        return await self.get_weather_by_date(city, None)

    async def get_weather_by_date(self, city: str, date: Optional[str] = None) -> Dict[str, Any]:
        """获取指定日期的天气"""
        cache_key = f"weather_{city}_{date or 'current'}"

        # 尝试从缓存获取
        cached_data = self.cache.get(cache_key)
        if cached_data:
            return cached_data

        try:
            # 调用真实API
            weather_data = await self._fetch_weather_by_date(city, date)
            # 缓存结果
            self.cache.set(cache_key, weather_data)
            return weather_data

        except Exception as e:
            self.logger.error(f"获取天气失败: {e}")
            # 返回错误信息
            return {
                'success': False,
                'error': str(e),
                'city': city,
                'date': date
            }

    async def get_weather_forecast(self, city: str, days: int = 3) -> Dict[str, Any]:
        """获取多天天气预报"""
        cache_key = f"forecast_{city}_{days}days"

        # 尝试从缓存获取
        cached_data = self.cache.get(cache_key)
        if cached_data:
            return cached_data

        try:
            # 调用真实API
            weather_data = await self._fetch_weather_forecast(city, days)

            # 缓存结果
            self.cache.set(cache_key, weather_data)
            return weather_data

        except Exception as e:
            self.logger.error(f"获取天气预报失败: {e}")
            # 返回错误信息
            return {
                'success': False,
                'error': str(e),
                'city': city,
                'days': days
            }
    
    async def _fetch_weather_by_date(self, city: str, date: Optional[str] = None) -> Dict[str, Any]:
        """从高德地图API获取指定日期的天气"""
        return await self._fetch_current_weather(city)  # 高德API主要提供当前天气和预报

    async def _fetch_weather_forecast(self, city: str, days: int) -> Dict[str, Any]:
        """从高德地图API获取天气预报"""
        return await self._fetch_current_weather(city)  # 高德API的预报数据包含在同一个接口中

    async def _fetch_current_weather(self, city: str) -> Dict[str, Any]:
        """从高德地图API获取当前天气"""
        if not self.session:
            raise RuntimeError("WeatherService 未正确初始化")

        try:
            # 首先获取城市的adcode
            adcode = await self._get_city_adcode(city)
            if not adcode:
                return {
                    'success': False,
                    'error': f"找不到城市 '{city}'，请检查城市名称是否正确",
                    'city': city
                }

            # 使用adcode查询天气
            url = self.weather_config.get_weather_url()
            params = {
                'key': self.weather_config.api_key,
                'city': adcode,
                'extensions': self.weather_config.extensions,
                'output': self.weather_config.output
            }

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('status') == '1':  # 高德API成功状态
                        return {
                            'success': True,
                            'data': data,
                            'city': city,
                            'source': 'amap'
                        }
                    else:
                        return {
                            'success': False,
                            'error': f"高德API错误: {data.get('info', '未知错误')}",
                            'city': city
                        }
                else:
                    error_text = await response.text()
                    return {
                        'success': False,
                        'error': f"API请求失败 (状态码: {response.status}): {error_text}",
                        'city': city
                    }
        except Exception as e:
            return {
                'success': False,
                'error': f"请求异常: {str(e)}",
                'city': city
            }

    async def _get_city_adcode(self, city: str) -> Optional[str]:
        """获取城市的adcode（行政区划代码）"""
        try:
            url = self.weather_config.get_geocode_url()
            params = {
                'key': self.weather_config.api_key,
                'address': city,
                'output': self.weather_config.output
            }

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('status') == '1' and data.get('geocodes'):
                        # 返回第一个匹配结果的adcode
                        return data['geocodes'][0].get('adcode')
            return None
        except Exception as e:
            self.logger.error(f"获取城市adcode失败: {e}")
            return None
    

class WeatherFormatter:
    """天气信息格式化器"""

    @staticmethod
    def format_current_weather(weather_result: Dict[str, Any]) -> str:
        """格式化当前天气信息（高德API格式）"""
        if not weather_result.get('success'):
            error = weather_result.get('error', '未知错误')
            city = weather_result.get('city', '未知城市')
            return f"❌ 获取{city}天气失败：{error}"

        data = weather_result['data']
        source = weather_result.get('source', 'unknown')

        try:
            # 处理高德API格式
            if 'lives' in data and data['lives']:
                # 实时天气数据
                live_data = data['lives'][0]
                city = live_data['city']
                weather = live_data['weather']
                temperature = live_data['temperature']
                humidity = live_data['humidity']
                wind_direction = live_data['winddirection']
                wind_power = live_data['windpower']
                report_time = live_data['reporttime']

                # 选择合适的天气图标
                weather_icon = WeatherFormatter._get_weather_icon_chinese(weather)

                result = f"""📍 {city} 天气信息

{weather_icon} 天气：{weather}
🌡️ 温度：{temperature}°C
💧 湿度：{humidity}%
💨 风向：{wind_direction} {wind_power}
🕐 更新时间：{report_time}"""

                # 如果有预报数据，添加今日预报
                if 'forecasts' in data and data['forecasts'] and data['forecasts'][0].get('casts'):
                    cast = data['forecasts'][0]['casts'][0]
                    day_temp = cast['daytemp']
                    night_temp = cast['nighttemp']
                    day_weather = cast['dayweather']
                    night_weather = cast['nightweather']

                    result += f"""

📅 今日预报：
🌅 白天：{day_weather} {day_temp}°C
🌙 夜间：{night_weather} {night_temp}°C"""

            elif 'forecasts' in data and data['forecasts']:
                # 只有预报数据的情况（模拟数据）
                forecast_data = data['forecasts'][0]
                city = forecast_data['city']

                if forecast_data.get('casts'):
                    cast = forecast_data['casts'][0]
                    day_temp = cast['daytemp']
                    night_temp = cast['nighttemp']
                    day_weather = cast['dayweather']
                    night_weather = cast['nightweather']

                    # 选择合适的天气图标
                    weather_icon = WeatherFormatter._get_weather_icon_chinese(day_weather)

                    result = f"""📍 {city} 天气信息

{weather_icon} 天气：{day_weather}
🌡️ 温度：{day_temp}°C
📅 今日预报：
🌅 白天：{day_weather} {day_temp}°C
🌙 夜间：{night_weather} {night_temp}°C"""
                else:
                    return f"❌ 天气数据格式异常：预报数据缺失"
            else:
                return f"❌ 天气数据格式异常：无法解析高德API返回数据"

            # 如果是模拟数据，添加提示
            if source == 'mock':
                result += "\n\n💡 *这是模拟数据，如需真实天气信息，请在高德开放平台申请API密钥*"
            elif source == 'amap':
                result += "\n\n📡 *数据来源：高德地图*"

            return result

        except KeyError as e:
            return f"❌ 天气数据格式异常：缺少字段 {e}"

    @staticmethod
    def _get_weather_icon_chinese(weather: str) -> str:
        """根据中文天气描述获取图标"""
        icon_map = {
            '晴': '☀️',
            '多云': '⛅',
            '阴': '☁️',
            '小雨': '🌦️',
            '中雨': '🌧️',
            '大雨': '⛈️',
            '暴雨': '🌩️',
            '雷阵雨': '⛈️',
            '雪': '❄️',
            '小雪': '🌨️',
            '中雪': '❄️',
            '大雪': '🌨️',
            '雾': '🌫️',
            '霾': '😷',
            '沙尘暴': '🌪️'
        }
        return icon_map.get(weather, '🌤️')

    @staticmethod
    def format_weather_by_date(weather_result: Dict[str, Any], target_date: str) -> str:
        """格式化指定日期的天气信息"""
        if not weather_result.get('success'):
            error = weather_result.get('error', '未知错误')
            city = weather_result.get('city', '未知城市')
            return f"❌ 获取{city} {target_date} 天气失败：{error}"

        data = weather_result['data']
        source = weather_result.get('source', 'unknown')

        try:
            if 'forecasts' in data and data['forecasts']:
                forecast_data = data['forecasts'][0]
                city = forecast_data['city']
                casts = forecast_data.get('casts', [])

                # 查找指定日期的预报
                target_cast = None
                for cast in casts:
                    if cast['date'] == target_date:
                        target_cast = cast
                        break

                if target_cast:
                    day_weather = target_cast['dayweather']
                    night_weather = target_cast['nightweather']
                    day_temp = target_cast['daytemp']
                    night_temp = target_cast['nighttemp']
                    day_wind = target_cast['daywind']
                    day_power = target_cast['daypower']

                    # 选择合适的天气图标
                    weather_icon = WeatherFormatter._get_weather_icon_chinese(day_weather)

                    # 格式化日期显示
                    date_obj = datetime.strptime(target_date, '%Y-%m-%d')
                    weekdays = ['一', '二', '三', '四', '五', '六', '日']
                    weekday = weekdays[date_obj.weekday()]
                    date_display = f"{date_obj.month}月{date_obj.day}日 周{weekday}"

                    result = f"""📍 {city} {date_display} 天气预报

{weather_icon} 天气：{day_weather}
🌡️ 温度：{day_temp}°C / {night_temp}°C
💨 风向：{day_wind} {day_power}

📅 详细预报：
🌅 白天：{day_weather} {day_temp}°C
🌙 夜间：{night_weather} {night_temp}°C"""

                    # 如果是模拟数据，添加提示
                    if source == 'mock':
                        result += "\n\n💡 *这是模拟数据，如需真实天气信息，请在高德开放平台申请API密钥*"
                    elif source == 'amap':
                        result += "\n\n📡 *数据来源：高德地图*"

                    return result
                else:
                    return f"❌ 未找到 {city} {target_date} 的天气预报数据"
            else:
                return f"❌ 天气数据格式异常：无法解析预报数据"

        except Exception as e:
            return f"❌ 天气数据格式异常：{str(e)}"

    @staticmethod
    def format_weather_forecast(weather_result: Dict[str, Any]) -> str:
        """格式化多天天气预报"""
        if not weather_result.get('success'):
            error = weather_result.get('error', '未知错误')
            city = weather_result.get('city', '未知城市')
            days = weather_result.get('days', '未知')
            return f"❌ 获取{city} {days}天天气预报失败：{error}"

        data = weather_result['data']
        source = weather_result.get('source', 'unknown')
        days = weather_result.get('days', 3)

        try:
            if 'forecasts' in data and data['forecasts']:
                forecast_data = data['forecasts'][0]
                city = forecast_data['city']
                casts = forecast_data.get('casts', [])

                result = f"📍 {city} {days}天天气预报\n"

                for i, cast in enumerate(casts[:days]):
                    date_obj = datetime.strptime(cast['date'], '%Y-%m-%d')
                    weekdays = ['一', '二', '三', '四', '五', '六', '日']
                    weekday = weekdays[date_obj.weekday()]

                    if i == 0:
                        date_display = "今天"
                    elif i == 1:
                        date_display = "明天"
                    elif i == 2:
                        date_display = "后天"
                    else:
                        date_display = f"{date_obj.month}月{date_obj.day}日"

                    day_weather = cast['dayweather']
                    day_temp = cast['daytemp']
                    night_temp = cast['nighttemp']

                    weather_icon = WeatherFormatter._get_weather_icon_chinese(day_weather)

                    result += f"\n{weather_icon} {date_display} (周{weekday})：{day_weather} {day_temp}°C / {night_temp}°C"

                # 如果是模拟数据，添加提示
                if source == 'mock':
                    result += "\n\n💡 *这是模拟数据，如需真实天气信息，请在高德开放平台申请API密钥*"
                elif source == 'amap':
                    result += "\n\n📡 *数据来源：高德地图*"

                return result
            else:
                return f"❌ 天气数据格式异常：无法解析预报数据"

        except Exception as e:
            return f"❌ 天气数据格式异常：{str(e)}"
