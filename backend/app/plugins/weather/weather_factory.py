from kirara_ai.workflow.core.workflow.builder import WorkflowBuilder
from kirara_ai.workflow.implementations.blocks.im.messages import GetIMMessage, SendIMMessage
from .weather_block import WeatherQuery


class WeatherWorkflowFactory:
    """天气查询相关工作流工厂"""

    @staticmethod
    def create_weather_query_workflow() -> WorkflowBuilder:
        """创建天气查询工作流"""
        workflow_builder = (
            WorkflowBuilder("天气查询")
            .use(GetIMMessage)
            .chain(WeatherQuery)
            .chain(SendIMMessage)
        )
        workflow_builder.description ="""
        查询指定城市的天气信息，支持查询当前天气、指定日期天气和多天天气预报。
        示例用法：
        - 查询当前天气：北京天气
        - 查询指定日期天气：上海明天天气
        - 查询多天天气预报：广州未来几天天气
        - 查询指定日期天气：深圳7月16日天气
        - 查询天气情况： 北京明天下雨吗
        """
        return workflow_builder
