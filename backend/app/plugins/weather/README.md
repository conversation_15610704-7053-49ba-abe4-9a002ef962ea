# 天气查询插件

这是一个为 kirara-ai 开发的天气查询插件，使用高德地图API提供准确的天气信息查询服务。

## 功能特性

- 🌤️ 实时天气查询
- 📅 指定日期天气查询（今天、明天、后天、具体日期）
- 📊 多天天气预报（3天预报）
- 🏙️ 支持全国城市查询
- 📊 详细天气信息（温度、湿度、风向、风力等）
- 🕐 智能日期解析（支持多种日期格式）
- 💾 智能缓存机制（可配置）
- 🔄 API失败时自动降级到模拟数据
- 🎯 智能关键词匹配
- 🆓 使用高德地图免费API

## 支持的查询格式

### 当前天气查询
- `北京天气`
- `上海的天气`
- `查询广州天气`
- `深圳天气怎么样`
- `杭州天气如何`
- `成都的天气情况`
- `武汉气温`
- `西安温度`

### 指定日期天气查询
- `北京今天天气`
- `上海明天天气`
- `广州后天天气`
- `深圳大后天天气`
- `杭州7月16日天气`
- `成都7-16天气`
- `武汉2025-07-16天气`

### 多天天气预报查询
- `北京天气预报`
- `上海未来几天天气`
- `广州多天天气`

## 配置说明

### 环境变量配置

为了获取真实的天气数据，需要配置高德地图API密钥：

```bash
export AMAP_API_KEY="your_amap_api_key"
```

### 获取API密钥

1. 访问 [高德开放平台](https://console.amap.com/)
2. 注册账号并创建应用
3. 申请"Web服务API"密钥（免费额度：每日100万次调用）
4. 将API密钥设置为环境变量

### 配置选项

插件支持以下配置选项（在 `config.py` 中修改）：

- `api_key`: 高德地图API密钥
- `timeout`: API请求超时时间（默认10秒）
- `cache_duration`: 缓存时间（默认10分钟）
- `extensions`: 天气信息类型（base:实况天气, all:预报天气）
- `output`: 返回格式（json/xml）

## 安装和启用

1. 将插件文件放置在 `backend/app/plugins/weather_query/` 目录下
2. 重启 kirara-ai 服务
3. 插件会自动加载并注册天气查询工作流

## 使用示例

### 当前天气查询
```
用户: 北京天气
AI: 📍 北京 天气信息

☀️ 天气：晴
🌡️ 温度：25°C
💧 湿度：45%
💨 风向：南风 3级
🕐 更新时间：2025-07-16 11:00:00

📅 今日预报：
🌅 白天：晴 28°C
🌙 夜间：多云 20°C

📡 *数据来源：高德地图*
```

### 指定日期天气查询
```
用户: 北京明天天气
AI: 📍 北京 7月17日 周四 天气预报

☀️ 天气：晴
🌡️ 温度：28°C / 20°C
💨 风向：南风 3级

📅 详细预报：
🌅 白天：晴 28°C
🌙 夜间：多云 20°C

📡 *数据来源：高德地图*
```

### 多天天气预报
```
用户: 北京天气预报
AI: 📍 北京 3天天气预报

☀️ 今天 (周三)：晴 28°C / 20°C
⛅ 明天 (周四)：多云 26°C / 18°C
🌧️ 后天 (周五)：小雨 22°C / 16°C

📡 *数据来源：高德地图*
```

## 技术架构

### 核心组件

1. **WeatherPlugin**: 主插件类，负责插件生命周期管理
2. **WeatherQueryBlock**: 工作流处理块，处理天气查询逻辑
3. **WeatherService**: 天气服务类，负责API调用和数据处理
4. **WeatherFormatter**: 格式化器，负责天气信息的格式化显示
5. **WeatherCache**: 缓存管理器，提供智能缓存功能

### 工作流程

1. 用户发送包含天气关键词的消息
2. 插件通过正则表达式提取城市名称
3. 查询缓存，如果有效则直接返回
4. 调用天气API获取数据
5. 格式化天气信息并返回给用户
6. 将结果缓存以提高响应速度

## 错误处理

- API密钥未配置：自动使用模拟数据
- 网络请求失败：降级到模拟数据
- 城市名称无效：提示用户检查输入
- API限额超出：显示相应错误信息

## 扩展功能

插件设计为可扩展的架构，可以轻松添加以下功能：

- 天气预报（未来几天）
- 天气预警信息
- 空气质量查询
- 多语言支持
- 自定义天气API提供商

## 依赖项

- `aiohttp`: 异步HTTP客户端
- `kirara-ai`: 核心框架

## 许可证

本插件遵循与 kirara-ai 相同的许可证。
