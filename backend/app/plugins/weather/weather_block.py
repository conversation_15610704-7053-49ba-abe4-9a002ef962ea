import re
from typing import Any, Dict
import asyncio
import concurrent.futures

from kirara_ai.ioc.container import DependencyContainer
from app.plugins.llm_dispatch.types import LLMAnalysisParams
from kirara_ai.im.message import IMMessage, TextMessage
from kirara_ai.im.sender import ChatSender
from kirara_ai.workflow.core.block import Block
from kirara_ai.workflow.core.block.input_output import Input, Output
from .weather_service import WeatherService, WeatherFormatter


class WeatherQuery(Block):
    """天气查询 block"""

    name = "weather_query"
    inputs = {
        "message": Input("message", "输入消息", IMMessage, "输入消息包含天气查询命令")
    }
    outputs = {
        "response": Output(
            "response", "响应消息", IMMessage, "响应消息包含天气查询结果"
        )
    }

    def execute(self, message: IMMessage) -> Dict[str, Any]:
        # 解析天气查询命令
        command = message.content
        

        city = None
        target_date = None

        container: DependencyContainer = self.container
        if container:
            try:
                params: LLMAnalysisParams = container.resolve(LLMAnalysisParams)
                if params:
                    city = params.get('city', '')
                    target_date = params.get('date', '')
            except Exception:
                # 如果无法解析参数，使用默认值
                pass

        if not city:
            city = self._extract_city_name(command)
        
            if not city:
                return {
                    "response": IMMessage(
                        sender=ChatSender.get_bot_sender(),
                        message_elements=[TextMessage("请提供要查询的城市名称，例如：北京天气、上海天气怎么样？")],
                    )
                }
        if not target_date:
            target_date = self._extract_date_from_message(command)
        else:
            target_date= self._format_date(target_date)

        # 查询天气信息
        try:
            if target_date: # 检查是否指定了日期
                weather_info = self._get_weather_by_date_sync(city, target_date)
            else: # 检查是否是多天预报查询
                weather_info = self._get_weather_forecast_sync(city)
                
        except Exception as e:
            weather_info = f"抱歉，天气查询服务暂时不可用。错误信息：{str(e)}"

        return {
            "response": IMMessage(
                sender=ChatSender.get_bot_sender(),
                message_elements=[TextMessage(weather_info)]
            )
        }

    def _get_current_weather_sync(self, city: str) -> str:
        """同步获取当前天气信息"""
        try:
            asyncio.get_running_loop()
            # 如果已经在事件循环中，使用线程池执行
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, self._get_current_weather_async(city))
                return future.result()
        except RuntimeError:
            # 如果没有运行的事件循环，直接使用 asyncio.run
            return asyncio.run(self._get_current_weather_async(city))

    def _get_weather_by_date_sync(self, city: str, date: str) -> str:
        """同步获取指定日期的天气信息"""
        try:
            asyncio.get_running_loop()
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, self._get_weather_by_date_async(city, date))
                return future.result()
        except RuntimeError:
            return asyncio.run(self._get_weather_by_date_async(city, date))

    def _get_weather_forecast_sync(self, city: str) -> str:
        """同步获取天气预报信息"""
        try:
            asyncio.get_running_loop()
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, self._get_weather_forecast_async(city))
                return future.result()
        except RuntimeError:
            return asyncio.run(self._get_weather_forecast_async(city))

    async def _get_current_weather_async(self, city: str) -> str:
        """异步获取当前天气信息"""
        async with WeatherService(self.container) as weather_service:
            weather_result = await weather_service.get_current_weather(city)
            return WeatherFormatter.format_current_weather(weather_result)

    async def _get_weather_by_date_async(self, city: str, date: str) -> str:
        """异步获取指定日期的天气信息"""
        async with WeatherService(self.container) as weather_service:
            weather_result = await weather_service.get_weather_by_date(city, date)
            return WeatherFormatter.format_weather_by_date(weather_result, date)

    async def _get_weather_forecast_async(self, city: str) -> str:
        """异步获取天气预报信息"""
        async with WeatherService(self.container) as weather_service:
            weather_result = await weather_service.get_weather_forecast(city, 3)
            return WeatherFormatter.format_weather_forecast(weather_result)

    def _extract_city_name(self, message: str) -> str:
        """从消息中提取城市名称"""
        # 匹配常见的天气查询模式
        patterns = [
            r'查询(.+?)天气',     # 查询北京天气
            r'(.+?)的天气',       # 北京的天气
            r'(.+?)天气怎么样',    # 北京天气怎么样
            r'(.+?)天气如何',      # 北京天气如何
            r'(.+?)的天气情况',    # 北京的天气情况
            r'(.+?)天气',          # 北京天气
            r'(.+?)气温',          # 北京气温
            r'(.+?)温度',          # 北京温度
            r'(.+?)(有|会)',       # 北京会下雨
        ]
        
        for pattern in patterns:
            match = re.search(pattern, message)
            if match:
                city = match.group(1).strip()
                
                # 清理城市名称，移除常见的前缀词
                prefixes_to_remove = ['查询', '看看', '问问', '了解', '知道']
                for prefix in prefixes_to_remove:
                    if city.startswith(prefix):
                        city = city[len(prefix):].strip()
                
                # 移除常见的后缀词
                suffixes_to_remove = ['的', '市', '省', '区', '县']
                for suffix in suffixes_to_remove:
                    if city.endswith(suffix):
                        city = city[:-len(suffix)].strip()
                
                # 过滤掉一些无效的词汇
                invalid_words = ['今天', '明天', '现在', '当前', '实时', '这里', '那里', '哪里', '怎么样', '如何']
                if city and city not in invalid_words and len(city) > 0:
                    return city
        
        return ""
    
    def _format_date(self, date: str) -> str:
        return self._extract_date_from_message(date)

    def _extract_date_from_message(self, message: str) -> str:
        """从消息中提取日期"""
        # 使用 WeatherService 的日期解析功能
        weather_service = WeatherService(self.container)
        return weather_service.parse_date_from_query(message)

    def _is_forecast_query(self, message: str) -> bool:
        """判断是否为天气预报查询"""
        forecast_keywords = ['预报', '几天', '未来', '天气预报', '多天']
        return any(keyword in message for keyword in forecast_keywords)
