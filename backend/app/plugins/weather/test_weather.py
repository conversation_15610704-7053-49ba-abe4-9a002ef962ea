#!/usr/bin/env python3
"""
测试天气查询插件 - 包含日期支持
"""
import asyncio
from kirara_ai.ioc.container import DependencyContainer
from .weather_service import WeatherService, WeatherFormatter
from .config import WeatherConfig


async def test_weather():
    """测试天气查询功能"""
    print("🌤️ 测试天气查询插件（支持日期）...")

    # 创建测试容器
    container = DependencyContainer()
    weather_config = WeatherConfig()
    container.register(WeatherConfig, weather_config)

    async with WeatherService(container) as weather_service:
        # 测试当前天气
        print("\n📍 查询北京当前天气...")
        result = await weather_service.get_current_weather("北京")
        weather_info = WeatherFormatter.format_current_weather(result)
        print(weather_info)

        # 测试指定日期天气
        print("\n📍 查询北京明天天气...")
        from datetime import datetime, timedelta
        tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
        result = await weather_service.get_weather_by_date("北京", tomorrow)
        weather_info = WeatherFormatter.format_weather_by_date(result, tomorrow)
        print(weather_info)

        # 测试多天预报
        print("\n📍 查询北京3天天气预报...")
        result = await weather_service.get_weather_forecast("北京", 3)
        weather_info = WeatherFormatter.format_weather_forecast(result)
        print(weather_info)

        # 测试日期解析
        print("\n🔍 测试日期解析功能...")
        test_queries = [
            "北京今天天气",
            "上海明天天气怎么样",
            "广州后天天气",
            "深圳7月16日天气",
            "杭州天气预报",
            "成都未来几天天气"
        ]

        for query in test_queries:
            date = weather_service.parse_date_from_query(query)
            print(f"  '{query}' -> 解析日期: {date or '无'}")


if __name__ == "__main__":
    asyncio.run(test_weather())
