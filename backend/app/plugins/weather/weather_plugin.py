"""
天气查询插件
支持查询指定城市的天气信息
"""
from kirara_ai.ioc.container import DependencyContainer
from kirara_ai.plugin_manager.plugin import Plugin
from kirara_ai.logger import get_logger
from kirara_ai.workflow.core.block.registry import BlockRegistry
from kirara_ai.workflow.core.workflow.registry import WorkflowRegistry
from .config import get_weather_config
from .weather_block import WeatherQuery
from .weather_factory import WeatherWorkflowFactory


class WeatherPlugin(Plugin):
    """天气查询插件"""

    def __init__(self, container:DependencyContainer):
        self.logger = get_logger("WeatherPlugin")
        self.logger.info("WeatherPlugin 实例化")
        self.container = container
        self.weather_config = None

    def on_load(self):
        """插件加载时调用"""
        self.logger.info("天气查询插件加载完成")

    def on_start(self):
        """插件启动时调用"""
        self.logger.info("天气查询插件启动完成")

        # 获取配置
        self.weather_config = get_weather_config(self.container)

        # 注册天气查询 Block 和工作流
        try:
            # 注册 Block
            block_registry = self.container.resolve(BlockRegistry)
            block_registry.register("weather_query", "tools", WeatherQuery, "工具: 天气查询")

            self.logger.info("天气查询 Block 注册成功")

            # 注册工作流
            workflow_registry = self.container.resolve(WorkflowRegistry)
            workflow_registry.register("tools", "weather", WeatherWorkflowFactory.create_weather_query_workflow())
            self.logger.info("天气查询工作流注册成功")

        except Exception as e:
            self.logger.error(f"注册天气查询组件失败: {e}")

        # 显示配置信息
        if self.weather_config.is_api_configured:
            self.logger.info("高德地图API已配置，将使用真实天气数据")
        else:
            self.logger.info("高德地图API未配置，将使用模拟天气数据")

    def on_stop(self):
        """插件停止时调用"""
        self.logger.info("天气查询插件停止")
        