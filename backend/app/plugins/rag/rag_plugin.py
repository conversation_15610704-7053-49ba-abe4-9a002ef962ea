"""
RAG 知识库插件
提供知识库列表查询和基于知识库的对话功能
"""
from kirara_ai.ioc.container import DependencyContainer
from kirara_ai.plugin_manager.plugin import Plugin
from kirara_ai.logger import get_logger
from kirara_ai.workflow.core.block.registry import BlockRegistry
from kirara_ai.workflow.core.workflow.registry import WorkflowRegistry
from kirara_ai.workflow.core.dispatch.rules.base import DispatchRule
from app.plugins.rag.config import get_rag_config
from .rag_block import RAGListRepositories, RAGChat, FetchReply, SwitchRagRepo
from .rag_factory import RAGWorkflowFactory
from .rag_session_dispatch_rule import RagSessionDispatchRule


class RAGPlugin(Plugin):
    """RAG 知识库插件"""

    def __init__(self, container: DependencyContainer):
        self.logger = get_logger("RAGPlugin")
        self.logger.info("RAGPlugin 实例化")
        self.container = container
        self.rag_config = None

    def on_load(self):
        """插件加载时调用"""
        self.logger.info("RAG 知识库插件加载完成")

    def on_start(self):
        """插件启动时调用"""
        self.logger.info("RAG 知识库插件启动完成")

        # 获取配置
        self.rag_config = get_rag_config(self.container)

        # 注册 RAG Block 和工作流
        try:
            # 注册 Block
            block_registry = self.container.resolve(BlockRegistry)
            block_registry.register("rag_list_repositories", "rag", RAGListRepositories, "知识库列表查询")
            block_registry.register("rag_chat", "rag", RAGChat, "RAG对话")
            block_registry.register("fetch_reply", "im", FetchReply, "回复消息")
            block_registry.register("switch_rag_repo", "rag", SwitchRagRepo, "选择知识库")

            self.logger.info("RAG Block 注册成功")

            # 注册工作流
            workflow_registry = self.container.resolve(WorkflowRegistry)
            workflow_registry.register("rag", "list_repo", RAGWorkflowFactory.create_list_repositories_workflow())
            workflow_registry.register("rag", "switch_repo", RAGWorkflowFactory.create_switch_rag_repo_workflow())
            workflow_registry.register("rag", "chat", RAGWorkflowFactory.create_rag_chat_workflow())
            # 暂时用不到，用上边的list_repo代替
            # workflow_registry.register("rag", "help", RAGWorkflowFactory.create_rag_help_workflow())

            # 注册分发规则
            DispatchRule.register_rule_type(RagSessionDispatchRule)
            workflow_registry.register("im", "send_msg", RAGWorkflowFactory.create_im_send_msg_workflow())

            self.logger.info("RAG 工作流注册成功")

        except Exception as e:
            self.logger.error(f"注册RAG组件失败: {e}")

        # 显示配置信息
        if self.rag_config.is_configured:
            self.logger.info("RAG API已配置，将使用真实RAG服务")
        else:
            self.logger.warning("RAG API未配置，请检查配置")

    def on_stop(self):
        """插件停止时调用"""
        self.logger.info("RAG 知识库插件停止")
