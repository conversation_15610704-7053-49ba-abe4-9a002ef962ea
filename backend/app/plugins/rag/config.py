"""
RAG 插件配置
"""
from pydantic import BaseModel, Field

from kirara_ai.ioc.container import DependencyContainer

class RAGConfig(BaseModel):
    """RAG 插件配置"""
    
    # API 配置
    api_base_url: str = Field(
        default="https://dev-rag-nova.jx.ruyi.cn",
        description="RAG API 基础URL"
    )
    
    api_key: str = Field(
        default="pk-7MG5s5oSihnbVfs3AJjmrA:sk-Mi6mY84owF4Hn7HR6yFKS6",
        description="RAG API 密钥"
    )
    
    default_userkey: str = Field(
        default="wx_wanglingran-1040327",
        description="默认用户密钥"
    )

    default_username: str = Field(
        default="王凌冉",
        description="默认用户姓名"
    )
    
    default_model_type: str = Field(
        default="ccp",
        description="默认模型类型"
    )
    
    # 请求配置
    request_timeout: int = Field(
        default=30,
        description="请求超时时间（秒）"
    )
    
    max_retries: int = Field(
        default=3,
        description="最大重试次数"
    )
    
    # 调试配置
    debug: bool = Field(
        default=False,
        description="是否启用调试模式"
    )
    
    @property
    def is_configured(self) -> bool:
        """检查是否已配置"""
        return bool(self.api_key and self.api_base_url)


def get_rag_config(container:DependencyContainer)->RAGConfig:
    return container.resolve(RAGConfig)


help_text = """
1. 帮助
发送消息"帮助"或"知识库"，将展示知识库的使用说明和知识库列表
2. 查询知识库：
 操作步骤: 选择知识库 - 发送问题
 a. 选择知识库: 发送消息"选择知识库1018", 提示:✅ 已成功切到「员工手册知识库」，后续对话将基于该知识库进行
 b. 发送问题:  发送消息"考勤规则"，回复相关内容。最后一个问题10分钟内无操作，需重新选择知识库
3. 其它功能:
 a. 城市天气: 发送"北京天气"可查询北京实时天气
"""