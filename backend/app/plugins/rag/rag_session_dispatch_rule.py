"""
RAG会话分发规则
基于用户当前设置的知识库进行分发
"""
from typing import Optional
from pydantic import Field

from kirara_ai.im.message import IMMessage
from kirara_ai.workflow.core.dispatch.rules.base import DispatchRule, RuleConfig
from kirara_ai.workflow.core.workflow.registry import WorkflowRegistry
from kirara_ai.logger import get_logger

from .rag_user_repo import RagUserRepoManager


class RagSessionDispatchRuleConfig(RuleConfig):
    """RAG会话分发规则配置"""

    # 目标工作流ID
    target_workflow_id: str = Field(
        default="rag:chat",
        title="目标工作流ID",
        description="匹配成功后要执行的RAG工作流ID"
    )


class RagSessionDispatchRule(DispatchRule):
    """RAG会话分发规则"""
    
    config_class = RagSessionDispatchRuleConfig
    
    type_name = "rag_session_dispatch"
    rule_id = "rag_session_dispatch"
    priority = 6  # 中等优先级，低于LLM分发但高于普通规则
    
    def __init__(
        self,
        workflow_registry: WorkflowRegistry,
        target_workflow_id: str = "rag:chat"
    ):
        super().__init__(workflow_registry, target_workflow_id)
        self.target_workflow_id = target_workflow_id
        self.logger = get_logger("RagSessionDispatchRule")

        # 设置中等优先级
        self.priority = 6

        # 当前匹配的知识库ID
        self.current_repo_id: Optional[int] = None

    def match(self, message: IMMessage) -> bool:
        """匹配规则 - 核心逻辑：检查用户是否设置了知识库"""
        try:
            sender = message.sender
            # 获取用户信息
            if not sender or not sender.user_id:
                self.logger.debug("消息缺少发送者信息，跳过RAG会话分发")
                return False
            
            user_key=sender.user_id
            # 创建用户知识库管理器
            repo_manager = RagUserRepoManager(user_key, sender.display_name)
            
            # 获取用户当前设置的知识库ID
            repo_id = repo_manager.get_and_activate_repo_id()
            
            # 核心匹配逻辑：知识库ID不为空
            if repo_id is not None:
                self.current_repo_id = repo_id
                self.workflow_id = self.target_workflow_id

                self.logger.debug(f"RAG会话分发匹配成功: user_key={user_key}, repo_id={repo_id}")
                return True

            # 用户未设置知识库
            self.logger.debug(f"用户未设置知识库，跳过RAG会话分发: user_key={user_key}")
            return False
            
        except Exception as e:
            self.logger.error(f"RAG会话分发规则匹配失败: {e}")
            return False

    def get_workflow_params(self) -> dict:
        """获取工作流参数"""
        params = {}
        
        # 传递知识库ID参数
        if self.current_repo_id is not None:
            params['repo_id'] = self.current_repo_id
            
        return params

    def get_config(self) -> RagSessionDispatchRuleConfig:
        """获取规则配置"""
        return RagSessionDispatchRuleConfig(
            target_workflow_id=self.target_workflow_id
        )

    @classmethod
    def from_config(
        cls,
        config: RagSessionDispatchRuleConfig,
        workflow_registry: WorkflowRegistry,
        workflow_id: str
    ) -> "RagSessionDispatchRule":
        """从配置创建规则实例"""
        return cls(
            workflow_registry=workflow_registry,
            target_workflow_id=config.target_workflow_id or workflow_id
        )

    def reset(self):
        """重置规则状态"""
        super().reset()
        self.current_repo_id = None
