"""
RAG 工作流工厂
创建RAG相关的工作流
"""
from kirara_ai.workflow.core.workflow.builder import WorkflowBuilder
from kirara_ai.workflow.implementations.blocks.im.messages import GetIMMessage, SendIMMessage
from .rag_block import RAGListRepositories, SwitchRagRepo, RAGChat, FetchReply


class RAGWorkflowFactory:
    """RAG 工作流工厂"""

    @staticmethod
    def create_list_repositories_workflow() -> WorkflowBuilder:
        """知识库对话工作流"""
        workflow_builder:WorkflowBuilder =  (
            WorkflowBuilder("知识库-列表")
            .use(GetIMMessage)
            .chain(RAGListRepositories)
            .chain(SendIMMessage)
        )
        workflow_builder.description ="""
        查询可用的知识库列表。
        示例用法：
        - 查询知识库列表：知识库列表
        - 列出知识库：列出知识库
        - 显示知识库：显示知识库
        - 显示所有知识库：显示所有知识库
        """
        return workflow_builder
    
    @staticmethod
    def create_switch_rag_repo_workflow() -> WorkflowBuilder:
        """切换知识库工作流"""
        workflow_builder:WorkflowBuilder =  (
            WorkflowBuilder("选择知识库")
            .use(GetIMMessage)
            .chain(SwitchRagRepo)
            .chain(SendIMMessage)
        )
        workflow_builder.description ="""
        切换到指定的知识库
        示例用法：
        - 切换到知识库：切换到知识库123
        - 切换到知识库：切知识库123
        - 切换到知识库：切到知识库123
        """
        return workflow_builder
    

    @staticmethod
    def create_rag_chat_workflow() -> WorkflowBuilder:
        """知识库对话工作流"""
        workflow_builder:WorkflowBuilder =  (
            WorkflowBuilder("知识库-聊天")
            .use(GetIMMessage,name='get_im_message')
            .chain(RAGChat)
            .chain(SendIMMessage)
        )
        workflow_builder.description ="""
        基于选定知识库对话
        示例用法：
        - @知识库[1060] 产品评级靠谱吗？
        """
        return workflow_builder

    @staticmethod
    def create_rag_help_workflow() -> WorkflowBuilder:
        """知识库帮助工作流"""
        workflow_builder:WorkflowBuilder =  (
            WorkflowBuilder("帮助")
            .use(GetIMMessage)
            .chain(RAGChat)
            .chain(SendIMMessage)
        )
        workflow_builder.description ="""
        基于指定知识库的系统帮助
        """
        return workflow_builder
    
    @staticmethod
    def create_im_send_msg_workflow() -> WorkflowBuilder:
        """通用消息发送"""
        workflow_builder:WorkflowBuilder =  (
            WorkflowBuilder("通用消息发送")
            .use(GetIMMessage)
            .chain(FetchReply)
            .chain(SendIMMessage)
        )
        workflow_builder.description ="""
        基于指定知识库的系统帮助
        """
        return workflow_builder
