"""
RAG 服务
提供知识库列表查询和对话功能
"""
import aiohttp
import asyncio
import concurrent
import concurrent.futures

from typing import List, Dict, Optional
from kirara_ai.logger import get_logger
from kirara_ai.ioc.container import DependencyContainer
from kirara_ai.im.sender import Chat<PERSON>ender
from app.plugins.rag.config import get_rag_config
from app.plugins.rag.utils import wechat_user_id_to_rag_user_key




class RAGRepository:
    """知识库信息"""
    
    def __init__(self, repo_id: int, name: str, description: str = ""):
        self.repo_id = repo_id
        self.name = name
        self.description = description
    
    def __str__(self):
        return f"知识库 {self.repo_id}: {self.name}"


class RAGChatResponse:
    """RAG 对话响应"""
    
    def __init__(self, answer: str, sources: List[str] = None):
        self.answer = answer
        self.sources = sources or []
    
    def __str__(self):
        return self.answer


class RAGService:
    """RAG 服务类"""

    def __init__(self, container: DependencyContainer):
        self.logger = get_logger("RAGService")
        self.container = container
        self.rag_config = get_rag_config(container)
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.rag_config.request_timeout),
            headers={
                'Authorization': self.rag_config.api_key,
                'Content-Type': 'application/json'
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def list_repositories(self, userkey: str = None, username: str = None) -> List[RAGRepository]:
        """
        列出知识库
        
        Args:
            userkey: 用户密钥，默认使用配置中的值
            
        Returns:
            知识库列表
        """
        if not self.rag_config.is_configured:
            raise ValueError("RAG API 未配置")

        userkey = userkey or self.rag_config.default_userkey
        username = username or self.rag_config.default_username

        url = f"{self.rag_config.api_base_url}/api/third/repo_list"
        payload = {"userkey": userkey, 'username':username}
        
        try:
            self.logger.debug(f"请求知识库列表: {url}, payload: {payload}")
            
            async with self.session.post(url, json=payload) as response:
                if response.status != 200:
                    error_text = await response.text()
                    self.logger.error(f"获取知识库列表失败: {response.status}, {error_text}")
                    raise Exception(f"API请求失败: {response.status}")
                
                data = await response.json()
                self.logger.debug(f"知识库列表响应: {data}")
                if 'code' not in data or data.get('code')!=200:
                    error_text = await response.text()
                    self.logger.error(f"获取知识库列表失败: {response.status}, {error_text}")
                    raise Exception(f"API请求失败: {response.status}")

                data = data['data']
                repo_list = data.get('my',[]) + data.get('public',[])
                
                repo_dict:Dict[int, RAGRepository] = {}
                for repo in repo_list:
                    if not isinstance(repo, dict):
                        continue
                    
                    repo_id = repo.get('repo_id')
                    if not repo_id:
                        continue
                    name = repo.get('name')
                    description = repo.get('description', '')
                    repo_dict[repo_id] = RAGRepository(repo_id, name, description)
                
                repositories = list(repo_dict.values())
                self.logger.info(f"获取到 {len(repositories)} 个知识库")
                return repositories
                    
        except Exception as e:
            self.logger.error(f"获取知识库列表异常: {e}")
            raise
    
    async def chat_with_repository(
        self, 
        question: str, 
        repo_id: int, 
        userkey: str = None,
        username: str = None,
        model_type: str = None
    ) -> RAGChatResponse:
        """
        基于知识库对话
        
        Args:
            question: 问题
            repo_id: 知识库ID
            userkey: 用户密钥，默认使用配置中的值
            model_type: 模型类型，默认使用配置中的值
            
        Returns:
            对话响应
        """
        if not self.rag_config.is_configured:
            raise ValueError("RAG API 未配置")

        userkey = userkey or self.rag_config.default_userkey
        username = username or self.rag_config.default_username
        model_type = model_type or self.rag_config.default_model_type

        url = f"{self.rag_config.api_base_url}/api/third/query_chat"
        
        # repo_id有可能是repo_name, 如果是repo_name(非数字)，则需要转换为repo_id
        if not isinstance(repo_id, int):
            try:
                repo_id = int(repo_id)
            except ValueError:
                # 尝试从知识库列表中找到对应的repo_id
                repositories = await self.list_repositories(userkey, username)
                for repo in repositories:
                    if repo.name == repo_id:
                        repo_id = repo.repo_id
                        break
                else:
                    raise ValueError(f"知识库 {repo_id} 不存在")
        payload = {
            "userkey": userkey,
            "username": username,
            "repo_id": repo_id,
            "question": question,
            "model_type": model_type
        }
        
        try:
            self.logger.debug(f"RAG对话请求: {url}, payload: {payload}")
            
            async with self.session.post(url, json=payload) as response:
                
                if response.status!= 200:
                    error_text = await response.text()
                    self.logger.error(f"RAG对话失败(102): {response.status}, {error_text}")
                    raise Exception(f"API请求失败: {response.status}")
                
                data = await response.json()
                self.logger.debug(f"RAG对话响应: {data}")
                data = data['data']
                if not data:
                    answer = str(data)
                    sources = []
                else:
                    answer = data.get('content','')
                    sources = data.get('src_files',[])
                
                if not answer:
                    answer = "抱歉，没有找到相关信息。"
                
                return RAGChatResponse(answer, sources)
                    
                    
        except Exception as e:
            self.logger.error(f"RAG对话异常: {e}")
            raise

def get_repo_by_id(repo_id: int, sender:ChatSender, container:DependencyContainer)->Optional[RAGRepository]:
    """根据知识库ID获取知识库信息"""
    repositories = get_repositories(sender, container)
    for repo in repositories:
        if repo.repo_id == repo_id:
            return repo
    return None

def get_repositories( sender:ChatSender, container:DependencyContainer)->List[RAGRepository]:
        """同步获取知识库列表"""
        try:
            asyncio.get_running_loop()
            # 如果已经在事件循环中，使用线程池执行
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, _get_repositories_async(sender, container))
                return future.result()
        except RuntimeError:
            # 如果没有运行的事件循环，直接使用 asyncio.run
            return asyncio.run(_get_repositories_async(sender, container))

async def _get_repositories_async(sender:ChatSender, container:DependencyContainer):
    """异步获取知识库列表"""
    async with RAGService(container) as rag_service:
        userkey = wechat_user_id_to_rag_user_key(sender.user_id)
        return await rag_service.list_repositories(userkey=userkey, username=sender.display_name)
        

class RAGFormatter:
    """RAG 响应格式化器"""
    
    @staticmethod
    def format_repository_list(repositories: List[RAGRepository]) -> str:
        """格式化知识库列表"""
        if not repositories:
            return "暂无可用的知识库。"
        
        lines = ["📚 可用知识库列表：\n"]
        for i, repo in enumerate(repositories, 1):
            lines.append(f"- {repo.name} (ID: {repo.repo_id})")
            if repo.description:
                lines.append(f"   描述: {repo.description}")
        
        lines.append(f"\n共 {len(repositories)} 个知识库")
        # lines.append(f"\n输入\"切知识库1018\" 切换到ID=1018的知识库。根据需要调整实际知识库ID即可。")
        return "\n".join(lines)
    
    @staticmethod
    def format_chat_response(response: RAGChatResponse, repo_name: str = None) -> str:
        """格式化对话响应"""
        lines = []
        
        if repo_name:
            lines.append(f"🤖 基于知识库「{repo_name}」的回答：\n")
        
        lines.append(response.answer)
        
        if response.sources:
            lines.append("\n📖 参考来源：")
            for i, source in enumerate(response.sources, 1):
                lines.append(f"{i}. {source}")
        
        return "\n".join(lines)
