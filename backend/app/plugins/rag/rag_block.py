"""
RAG 功能块
提供知识库列表查询和对话功能的工作流块
"""
import re
import asyncio
import traceback

import concurrent.futures
from typing import Any, Dict
from typing import Any, Dict


from kirara_ai.workflow.core.block import Block, Input, Output
from kirara_ai.ioc.container import DependencyContainer
from kirara_ai.im.message import IMMessage, TextMessage
from app.plugins.nova_wecom.message import MarkdownMessage
from kirara_ai.im.sender import ChatSender
from kirara_ai.workflow.core.block import Block
from kirara_ai.workflow.core.block.input_output import Input, Output


from app.plugins.llm_dispatch.types import LLMAnalysisParams
from app.config import settings
from app.utils.waiting_messages import get_waiting_message
from app.plugins.rag.rag_user_repo import RagUserRepoManager
from app.plugins.rag.rag_service import get_repositories, get_repo_by_id
from app.plugins.rag.utils import wechat_user_id_to_rag_user_key
from app.plugins.rag import config


from .rag_service import RAGService, RAGFormatter


class RAGListRepositories(Block):
    """列出知识库 block"""

    name = "rag_list_repositories"
    inputs = {
        "message": Input("message", "输入消息", IMMessage, "输入消息包含知识库列表查询命令")
    }
    outputs = {
        "response": Output(
            "response", "响应消息", IMMessage, "响应消息包含知识库列表"
        )
    }

    def execute(self, message: IMMessage) -> Dict[str, Any]:
        """执行知识库列表查询"""
        try:
            # 获取知识库列表
            repositories = get_repositories(message.sender, self.container)
            
            
            # 格式化响应
            repo_list_text = RAGFormatter.format_repository_list(repositories)
            
            return {
                "response": IMMessage(
                    sender=ChatSender.get_bot_sender(),
                    message_elements=[
                        TextMessage(config.help_text), # 帮助
                        MarkdownMessage(repo_list_text) # "我的知识库"
                    ]
                )
            }
        except Exception as e:
            error_message = f"获取知识库列表失败：{str(e)}"
            return {
                "response": IMMessage(
                    sender=ChatSender.get_bot_sender(),
                    message_elements=[TextMessage(error_message)]
                )
            }


class RAGChat(Block):
    """RAG 对话 block"""

    name = "rag_chat"
    inputs = {
        "message": Input("message", "输入消息", IMMessage, "输入消息包含RAG对话命令")
    }
    outputs = {
        "response": Output(
            "response", "响应消息", IMMessage, "响应消息包含RAG对话结果"
        )
    }
    

    def __init__(self):
        self.repo_id= settings.default_rag_repo_id

    def execute(self, message: IMMessage) -> Dict[str, Any]:
        """执行RAG对话"""
        command = message.content
        
        container:DependencyContainer = self.container
        repo_id = None
        if container:
            try:
                params:LLMAnalysisParams = container.resolve(LLMAnalysisParams) or {}
                repo_id = params.get('repo_id', '')
                question = params.get('question', '')
            except Exception:
                pass
        if not repo_id:
            current_repo_id = RagUserRepoManager(message.sender.user_id, message.sender.display_name).get_and_activate_repo_id()
            if current_repo_id:
                repo_id = current_repo_id
                question = message.content

        # 解析命令，提取知识库ID和问题
        if not repo_id:
            repo_id, question = self._parse_rag_command(command)
        if not repo_id and self.repo_id:
            repo_id=self.repo_id
            question = command
        
        if not repo_id:
            return {
                "response": IMMessage(
                    sender=ChatSender.get_bot_sender(),
                    message_elements=[TextMessage(
                        "请指定知识库ID和问题，格式：@知识库[ID] 问题\n"
                        "例如：@知识库[1060] 产品评级靠谱吗？\n"
                        "或者：知识库1060 产品评级靠谱吗？"
                    )]
                )
            }
        
        if not question:
            return {
                "response": IMMessage(
                    sender=ChatSender.get_bot_sender(),
                    message_elements=[TextMessage("请提供要询问的问题。")]
                )
            }

        try:
            # 执行RAG对话
            userkey=wechat_user_id_to_rag_user_key(message.sender.user_id)
            username=message.sender.display_name
            response = self._chat_with_repository_sync(
                userkey=userkey, username=username, 
                question=question, repo_id=repo_id
            )
            
            # 格式化响应
            repo = get_repo_by_id(repo_id, message.sender, self.container)
            repo_name = repo.name if repo else f"知识库{repo_id}"
            response_text = RAGFormatter.format_chat_response(response, repo_name)
            
            return {
                "response": IMMessage(
                    sender=ChatSender.get_bot_sender(),
                    message_elements=[MarkdownMessage(response_text)]
                )
            }
        except Exception as e:
            error_message = f"RAG对话失败(101)：{str(e)}"
            #记录日志，打印调用栈
            print(traceback.format_exc())
            return {
                "response": IMMessage(
                    sender=ChatSender.get_bot_sender(),
                    message_elements=[TextMessage(error_message)]
                )
            }

    def _parse_rag_command(self, message: str) -> tuple[int, str]:
        """
        解析RAG命令，提取知识库ID和问题
        
        支持的格式：
        - @知识库[1060] 产品评级靠谱吗？
        - 知识库1060 产品评级靠谱吗？
        - rag 1060 产品评级靠谱吗？
        """
        patterns = [
            r'@知识库\[(\d+)\]\s*(.+)',      # @知识库[1060] 问题
            r'知识库(\d+)\s+(.+)',           # 知识库1060 问题
            r'rag\s+(\d+)\s+(.+)',          # rag 1060 问题
            r'RAG\s+(\d+)\s+(.+)',          # RAG 1060 问题
        ]
        
        for pattern in patterns:
            match = re.search(pattern, message)
            if match:
                repo_id = int(match.group(1))
                question = match.group(2).strip()
                return repo_id, question
        
        return None, None

    def _chat_with_repository_sync(self, userkey:str, username:str, question: str, repo_id: int):
        """同步执行RAG对话"""
        try:
            asyncio.get_running_loop()
            # 如果已经在事件循环中，使用线程池执行
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, self._chat_with_repository_async(userkey, username, question, repo_id))
                return future.result()
        except RuntimeError:
            # 如果没有运行的事件循环，直接使用 asyncio.run
            return asyncio.run(self._chat_with_repository_async(userkey, username, question, repo_id))

    async def _chat_with_repository_async(self, userkey:str, username:str, question: str, repo_id: int):
        """异步执行RAG对话"""
        async with RAGService(self.container) as rag_service:
            return await rag_service.chat_with_repository(question, repo_id,userkey=userkey,username=username)


class FetchReply(Block):
    """回复消息 block"""

    name = "fetch_reply"
    inputs = {
        "message": Input("message", "输入消息", IMMessage, "触发回复的输入消息")
    }
    outputs = {
        "response": Output(
            "response", "响应消息", IMMessage, "返回回复消息"
        )
    }

    def execute(self, message: IMMessage) -> Dict[str, Any]:
        """返回回复消息"""
        # 从容器中获取自定义内容，如果没有则使用默认的友好提示
        params = self.container.resolve(LLMAnalysisParams) or {}
        content = params.get('content') or get_waiting_message()

        # 这里可以根据输入消息定制回复内容，目前使用默认内容
        _ = message  # 标记参数已使用，避免警告

        return {
            "response": IMMessage(
                sender=ChatSender.get_bot_sender(),
                message_elements=[TextMessage(content)]
            )
        }


class SwitchRagRepo(Block):
    """选择知识库 block"""

    name = "switch_knowledge_base"
    inputs = {
        "message": Input("message", "输入消息", IMMessage, "用户输入的选择知识库消息")
    }
    outputs = {
        "response": Output(
            "response", "响应消息", IMMessage, "选择知识库的响应消息"
        )
    }

    def execute(self, message: IMMessage) -> Dict[str, Any]:
        """执行切换知识库操作"""
        # 获取消息文本
        message_text = ""
        for element in message.message_elements:
            if isinstance(element, TextMessage):
                message_text += element.text

        # 正则匹配切换知识库命令
        patterns = [
            r'^\s*[#/\\]?切换?到?知识库\s*(\d+)\s*$',
            r'^\s*[#/\\]?选择?知识库\s*(\d+)\s*$',
        ]
        repo_id = None
        for pattern in patterns:
            match = re.match(pattern, message_text.strip())
            if match:
                repo_id = int(match.group(1))
                break

        if not repo_id:
            return {
                "response": IMMessage(
                    sender=ChatSender.get_bot_sender(),
                    message_elements=[TextMessage("❌ 选择知识库失败。请使用正确的格式：选择知识库1018")]
                ),
                "repo_id": 0,
                "matched": False
            }

        # 提取知识库ID
        sender = message.sender
        repo_manager = RagUserRepoManager(sender.user_id, sender.display_name)

        # 保存到Redis
        if repo_manager.set_repo_id(repo_id):
            repo = get_repo_by_id(repo_id=repo_id, sender=sender, container=self.container)
            repo_name = repo.name if repo else  f"知识库 {repo_id}"
            response_text = f"✅ 已成功切到「{repo_name}」，后续对话将基于该知识库进行。"
        else:
            response_text = f"❌ 选择知识库失败。 知识库(ID={repo_id})不存在"

        return {
            "response": IMMessage(
                sender=ChatSender.get_bot_sender(),
                message_elements=[TextMessage(response_text)]
            )
        }
