"""
知识库工具函数
提供知识库相关的工具方法
"""

from typing import Optional
from app.utils.kvdb_utils import get_kvdb_redis_client


class RagUserRepoManager:
    """知识库管理器"""

    _RAG_USER_REPO_KEY_PREFIX = "rag_user_current_repo_id:"
    
    def __init__(self, user_key:str, username:str):
        self.redis_client = get_kvdb_redis_client()
        self._user_key = user_key
        self._username = username

    def _get_key(self)->str:
        return self._RAG_USER_REPO_KEY_PREFIX+ self._user_key
        
    
    def set_repo_id(self, repo_id: int, expire_seconds: int = 600) -> bool:
        """设置用户的知识库ID"""
        try:
            self.redis_client.set(self._get_key(), repo_id, ex=expire_seconds)
            return True
        except Exception as e:
            print(f"Failed to set user rag repo_id: {e}")
            return False
        
    def get_and_activate_repo_id(self):
        """获取repo_id， 并延长session有效期"""
        repo_id = self._get_repo_id()
        if repo_id:#续命
            self.set_repo_id(repo_id=repo_id)
        return repo_id
    
    def _get_repo_id(self) -> Optional[int]:
        """获取用户的知识库ID"""
        try:
            repo_id = self.redis_client.get(self._get_key())
            if repo_id is not None:
                return int(repo_id)
            return None
        except Exception as e:
            print(f"Failed to get user rag repo_id: {e}")
            return None
    
    def remove_repo_id(self) -> bool:
        """移除用户的知识库设置"""
        try:
            self.redis_client.delete(self._get_key())
            return True
        except Exception as e:
            print(f"Failed to remove user rag repo_id: {e}")
            return False
    