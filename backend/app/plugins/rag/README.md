# RAG 知识库插件

RAG（Retrieval-Augmented Generation）知识库插件为 Kirara AI 提供了基于知识库的问答功能。

## 功能特性

### 1. 知识库列表查询
- 获取用户可访问的所有知识库
- 显示知识库ID、名称和描述信息
- 支持格式化输出

### 2. 基于知识库的对话
- 基于指定知识库回答用户问题
- 支持多种命令格式
- 提供参考来源信息

## 配置说明

### 环境变量配置

```bash
# RAG API 配置
RAG_API_BASE_URL=https://dev-rag-nova.jx.ruyi.cn
RAG_API_KEY=pk-7MG5s5oSihnbVfs3AJjmrA:sk-Mi6mY84owF4Hn7HR6yFKS6
RAG_DEFAULT_USERKEY=wx_wanglingran-1040327
RAG_DEFAULT_MODEL_TYPE=ccp

# 请求配置
RAG_REQUEST_TIMEOUT=30
RAG_MAX_RETRIES=3

# 调试配置
RAG_DEBUG=false
```

### 配置文件

插件配置位于 `config.py` 文件中，包含以下配置项：

- `api_base_url`: RAG API 基础URL
- `api_key`: API 认证密钥
- `default_userkey`: 默认用户密钥
- `default_model_type`: 默认模型类型
- `request_timeout`: 请求超时时间
- `max_retries`: 最大重试次数
- `debug`: 调试模式开关

## 使用方法

### 1. 查询知识库列表

发送以下任意消息触发知识库列表查询：
- "知识库列表"
- "列出知识库"
- "显示知识库"

### 2. 基于知识库对话

支持以下命令格式：

```
@知识库[1060] 产品评级靠谱吗？
知识库1060 产品评级靠谱吗？
rag 1060 产品评级靠谱吗？
RAG 1060 产品评级靠谱吗？
```

其中 `1060` 是知识库ID，`产品评级靠谱吗？` 是要询问的问题。

## API 接口

### 1. 知识库列表接口

```bash
curl --location 'https://dev-rag-nova.jx.ruyi.cn/api/third/repo_list?userkey=wx_wanglingran-1040327' \
--header 'Authorization: pk-7MG5s5oSihnbVfs3AJjmrA:sk-Mi6mY84owF4Hn7HR6yFKS6'
```

### 2. 对话接口

```bash
curl --location 'https://dev-rag-nova.jx.ruyi.cn/api/third/query_chat' \
--header 'Content-Type: application/json' \
--header 'Authorization: pk-7MG5s5oSihnbVfs3AJjmrA:sk-Mi6mY84owF4Hn7HR6yFKS6' \
--data '{
    "userkey": "wx_wanglingran-1040327",
    "repo_id": 1060,
    "question": "产品评级靠谱吗?",
    "model_type": "ccp"
}'
```

## 工作流

插件注册了两个工作流：

1. **知识库列表查询工作流** (`tools:rag_list`)
   - GetIMMessage → RAGListRepositories → SendIMMessage

2. **RAG对话工作流** (`tools:rag_chat`)
   - GetIMMessage → RAGChat → SendIMMessage

## 测试

运行测试：

```bash
cd backend/app/plugins/rag
python test_rag.py
```

测试包括：
- RAG服务功能测试
- 格式化器测试
- 块功能测试
- 配置测试

## 错误处理

插件包含完善的错误处理机制：

- API 请求失败时返回友好的错误信息
- 网络超时自动重试
- 无效命令格式提示正确用法
- 详细的日志记录便于调试

## 依赖

- `aiohttp`: 异步HTTP客户端
- `kirara_ai`: Kirara AI 框架
- `pydantic`: 数据验证和配置管理

## 注意事项

1. 确保网络连接正常，能够访问 RAG API
2. 检查 API 密钥和用户密钥的有效性
3. 知识库ID必须是有效的数字
4. 建议在生产环境中使用环境变量配置敏感信息
