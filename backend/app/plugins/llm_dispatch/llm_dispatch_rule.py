"""
LLM智能分发规则
扩展kirara-ai的分发规则系统，使用大模型分析消息内容
"""
from typing import Optional
from pydantic import Field

from kirara_ai.im.message import IMMessage
from kirara_ai.ioc.container import DependencyContainer
from kirara_ai.workflow.core.dispatch.rules.base import DispatchRule, RuleConfig
from kirara_ai.workflow.core.workflow.registry import WorkflowRegistry
from kirara_ai.logger import get_logger

from .types import LLMAnalysisParams
from .llm_analysis_service import LLMAnalysisService
from .config import get_llm_dispatch_config


class LLMDispatchRuleConfig(RuleConfig):
    """LLM智能分发规则配置"""
    
    # 置信度阈值
    confidence_threshold: float = Field(
        default=0.7,
        ge=0.0,
        le=1.0,
        title="置信度阈值",
        description="LLM分析的置信度阈值，范围0-1"
    )
    
    # 是否启用缓存
    enable_cache: bool = Field(
        default=True,
        title="启用缓存",
        description="是否启用分析结果缓存"
    )
    
    # 分析超时时间（秒）
    analysis_timeout: int = Field(
        default=10,
        ge=1,
        le=60,
        title="分析超时时间",
        description="LLM分析的超时时间（秒）"
    )
    
    # 自定义提示词模板
    custom_prompt_template: Optional[str] = Field(
        default=None,
        title="自定义提示词模板",
        description="自定义的LLM分析提示词模板，留空使用默认模板"
    )


class LLMDispatchRule(DispatchRule):
    """LLM智能分发规则"""
    
    config_class = LLMDispatchRuleConfig
    
    type_name = "llm_dispatch"
    rule_id = "llm_dispatch"
    priority = 8
    analysis_service = None

    def __init__(
        self,
        workflow_registry: WorkflowRegistry,
        confidence_threshold: float = 0.7,
        enable_cache: bool = True,
        analysis_timeout: int = 10,
        custom_prompt_template: Optional[str] = None
    ):
        super().__init__(workflow_registry, None)
        self.confidence_threshold = confidence_threshold
        self.enable_cache = enable_cache
        self.analysis_timeout = analysis_timeout
        self.custom_prompt_template = custom_prompt_template
        self.logger = get_logger("LLMDispatchRule")

        # 设置较高的优先级，因为LLM分析比较智能
        self.priority = 8

        # 工作流参数（匹配后设置）
        self.workflow_params: LLMAnalysisParams = LLMAnalysisParams()
        
    
    def _get_analysis_service(self) -> Optional[LLMAnalysisService]:
        """获取LLM分析服务（延迟初始化）"""
        if self.analysis_service is None:
            try:
                # 注意：这里需要在插件注册时传入必要的依赖
                # 实际使用时，LLMManager 应该从容器中获取
                self.logger.warning("LLM分析服务需要在插件初始化时设置")
                return None
            except Exception as e:
                self.logger.error(f"初始化LLM分析服务失败: {e}")

        return self.analysis_service

    @classmethod
    def set_analysis_service(cls, analysis_service: LLMAnalysisService):
        """设置LLM分析服务（由插件在初始化时调用）"""
        cls.analysis_service = analysis_service

    def match(self, message: IMMessage) -> bool:
        """匹配规则"""
        # 只处理文本消息
        if not message.content:
            return False

        try:
            # 调用LLM分析服务
            result = self.analysis_service.analyze_message(message=message.content)
            if result and result.workflow_id:
                self.workflow_id = result.workflow_id
                self.workflow_params = result.workflow_params

                self.logger.debug(f"LLM分发匹配成功: workflow_id={result.workflow_id}, "
                                    f"confidence={result.confidence}, params={result.workflow_params}")

                return True
            else:
                return False
        except Exception as e:
            self.logger.error(f"LLM分发规则匹配失败: {e}")
            return False

    def get_workflow_params(self) -> LLMAnalysisParams:
        """获取工作流参数"""
        # 创建一个新的实例并复制数据
        new_params = LLMAnalysisParams()
        new_params.data = self.workflow_params.data.copy()
        return new_params

    def get_config(self) -> LLMDispatchRuleConfig:
        """获取规则配置"""
        return LLMDispatchRuleConfig(
            confidence_threshold=self.confidence_threshold,
            enable_cache=self.enable_cache,
            analysis_timeout=self.analysis_timeout,
            custom_prompt_template=self.custom_prompt_template
        )
    
    @classmethod
    def from_config(
        cls,
        config: LLMDispatchRuleConfig,
        workflow_registry: WorkflowRegistry,
        workflow_id: str
    ) -> "LLMDispatchRule":
        """从配置创建规则实例"""
        return cls(
            workflow_registry=workflow_registry,
            confidence_threshold=config.confidence_threshold,
            enable_cache=config.enable_cache,
            analysis_timeout=config.analysis_timeout,
            custom_prompt_template=config.custom_prompt_template
        )
