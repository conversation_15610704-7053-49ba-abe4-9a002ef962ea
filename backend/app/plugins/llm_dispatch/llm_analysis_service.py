"""
LLM分析服务
使用大模型分析消息内容并推荐工作流
"""
import time
import hashlib
import json
from typing import Optional, Dict, List

from kirara_ai.logger import get_logger
from kirara_ai.llm.llm_manager import LLMManager
from kirara_ai.workflow.core.workflow.registry import WorkflowRegistry
from kirara_ai.workflow.core.workflow.builder import WorkflowBuilder
from kirara_ai.llm.llm_registry import LLMAbility
from kirara_ai.llm.format.request import LLMChatRequest
from kirara_ai.llm.format.response import LLMChatResponse
from kirara_ai.llm.format.message import LLMChatMessage, LLMChatTextContent
from kirara_ai.ioc.container import DependencyContainer

from .config import get_llm_dispatch_config, LLMDispatchConfig

from .types import LLMAnalysisResult, LLMAnalysisParams



class LLMAnalysisCache:
    """LLM分析结果缓存"""

    def __init__(self, container: DependencyContainer):
        self._cache: Dict[str, Dict] = {}
        self.logger = get_logger("LLMAnalysisCache")
        self.llm_dispatch_config: LLMDispatchConfig = get_llm_dispatch_config(container)

    def _get_cache_key(self, message: str, workflows: List[str]) -> str:
        """生成缓存键"""
        content = f"{message}:{':'.join(sorted(workflows))}"
        return hashlib.md5(content.encode()).hexdigest()

    def get(self, message: str, workflows: List[str]) -> Optional[LLMAnalysisResult]:
        """获取缓存的分析结果"""
        llm_dispatch_config = self.llm_dispatch_config
        if not llm_dispatch_config.cache_enabled:
            return None

        cache_key = self._get_cache_key(message, workflows)
        if cache_key in self._cache:
            data = self._cache[cache_key]
            if time.time() - data['timestamp'] < llm_dispatch_config.cache_duration:
                self.logger.debug(f"缓存命中: {cache_key}")
                return LLMAnalysisResult(
                    workflow_id=data['workflow_id'],
                    confidence=data.get('confidence', 1.0),
                    workflow_params=data.get('workflow_params', {})
                )
            else:
                # 缓存过期，删除
                del self._cache[cache_key]
                self.logger.debug(f"缓存过期: {cache_key}")

        return None

    def set(self, message: str, workflows: List[str], result: LLMAnalysisResult) -> None:
        """设置缓存"""
        llm_dispatch_config = self.llm_dispatch_config
        if llm_dispatch_config.cache_enabled:
            cache_key = self._get_cache_key(message, workflows)
            self._cache[cache_key] = {
                'workflow_id': result.workflow_id,
                'confidence': result.confidence,
                'workflow_params': result.workflow_params,
                'timestamp': time.time()
            }
            self.logger.debug(f"缓存设置: {cache_key} -> {result.workflow_id}")


class LLMAnalysisService:
    """LLM分析服务"""
    
    def __init__(self, llm_manager: LLMManager, workflow_registry: WorkflowRegistry, container: DependencyContainer):
        self.logger = get_logger("LLMAnalysisService")
        self.llm_manager = llm_manager
        self.workflow_registry = workflow_registry
        self.cache = LLMAnalysisCache(container=container)
        self.llm_dispatch_config = get_llm_dispatch_config(container)
    
    def analyze_message(self, message: str, history_messages: List[str] = None) -> Optional[LLMAnalysisResult]:
        """分析消息并返回推荐的工作流和参数"""
        llm_dispatch_config = self.llm_dispatch_config
        if not llm_dispatch_config.is_enabled:
            self.logger.debug("LLM分发已禁用")
            return None

        try:
            # 获取可用的工作流字典
            workflows_dict = self._get_available_workflows()
            if not workflows_dict:
                self.logger.warning("没有可用的工作流")
                return None

            # 转换为工作流ID列表用于缓存
            workflow_ids = list(workflows_dict.keys())

            # 检查缓存
            cached_result = self.cache.get(message, workflow_ids)
            if cached_result:
                return cached_result

            # 使用LLM分析
            if not history_messages:
                history_messages = []
            result = self._analyze_with_llm(message, history_messages, workflows_dict)

            # 缓存结果
            if result:
                self.cache.set(message, workflow_ids, result)

            return result

        except Exception as e:
            self.logger.error(f"LLM分析失败: {e}")
            return None
    
    def _get_available_workflows(self) -> Dict[str, WorkflowBuilder]:
        """获取可用的工作流列表"""
        try:
            llm_dispatch_config = self.llm_dispatch_config
            workflows = {}
            for workflow_id, workflow_builder in self.workflow_registry._workflows.items():
                # 只看白名单
                if workflow_id not in llm_dispatch_config.workflow_id_whitelist:
                    continue
                workflows[workflow_id] = workflow_builder

            if llm_dispatch_config.debug:
                self.logger.debug(f"可用工作流: {list(workflows.keys())}")

            return workflows
        except Exception as e:
            self.logger.error(f"获取工作流列表失败: {e}")
            return {}
    
    def _analyze_with_llm(self, message: str, history_messages: List[str], workflows_dict: Dict[str, WorkflowBuilder]) -> Optional[LLMAnalysisResult]:
        """使用LLM分析消息"""
        try:
            llm_dispatch_config = self.llm_dispatch_config
            # 构建工作流描述
            workflow_descriptions = []
            for workflow_id, workflow_builder in workflows_dict.items():
                workflow_descriptions.append(f"工作流ID: {workflow_id}")
                workflow_descriptions.append(f"工作流名字: {workflow_builder.name}")
                workflow_descriptions.append(f"描述: {workflow_builder.description}")

            workflows_text = "\n\n".join(workflow_descriptions)

            # 构建提示词
            prompt = llm_dispatch_config.analysis_prompt_template.format(
                message=message,
                history_messages=history_messages,
                workflows=workflows_text,
                default_workflow_id=llm_dispatch_config.default_workflow_id
            )

            if llm_dispatch_config.debug:
                self.logger.debug(f"分析提示词: {prompt}")

            # 调用LLM
            response = self.generate_text(prompt=prompt)

            if not response:
                self.logger.warning("LLM返回空响应")
                return None

            # 解析JSON响应
            result = self._parse_llm_response(response, workflows_dict)

            if llm_dispatch_config.debug:
                self.logger.debug(f"LLM分析结果: {result}")

            return result

        except Exception as e:
            import traceback
            print(traceback.format_exc())
            self.logger.error(f"LLM分析异常: {e}")
            return None

    def _parse_llm_response(self, response: str, workflows_dict: Dict[str, WorkflowBuilder]) -> Optional[LLMAnalysisResult]:
        """解析LLM的JSON响应"""
        try:
            llm_dispatch_config = self.llm_dispatch_config
            # 清理响应文本，提取JSON部分
            response = response.strip()

            # 尝试找到JSON部分
            json_start = response.find('{')
            json_end = response.rfind('}') + 1

            if json_start == -1 or json_end == 0:
                # 如果没有找到JSON，尝试直接解析为工作流ID
                workflow_id = response.strip()
                if workflow_id in workflows_dict or workflow_id == llm_dispatch_config.default_workflow_id:
                    return LLMAnalysisResult(
                        workflow_id=workflow_id,
                        confidence=1.0,
                        workflow_params={}
                    )
                else:
                    self.logger.warning(f"无法解析响应: {response}")
                    return None

            json_str = response[json_start:json_end]
            data = json.loads(json_str)

            # 提取字段
            workflow_id = data.get('workflow_id', '').strip()
            confidence = float(data.get('confidence', 1.0))
            workflow_params = data.get('workflow_params', {})

            # 验证工作流ID
            if not workflow_id:
                self.logger.warning("响应中缺少workflow_id")
                return None

            if workflow_id not in workflows_dict and workflow_id != llm_dispatch_config.default_workflow_id:
                self.logger.warning(f"无效的工作流ID: {workflow_id}")
                return None

            # 检查置信度阈值
            if confidence < llm_dispatch_config.confidence_threshold:
                self.logger.debug(f"置信度过低: {confidence} < {llm_dispatch_config.confidence_threshold}")
                return LLMAnalysisResult(
                    workflow_id=llm_dispatch_config.default_workflow_id,
                    confidence=confidence,
                    workflow_params=LLMAnalysisParams()
                )

            # 提取当前工作流的参数
            current_params = workflow_params.get(workflow_id, {})

            # 创建 LLMAnalysisParams 实例
            params = LLMAnalysisParams()
            params.update(current_params)

            return LLMAnalysisResult(
                workflow_id=workflow_id,
                confidence=confidence,
                workflow_params=params
            )

        except json.JSONDecodeError as e:
            self.logger.warning(f"JSON解析失败: {e}, 响应: {response}")
            # 尝试直接解析为工作流ID
            workflow_id = response.strip()
            if workflow_id in workflows_dict or workflow_id == llm_dispatch_config.default_workflow_id:
                return LLMAnalysisResult(
                    workflow_id=workflow_id,
                    confidence=1.0,
                    workflow_params=LLMAnalysisParams()
                )
            return None
        except Exception as e:
            self.logger.error(f"解析响应异常: {e}")
            return None

    def generate_text(self, prompt):
        """生成文本响应"""
        # try:
        llm_manager = self.llm_manager
        llm_dispatch_config = self.llm_dispatch_config
        model_id = llm_dispatch_config.default_model_id
        if not model_id:
            model_id = llm_manager.get_llm_id_by_ability(LLMAbility.Chat)
            if not model_id:
                raise ValueError("No available LLM models found")
            else:
                self.logger.info(
                    f"Model id unspecified, using default model: {model_id}"
                )
        else:
            self.logger.debug(f"Using specified model: {model_id}")

        llm = llm_manager.get_llm(model_id)
        if not llm:
            raise ValueError(f"LLM {model_id} not found, please check the model name")

        # 正确构造 LLMChatMessage
        message = LLMChatMessage(
            role="user",
            content=[LLMChatTextContent(text=prompt)]
        )

        req = LLMChatRequest(messages=[message], model=model_id)
        req.model = model_id
        
        # 在单独线程池中运行
        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future = executor.submit(llm.chat, req)
            response:LLMChatResponse = future.result(timeout=30)
            return response.message.content[0].text

        return None
        
        # except Exception as e:
        #     self.logger.error(f"生成文本时发生错误: {e}")
        #     # 不要重新抛出异常，返回None让调用者处理
        #     return None