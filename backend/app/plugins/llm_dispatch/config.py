"""
LLM智能分发插件配置
"""
from kirara_ai.ioc.container import DependencyContainer


class LLMDispatchConfig:
    """LLM智能分发配置"""

    def __init__(self,
                 enabled: bool = True,
                 default_model_id: str = 'deepseek-r1',
                 confidence_threshold: float = 0.7,
                 cache_enabled: bool = False,
                 cache_duration: int = 300,
                 timeout: int = 10,
                 debug: bool = False):
        # 是否启用LLM分发
        self.enabled = enabled
        self.default_model_id = default_model_id

        self.workflow_id_whitelist = [
            "rag:help", #默认
            "tools:weather",
            # "system:help",
            "system:clear_memory",
            "rag:list_repo",
            "rag:chat",
        ]
        self.default_workflow_id = self.workflow_id_whitelist[0]
        
        # 分析提示词模板
        self.analysis_prompt_template = """
请分析以下用户消息，判断用户的意图，并选择最合适的工作流来处理这个消息。

用户消息：{message}
历史消息: {history_messages}

可用的工作流：
{workflows}

请根据消息内容分析用户意图，并返回最合适的工作流ID。如果没有合适的工作流，则用"{default_workflow_id}"填充。

分析要求：
1. 仔细理解用户消息的语义和意图
2. 考虑消息的上下文和关键词
3. 选择最匹配的工作流，并给出置信度分数，提取对应工作流所需参数
4. 以json格式返回
5. json格式为{{
    "workflow_id":"xxx",
    "confidence":xx,
    "workflow_params":{{
        "tools:weather":{{
            "city":"北京",
            "date":"2025-01-02"
        }},
        "rag:chat_with_repository":{{
            "repo_id":1060,
            "question":"产品评级靠谱吗？"
        }}
    }}
}}

工作流ID："""
        
        # 置信度阈值
        self.confidence_threshold = confidence_threshold

        # 缓存配置
        self.cache_enabled = cache_enabled
        self.cache_duration = cache_duration  # 5分钟

        # 超时配置
        self.timeout = timeout  # 10秒

        # 调试模式
        self.debug = debug
    
    @property
    def is_enabled(self) -> bool:
        """检查是否启用LLM分发"""
        return self.enabled


def get_llm_dispatch_config(container:DependencyContainer)->LLMDispatchConfig:
    from app.plugins.llm_dispatch.config import LLMDispatchConfig as AbsoluteLLMDispatchConfig
    return container.resolve(AbsoluteLLMDispatchConfig)
