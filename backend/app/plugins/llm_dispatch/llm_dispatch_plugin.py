"""
LLM智能分发插件
基于大模型分析消息内容，智能分发到对应工作流
"""
import asyncio

from kirara_ai.ioc.container import DependencyContainer
from kirara_ai.plugin_manager.plugin import Plugin
from kirara_ai.logger import get_logger
from kirara_ai.workflow.core.dispatch.rules.base import DispatchRule
from .llm_dispatch_rule import LLMDispatchRule
from .config import get_llm_dispatch_config
from .llm_analysis_service import LLMAnalysisService



class LLMDispatchPlugin(Plugin):
    """LLM智能分发插件"""
    
    def __init__(self, container:DependencyContainer):
        self.logger = get_logger("LLMDispatchPlugin")
        self.logger.info("LLMDispatchPlugin 实例化")
        self.container = container
        self.llm_dispatch_config = None
    
    def on_load(self):
        """插件加载时调用"""
        self.logger.info("LLM智能分发插件加载完成")
    
    def on_start(self):
        """插件启动时调用"""
        self.logger.info("LLM智能分发插件启动完成")

        # 获取配置
        self.llm_dispatch_config = get_llm_dispatch_config(self.container)

        # 注册LLM分发规则
        try:
            # 初始化LLM分析服务（如果需要的话）
            self._setup_analysis_service()
            # 注册分发规则类型
            LLMDispatchRule.set_analysis_service(self.container.resolve(LLMAnalysisService))
            DispatchRule.register_rule_type(LLMDispatchRule)
            self.logger.info("LLM分发规则注册成功")

        except Exception as e:
            self.logger.error(f"注册LLM分发规则失败: {e}")

        # 显示配置信息
        if self.llm_dispatch_config.is_enabled:
            self.logger.info("LLM智能分发已启用")
            if self.llm_dispatch_config.debug:
                self.logger.info("调试模式已启用")
        else:
            self.logger.info("LLM智能分发已禁用")

    def _setup_analysis_service(self):
        """设置LLM分析服务"""
        try:
            if self.container:
                from kirara_ai.llm.llm_manager import LLMManager
                from kirara_ai.workflow.core.workflow.registry import WorkflowRegistry

                # 从容器获取依赖
                llm_manager = self.container.resolve(LLMManager)
                workflow_registry = self.container.resolve(WorkflowRegistry)

                # 创建分析服务
                analysis_service = LLMAnalysisService(llm_manager, workflow_registry, self.container)
                self.container.register(LLMAnalysisService, analysis_service)

                # 这里可以将分析服务设置到规则实例中
                # 实际使用时，规则实例会在运行时创建，所以这里主要是验证依赖可用性
                self.logger.info("LLM分析服务初始化成功")

        except Exception as e:
            self.logger.error(f"设置LLM分析服务失败: {e}")
    
    def on_stop(self):
        """插件停止时调用"""
        self.logger.info("LLM智能分发插件停止")
    
    def get_rule_class(self):
        """获取规则类（供外部使用）"""
        return LLMDispatchRule
