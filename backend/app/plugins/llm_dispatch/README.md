# LLM智能分发插件

基于大模型分析消息内容，智能分发到对应工作流的插件。

## 功能特性

- 🤖 使用大模型分析用户消息意图
- 🎯 智能推荐最合适的工作流
- 📊 支持置信度阈值配置
- 💾 智能缓存机制（可配置）
- ⚡ 异步处理，性能优化
- 🔧 扩展kirara-ai分发规则系统
- 🎛️ 灵活的配置选项
- 🐛 调试模式支持

## 工作原理

1. **消息接收**: 接收用户发送的消息
2. **意图分析**: 使用大模型分析消息内容和用户意图
3. **工作流推荐**: 根据分析结果推荐最合适的工作流
4. **智能分发**: 将消息分发到推荐的工作流进行处理
5. **结果缓存**: 缓存分析结果以提高响应速度

## 配置选项

### 环境变量配置

```bash
# 是否启用LLM分发
LLM_DISPATCH_ENABLED=true

# 置信度阈值（0-1）
LLM_DISPATCH_CONFIDENCE_THRESHOLD=0.7

# 是否启用缓存
LLM_DISPATCH_CACHE_ENABLED=true

# 缓存时长（秒）
LLM_DISPATCH_CACHE_DURATION=300

# 分析超时时间（秒）
LLM_DISPATCH_TIMEOUT=10

# 调试模式
LLM_DISPATCH_DEBUG=false
```

### 规则配置

在kirara-ai的分发规则管理界面中，可以配置以下选项：

- **目标工作流ID**: 指定特定的目标工作流，留空则匹配任何推荐的工作流
- **置信度阈值**: LLM分析的置信度阈值（0-1）
- **启用缓存**: 是否启用分析结果缓存
- **分析超时时间**: LLM分析的超时时间（秒）
- **自定义提示词模板**: 自定义的LLM分析提示词模板

## 使用示例

### 基本使用

```python
# 用户消息: "北京天气怎么样"
# LLM分析: 识别为天气查询意图
# 推荐工作流: "weather:query"
# 分发结果: 消息被分发到天气查询工作流

# 用户消息: "掷个骰子"
# LLM分析: 识别为游戏意图
# 推荐工作流: "game:dice"
# 分发结果: 消息被分发到骰子游戏工作流
```

### 规则配置示例

```json
{
  "target_workflow_id": "weather:query",
  "confidence_threshold": 0.8,
  "enable_cache": true,
  "analysis_timeout": 15,
  "custom_prompt_template": null
}
```

## 安装和配置

### 1. 插件安装

将插件文件放置在 `backend/app/plugins/llm_dispatch/` 目录下：

```
backend/app/plugins/llm_dispatch/
├── __init__.py                 # 插件导出
├── llm_dispatch_plugin.py      # 主插件类
├── llm_dispatch_rule.py        # 分发规则类
├── llm_analysis_service.py     # LLM分析服务
├── llm_dispatch_factory.py     # 规则工厂
├── config.py                   # 配置管理
├── test_llm_dispatch.py        # 单元测试
├── integration_test.py         # 集成测试
├── example_usage.py            # 使用示例
└── README.md                   # 文档
```

### 2. 环境配置

在 `.env` 文件或环境变量中配置：

```bash
# 启用LLM智能分发
LLM_DISPATCH_ENABLED=true

# 置信度阈值
LLM_DISPATCH_CONFIDENCE_THRESHOLD=0.7

# 缓存配置
LLM_DISPATCH_CACHE_ENABLED=true
LLM_DISPATCH_CACHE_DURATION=300

# 超时配置
LLM_DISPATCH_TIMEOUT=10

# 调试模式
LLM_DISPATCH_DEBUG=false
```

### 3. 启动服务

重启kirara-ai服务，插件会自动加载：

```bash
cd backend
python run.py
```

查看日志确认插件加载成功：

```
[LLMDispatchPlugin] LLM智能分发插件启动完成
[LLMDispatchPlugin] LLM分发规则注册成功
[LLMDispatchPlugin] LLM智能分发已启用
```

### 4. 创建分发规则

在kirara-ai的Web管理界面中：

1. 访问 "工作流" → "分发规则"
2. 点击 "创建规则"
3. 选择规则类型为 "LLM智能分发"
4. 配置规则参数
5. 启用规则

## 技术架构

### 核心组件

1. **LLMDispatchPlugin**: 主插件类，负责插件生命周期管理
2. **LLMDispatchRule**: 分发规则类，扩展kirara-ai的基础规则
3. **LLMAnalysisService**: LLM分析服务，负责消息分析和工作流推荐
4. **LLMAnalysisCache**: 分析结果缓存管理器
5. **LLMDispatchConfig**: 配置管理类

### 工作流程

1. 用户发送消息到kirara-ai
2. 分发系统检查LLM分发规则
3. LLM分发规则调用分析服务
4. 分析服务使用大模型分析消息
5. 返回推荐的工作流ID
6. 分发系统将消息路由到对应工作流

## 性能优化

- **缓存机制**: 相同消息的分析结果会被缓存，避免重复分析
- **异步处理**: 使用异步方式调用LLM，不阻塞主线程
- **超时控制**: 设置分析超时时间，避免长时间等待
- **智能过滤**: 自动过滤系统工作流，只分析用户相关的工作流

## 错误处理

- LLM分析失败时自动降级，不影响其他分发规则
- 网络超时时返回默认结果
- 无效工作流ID时自动过滤
- 详细的错误日志记录

## 扩展功能

插件设计为可扩展的架构，可以轻松添加以下功能：

- 多模型支持（不同场景使用不同模型）
- 上下文感知（考虑历史对话）
- 用户偏好学习（根据用户行为调整推荐）
- 工作流描述增强（更详细的工作流信息）
- 分析结果评分（置信度评估）

## 调试和监控

启用调试模式后，插件会输出详细的分析日志：

```
[LLMDispatchRule] LLM分析结果: weather:query
[LLMDispatchRule] 目标工作流匹配: weather:query == weather:query -> True
[LLMAnalysisCache] 缓存设置: abc123 -> weather:query
```

## 注意事项

1. 需要配置有效的LLM后端（如DeepSeek、OpenAI等）
2. LLM分析会消耗一定的计算资源和时间
3. 建议合理设置缓存时间以平衡性能和准确性
4. 在生产环境中建议启用缓存以提高响应速度
