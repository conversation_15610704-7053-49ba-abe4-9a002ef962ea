"""
LLM智能分发工厂
用于创建配置完整的LLM分发规则实例
"""
from typing import Optional
from kirara_ai.logger import get_logger
from kirara_ai.llm.llm_manager import LLMManager
from kirara_ai.workflow.core.workflow.registry import WorkflowRegistry
from kirara_ai.ioc.container import DependencyContainer

from .llm_dispatch_rule import LLMDispatchRule, LLMDispatchRuleConfig
from .llm_analysis_service import LLMAnalysisService


class LLMDispatchFactory:
    """LLM智能分发规则工厂"""
    
    def __init__(self, llm_manager: LLMManager, workflow_registry: WorkflowRegistry, container: DependencyContainer):
        self.logger = get_logger("LLMDispatchFactory")
        self.llm_manager = llm_manager
        self.workflow_registry = workflow_registry
        self._analysis_service: Optional[LLMAnalysisService] = None
    
    def get_analysis_service(self) -> LLMAnalysisService:
        """获取或创建LLM分析服务"""
        if self._analysis_service is None:
            self._analysis_service = LLMAnalysisService(
                self.llm_manager,
                self.workflow_registry,
                self.container
            )
        return self._analysis_service
    
    def create_rule(
        self,
        workflow_id: str,
        config: Optional[LLMDispatchRuleConfig] = None
    ) -> LLMDispatchRule:
        """创建配置完整的LLM分发规则"""
        if config is None:
            config = LLMDispatchRuleConfig()
        
        # 创建规则实例
        rule = LLMDispatchRule.from_config(
            config=config,
            workflow_registry=self.workflow_registry,
            workflow_id=workflow_id
        )
        
        # 设置分析服务
        rule.set_analysis_service(self.get_analysis_service())
        
        self.logger.debug(f"创建LLM分发规则: {workflow_id}")
        return rule
