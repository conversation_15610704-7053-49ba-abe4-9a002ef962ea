from typing import Dict, Any
from dataclasses import dataclass, field


@dataclass
class LLMAnalysisParams:
    """LLM分析参数"""
    data: Dict[str, Any] = field(default_factory=dict)

    def LLMAnalysisParams(self, data: Dict[str, Any]):
        self.data = data

    def get(self, key: str, default=None):
        """获取参数值"""
        return self.data.get(key, default)

    def set(self, key: str, value: Any):
        """设置参数值"""
        self.data[key] = value

    def update(self, other_data: Dict[str, Any]):
        """更新参数"""
        self.data.update(other_data)


@dataclass
class LLMAnalysisResult:
    """LLM分析结果"""
    workflow_id: str
    confidence: float
    workflow_params: LLMAnalysisParams = field(default_factory=LLMAnalysisParams)