"""
用户向导API接口
"""

from typing import Dict, Any, Optional
from quart import Blueprint, request, jsonify

from kirara_ai.database.manager import DatabaseManager
from kirara_ai.logger import get_logger
from app.services.user_guide_service import UserGuideService

logger = get_logger("UserGuideAPI")

# 创建蓝图
user_guide_bp = Blueprint('user_guide', __name__)

# 全局服务实例
user_guide_service: Optional[UserGuideService] = None


def init_user_guide_service(db_manager: DatabaseManager):
    """初始化用户向导服务"""
    global user_guide_service
    user_guide_service = UserGuideService(db_manager)


@user_guide_bp.route('/user-guide/steps', methods=['GET'])
async def get_user_steps():
    """获取用户向导步骤状态"""
    try:
        if not user_guide_service:
            return jsonify({'error': 'User guide service not initialized'}), 500
        
        # 从查询参数获取用户ID，默认为'default'
        user_id = request.args.get('user_id', 'default')
        
        steps = user_guide_service.get_user_steps(user_id)
        return jsonify({
            'success': True,
            'data': {
                'user_id': user_id,
                'steps': steps
            }
        })
        
    except Exception as e:
        logger.error(f"获取用户向导步骤API错误: {e}")
        return jsonify({'error': str(e)}), 500


@user_guide_bp.route('/user-guide/steps/<step_key>', methods=['PUT'])
async def update_step(step_key: str):
    """更新向导步骤状态"""
    try:
        if not user_guide_service:
            return jsonify({'error': 'User guide service not initialized'}), 500
        
        data = await request.get_json()
        if not data:
            return jsonify({'error': 'Request body is required'}), 400
        
        completed = data.get('completed')
        if completed is None:
            return jsonify({'error': 'completed field is required'}), 400
        
        user_id = data.get('user_id', 'default')
        metadata = data.get('metadata')
        
        success = user_guide_service.update_step(
            step_key=step_key,
            completed=bool(completed),
            user_id=user_id,
            metadata=metadata
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': f'Step {step_key} updated successfully'
            })
        else:
            return jsonify({'error': 'Failed to update step'}), 500
            
    except Exception as e:
        logger.error(f"更新向导步骤API错误: {e}")
        return jsonify({'error': str(e)}), 500


@user_guide_bp.route('/user-guide/settings', methods=['GET'])
async def get_user_settings():
    """获取用户向导设置"""
    try:
        if not user_guide_service:
            return jsonify({'error': 'User guide service not initialized'}), 500
        
        # 从查询参数获取用户ID，默认为'default'
        user_id = request.args.get('user_id', 'default')
        
        settings = user_guide_service.get_user_settings(user_id)
        return jsonify({
            'success': True,
            'data': settings
        })
        
    except Exception as e:
        logger.error(f"获取用户向导设置API错误: {e}")
        return jsonify({'error': str(e)}), 500


@user_guide_bp.route('/user-guide/settings', methods=['PUT'])
async def update_user_settings():
    """更新用户向导设置"""
    try:
        if not user_guide_service:
            return jsonify({'error': 'User guide service not initialized'}), 500
        
        data = await request.get_json()
        if not data:
            return jsonify({'error': 'Request body is required'}), 400
        
        user_id = data.get('user_id', 'default')
        hide_guide = data.get('hide_guide')
        settings = data.get('settings')
        
        success = user_guide_service.update_user_settings(
            user_id=user_id,
            hide_guide=hide_guide,
            settings=settings
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': 'User guide settings updated successfully'
            })
        else:
            return jsonify({'error': 'Failed to update user guide settings'}), 500
            
    except Exception as e:
        logger.error(f"更新用户向导设置API错误: {e}")
        return jsonify({'error': str(e)}), 500


@user_guide_bp.route('/user-guide/steps/all', methods=['GET'])
async def get_all_steps():
    """获取用户的所有向导步骤详细信息"""
    try:
        if not user_guide_service:
            return jsonify({'error': 'User guide service not initialized'}), 500
        
        # 从查询参数获取用户ID，默认为'default'
        user_id = request.args.get('user_id', 'default')
        
        steps = user_guide_service.get_all_steps(user_id)
        return jsonify({
            'success': True,
            'data': {
                'user_id': user_id,
                'steps': steps
            }
        })
        
    except Exception as e:
        logger.error(f"获取所有向导步骤API错误: {e}")
        return jsonify({'error': str(e)}), 500


@user_guide_bp.route('/user-guide/reset', methods=['POST'])
async def reset_user_guide():
    """重置用户向导"""
    try:
        if not user_guide_service:
            return jsonify({'error': 'User guide service not initialized'}), 500
        
        data = await request.get_json() or {}
        user_id = data.get('user_id', 'default')
        
        success = user_guide_service.reset_user_guide(user_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': 'User guide reset successfully'
            })
        else:
            return jsonify({'error': 'Failed to reset user guide'}), 500
            
    except Exception as e:
        logger.error(f"重置用户向导API错误: {e}")
        return jsonify({'error': str(e)}), 500


@user_guide_bp.route('/user-guide/batch-update', methods=['POST'])
async def batch_update_steps():
    """批量更新向导步骤"""
    try:
        if not user_guide_service:
            return jsonify({'error': 'User guide service not initialized'}), 500
        
        data = await request.get_json()
        if not data:
            return jsonify({'error': 'Request body is required'}), 400
        
        user_id = data.get('user_id', 'default')
        steps = data.get('steps', {})
        
        if not isinstance(steps, dict):
            return jsonify({'error': 'steps must be a dictionary'}), 400
        
        success_count = 0
        total_count = len(steps)
        
        for step_key, completed in steps.items():
            success = user_guide_service.update_step(
                step_key=step_key,
                completed=bool(completed),
                user_id=user_id
            )
            if success:
                success_count += 1
        
        return jsonify({
            'success': True,
            'message': f'Updated {success_count}/{total_count} steps successfully',
            'success_count': success_count,
            'total_count': total_count
        })
        
    except Exception as e:
        logger.error(f"批量更新向导步骤API错误: {e}")
        return jsonify({'error': str(e)}), 500
