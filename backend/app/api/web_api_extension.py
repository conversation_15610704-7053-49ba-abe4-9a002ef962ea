"""
Web API 扩展模块
扩展 kirara-ai 的 Web API 应用，添加自定义的聊天接口
"""

from quart import Quart
from kirara_ai.ioc.container import DependencyContainer
from kirara_ai.logger import get_logger

from .chat import chat_bp, init_chat_service
from .user_guide import user_guide_bp, init_user_guide_service
from .oauth_auth import oauth_auth_bp


def extend_web_api_app(app: Quart, container: DependencyContainer):
    """
    扩展 Web API 应用

    Args:
        app: Quart 应用实例
        container: 依赖注入容器
    """
    logger = get_logger("WebApiExtension")
    try:
        # 保存原有的路由，以便检查是否有冲突
        original_routes = list(app.url_map.iter_rules())
        logger.info(f"原有路由数量: {len(original_routes)}")

        # 初始化聊天服务
        init_chat_service(container)

        # 初始化用户向导服务
        from kirara_ai.database.manager import DatabaseManager
        db_manager = container.resolve(DatabaseManager)
        init_user_guide_service(db_manager)

        # 注册聊天API蓝图
        app.register_blueprint(chat_bp, url_prefix='/api/web/')

        # 注册用户向导API蓝图
        app.register_blueprint(user_guide_bp, url_prefix='/api/web/')

        # 注册 OAuth 认证API蓝图
        app.register_blueprint(oauth_auth_bp, url_prefix='/api/auth/')

        logger.info("聊天API接口已注册")

        # 创建一个新的蓝图来添加额外的路由
        from quart import Blueprint
        extra_bp = Blueprint('extra', __name__)

        @extra_bp.route('/health')
        async def health_check():
            return {'status': 'ok', 'service': 'ai-chat-backend'}

        @extra_bp.route('/chat-api/info')
        async def api_info():
            return {
                'name': 'AI Chat Backend',
                'version': '1.0.0',
                'description': '基于 kirara-ai 的 AI 聊天后端服务',
                'endpoints': {
                    'chat': {
                        # 'send_message': 'POST /api/web/chat',
                        'get_sessions': 'GET /api/web/sessions',
                        'create_session': 'POST /api/web/sessions',
                        'get_history': 'GET /api/web/history/{session_id}',
                        'delete_session': 'DELETE /api/web/sessions/{session_id}',
                        'clear_history': 'DELETE /api/web/history/{session_id}',
                        'update_session': 'PUT /api/web/sessions/{session_id}'
                    },
                    'user_guide': {
                        'get_steps': 'GET /api/web/user-guide/steps',
                        'update_step': 'PUT /api/web/user-guide/steps/{step_key}',
                        'get_settings': 'GET /api/web/user-guide/settings',
                        'update_settings': 'PUT /api/web/user-guide/settings',
                        'get_all_steps': 'GET /api/web/user-guide/steps/all',
                        'reset_guide': 'POST /api/web/user-guide/reset',
                        'batch_update': 'POST /api/web/user-guide/batch-update'
                    }
                }
            }

        # 注册额外的蓝图
        app.register_blueprint(extra_bp)

        # 检查新增的路由
        new_routes = list(app.url_map.iter_rules())
        logger.info(f"扩展后路由数量: {len(new_routes)}")

        # 列出新增的路由
        added_routes = [rule for rule in new_routes if rule not in original_routes]
        for route in added_routes:
            print(f"新增路由: {route.methods} {route.rule}")

        logger.info("Web API 扩展完成")

    except Exception as e:
        logger.error(f"扩展 Web API 应用失败: {e}")
        raise
