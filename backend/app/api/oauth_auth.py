"""
OAuth2 认证扩展
为现有的认证系统添加 OAuth2 支持
"""

from typing import Optional

from quart import Blueprint, jsonify, request, g
from pydantic import BaseModel

from kirara_ai.logger import get_logger
from app.services.user_permission import UserPermissionService

from app.config import settings
from app.utils.oauth_client import oauth2_client

logger = get_logger("OAuthAuth")

# 创建蓝图
oauth_auth_bp = Blueprint('oauth_auth', __name__)


class OAuthTokenRequest(BaseModel):
    code: str
    state: Optional[str] = None


class OAuthConfigResponse(BaseModel):
    client_id: str
    redirect_uri: str
    authorize_url: str
    scope: str = "openid profile email"
    response_type: str = "code"


class UserInfo(BaseModel):
    id: str
    username: str
    email: Optional[str] = None
    name: Optional[str] = None


@oauth_auth_bp.route('/oauth/config', methods=['GET'])
async def get_oauth_config():
    """获取 OAuth2 配置"""
    try:
        config = OAuthConfigResponse(
            client_id=settings.OIDC_CLIENT_ID,
            redirect_uri=settings.OIDC_REDIRECT_URL,
            authorize_url=settings.OIDC_AUTHORIZATION_URL,
            scope="openid profile email",
            response_type="code"
        )
        logger.info("OAuth 配置获取成功")
        return config.model_dump()
    except Exception as e:
        logger.error(f"获取 OAuth 配置失败: {e}")
        return jsonify({'error': '获取配置失败'}), 500


@oauth_auth_bp.route('/oauth/token', methods=['POST'])
async def exchange_token():
    """使用授权码换取访问令牌"""
    try:
        data = await request.get_json()
        response_data = await oauth2_client.exchange_code_for_token(data['code'])
        # 构建登录响应，包含用户信息
        response_data = {
            'access_token': response_data.get('access_token'),
            'refresh_token':response_data.get('refresh_token'),
            'user_info': response_data.get('user_info')
        }
        return response_data
        
    except Exception as e:
        logger.error(f"OAuth 令牌交换失败: {e}")
        return jsonify({'error': '登录失败'}), 500


async def get_user_info(access_token: str) -> Optional[UserInfo]:
    """使用访问令牌获取用户信息"""
    try:
        user_data = await oauth2_client.get_userinfo(access_token=access_token)
        # 根据实际的 OAuth 服务器响应格式调整字段映射
        return UserInfo(
            id=str(user_data.get('id', user_data.get('sub', ''))),
            username=user_data.get('username', user_data.get('preferred_username', '')),
            email=user_data.get('email'),
            name=user_data.get('name', user_data.get('display_name'))
        )
    except Exception as e:
        logger.error(f"获取用户信息异常: {e}")
        return None


async def is_user_authorized(access_token, user_info: UserInfo) -> bool:
    """检查用户是否有权限访问"""
    # 这里可以添加更复杂的权限检查逻辑
    # 例如：检查用户是否在允许的用户列表中，或者检查用户的角色等

    # 目前简单检查用户信息是否完整
    if not user_info.id or not user_info.username:
        return False

    # 使用 await 调用异步方法
    return await UserPermissionService().is_admin(access_token, user_info.id)


@oauth_auth_bp.route('/oauth/userinfo', methods=['GET'])
async def get_current_user_info():
    """获取当前登录用户信息（需要认证）"""
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'error': '未提供有效的认证令牌'}), 401

        access_token = auth_header.split(' ')[1]

        # 使用访问令牌获取用户信息
        user_info = await get_user_info(access_token)
        if not user_info:
            return jsonify({'error': '获取用户信息失败'}), 400

        return jsonify(user_info.model_dump())

    except Exception as e:
        logger.error(f"获取当前用户信息失败: {e}")
        return jsonify({'error': '获取用户信息失败'}), 500


@oauth_auth_bp.route('/oauth/me', methods=['GET'])
async def get_me():
    """获取当前登录用户的详细信息（页面刷新时使用）"""
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'error': '未提供有效的认证令牌'}), 401

        access_token = auth_header.split(' ')[1]

        # 使用访问令牌获取用户信息
        user_info = await get_user_info(access_token)
        if not user_info:
            return jsonify({'error': '获取用户信息失败'}), 400

        # 增强用户信息，添加更多字段
        enhanced_info = user_info.model_dump()

        # 获取用户权限信息
        try:
            permission_service = UserPermissionService()
            is_admin = await permission_service.is_admin(access_token, user_info.id)
            if not is_admin:
                return jsonify({'error': '需要管理员权限'}), 403

            enhanced_info.update({
                'roles': ['admin'] if is_admin else ['user'],
                'permissions': ['admin'] if is_admin else [],
                'is_admin': is_admin,
                'last_login': str(g.get('last_login_time', '')),
                'session_valid': True
            })
        except Exception as e:
            logger.error(f"获取用户权限信息失败: {e}")
            enhanced_info.update({
                'roles': ['user'],
                'permissions': [],
                'is_admin': False,
                'last_login': str(g.get('last_login_time', '')),
                'session_valid': True
            })

        logger.info(f"获取用户详细信息成功: {user_info.username}")
        return jsonify(enhanced_info)

    except Exception as e:
        logger.error(f"获取用户详细信息失败: {e}")
        return jsonify({'error': '获取用户详细信息失败'}), 500
