"""
聊天API接口
提供AI聊天功能的HTTP接口
"""

import uuid
from datetime import datetime
from typing import Dict, List, Optional

from quart import Blueprint, request, jsonify
from kirara_ai.ioc.container import DependencyContainer
from kirara_ai.llm.llm_manager import LLMManager
from kirara_ai.logger import get_logger

# 创建蓝图
chat_bp = Blueprint('chat', __name__)
logger = get_logger('ChatService')

# 内存存储（生产环境应使用数据库）
sessions_store: Dict[str, Dict] = {}
messages_store: Dict[str, List[Dict]] = {}


class ChatService:
    """聊天服务类"""
    
    def __init__(self, container: DependencyContainer):
        self.container = container
        try:
            self.llm_manager = container.resolve(LLMManager)
            self.llm_available = True
        except Exception as e:
            logger.warning(f"LLM管理器不可用: {e}")
            self.llm_manager = None
            self.llm_available = False
    
    async def send_message(self, message: str, session_id: Optional[str] = None,
                          user_id: Optional[str] = None, username: Optional[str] = None,
                          metadata: Optional[Dict] = None) -> Dict:
        """发送聊天消息"""
        try:
            # 如果没有提供session_id，创建新会话
            if not session_id:
                session_id = self.create_session()
            
            # 确保会话存在
            if session_id not in sessions_store:
                self.create_session(session_id)
            
            # 创建用户消息
            user_message = {
                'id': str(uuid.uuid4()),
                'content': message,
                'role': 'user',
                'timestamp': int(datetime.now().timestamp() * 1000),
                'session_id': session_id,
                'metadata': metadata or {}
            }
            
            # 添加到消息历史
            if session_id not in messages_store:
                messages_store[session_id] = []
            messages_store[session_id].append(user_message)
            
            # 调用LLM生成响应
            ai_response_content = await self._generate_ai_response(message, session_id)

            # 创建AI响应消息
            ai_message = {
                'id': str(uuid.uuid4()),
                'content': ai_response_content,
                'role': 'assistant',
                'timestamp': int(datetime.now().timestamp() * 1000),
                'session_id': session_id,
                'metadata': {}
            }
            
            # 添加AI响应到消息历史
            messages_store[session_id].append(ai_message)
            
            # 更新会话信息
            self._update_session(session_id, ai_response_content)
            
            return {
                'message_id': user_message['id'],
                'session_id': session_id,
                'status': 'completed',
                'response': ai_message
            }
            
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            return {
                'message_id': str(uuid.uuid4()),
                'session_id': session_id or 'unknown',
                'status': 'error',
                'error': str(e)
            }
    
    async def _generate_ai_response(self, message: str, session_id: str) -> str:
        """生成AI响应"""
        try:
            # 获取会话历史作为上下文
            history = messages_store.get(session_id, [])

            # 构建对话上下文
            context_messages = []
            for msg in history[-10:]:  # 只取最近10条消息作为上下文
                context_messages.append({
                    'role': msg['role'],
                    'content': msg['content']
                })

            # 添加当前消息
            context_messages.append({
                'role': 'user',
                'content': message
            })

            logger.info(f"正在为会话 {session_id} 生成AI响应，上下文消息数: {len(context_messages)}")

            # 检查LLM是否可用
            if not self.llm_available or not self.llm_manager:
                logger.warning("LLM管理器不可用，使用模拟响应")
                return self._generate_mock_response(message)

            # 尝试调用LLM管理器
            try:
                # 检查是否有可用的LLM后端
                backends = self.llm_manager.active_backends
                if not backends:
                    logger.warning("没有可用的LLM后端，使用模拟响应")
                    return self._generate_mock_response(message)

                logger.info(f"可用的LLM后端: {[b.name for b in backends]}")

                # 使用第一个可用的后端
                backend = backends[0]
                logger.info(f"使用LLM后端: {backend.name}")

                # 调用LLM生成响应
                response = await self.llm_manager.generate_response(
                    messages=context_messages,
                    backend_name=backend.name
                )

                if hasattr(response, 'content'):
                    return response.content
                elif hasattr(response, 'text'):
                    return response.text
                else:
                    return str(response)

            except Exception as llm_error:
                logger.error(f"LLM调用失败: {llm_error}")
                # 使用模拟响应作为备用
                return self._generate_mock_response(message)

        except Exception as e:
            logger.error(f"生成AI响应失败: {e}")
            return f"抱歉，我遇到了一些问题：{str(e)}"

    def _generate_mock_response(self, message: str) -> str:
        """生成模拟AI响应（当LLM不可用时使用）"""
        import random

        # 基于用户消息的关键词生成响应
        message_lower = message.lower()

        if any(word in message_lower for word in ['你好', 'hello', 'hi', '您好']):
            # 返回多条消息来模拟您期望的格式
            responses = [
                "(微笑着抬起头)",
                "你好！我是AI助手，很高兴为您服务。",
                "(整理一下桌面文件)"
            ]
            return '\n'.join(responses)
        elif any(word in message_lower for word in ['谢谢', 'thank', '感谢']):
            responses = [
                "(点头致意)",
                "不客气！如果还有其他问题，随时可以问我。",
                "(继续专注地工作)"
            ]
            return '\n'.join(responses)
        elif any(word in message_lower for word in ['再见', 'bye', '拜拜']):
            responses = [
                "(挥手告别)",
                "再见！祝您生活愉快！",
                "(目送您离开)"
            ]
            return '\n'.join(responses)
        elif '?' in message or '？' in message or any(word in message_lower for word in ['什么', 'what', '如何', 'how', '为什么', 'why']):
            responses = [
                "(思考片刻)",
                f"关于您提到的「{message[:20]}...」，这是一个很好的问题。",
                "由于当前AI服务暂时不可用，我无法给出详细回答，请稍后再试。"
            ]
            return '\n'.join(responses)
        else:
            responses = [
                "(皱眉盯着手机屏幕，手指在键盘上停留片刻)",
                f"我收到了您的消息：「{message[:30]}...」",
                "当前AI服务暂时不可用，请稍后再试。"
            ]
            return '\n'.join(responses)
    
    def create_session(self, session_id: Optional[str] = None, title: Optional[str] = None) -> str:
        """创建新会话"""
        if not session_id:
            session_id = str(uuid.uuid4())
        
        now = int(datetime.now().timestamp() * 1000)
        sessions_store[session_id] = {
            'id': session_id,
            'title': title or '新会话',
            'created_at': now,
            'updated_at': now,
            'message_count': 0,
            'last_message': ''
        }
        
        messages_store[session_id] = []
        return session_id
    
    def _update_session(self, session_id: str, last_message: str):
        """更新会话信息"""
        if session_id in sessions_store:
            sessions_store[session_id].update({
                'updated_at': int(datetime.now().timestamp() * 1000),
                'message_count': len(messages_store.get(session_id, [])),
                'last_message': last_message[:100] + '...' if len(last_message) > 100 else last_message
            })
    
    def get_sessions(self) -> List[Dict]:
        """获取所有会话"""
        return list(sessions_store.values())
    
    def get_session_history(self, session_id: str) -> List[Dict]:
        """获取会话历史"""
        return messages_store.get(session_id, [])
    
    def delete_session(self, session_id: str) -> bool:
        """删除会话"""
        if session_id in sessions_store:
            del sessions_store[session_id]
            if session_id in messages_store:
                del messages_store[session_id]
            return True
        return False
    
    def clear_session_history(self, session_id: str) -> bool:
        """清除会话历史"""
        if session_id in messages_store:
            messages_store[session_id] = []
            if session_id in sessions_store:
                sessions_store[session_id].update({
                    'message_count': 0,
                    'last_message': '',
                    'updated_at': int(datetime.now().timestamp() * 1000)
                })
            return True
        return False


# 全局聊天服务实例
chat_service: Optional[ChatService] = None


def init_chat_service(container: DependencyContainer):
    """初始化聊天服务"""
    global chat_service
    chat_service = ChatService(container)



@chat_bp.route('/chat', methods=['POST'])
async def send_message():
    """发送聊天消息"""
    try:
        data = await request.get_json()
        message = data.get('message')
        session_id = data.get('session_id')
        user_id = data.get('user_id')
        metadata = data.get('metadata')

        if not message:
            return jsonify({'error': 'Message is required'}), 400

        if not chat_service:
            return jsonify({'error': 'Chat service not initialized'}), 500

        result = await chat_service.send_message(message, session_id, user_id, metadata)
        return jsonify(result)

    except Exception as e:
        logger.error(f"发送消息API错误: {e}")
        return jsonify({'error': str(e)}), 500


@chat_bp.route('/sessions', methods=['GET'])
async def get_sessions():
    """获取所有会话"""
    try:
        if not chat_service:
            return jsonify({'error': 'Chat service not initialized'}), 500
        
        sessions = chat_service.get_sessions()
        return jsonify({'sessions': sessions})
        
    except Exception as e:
        logger.error(f"获取会话列表API错误: {e}")
        return jsonify({'error': str(e)}), 500


@chat_bp.route('/sessions', methods=['POST'])
async def create_session():
    """创建新会话"""
    try:
        data = await request.get_json() or {}
        title = data.get('title')
        
        if not chat_service:
            return jsonify({'error': 'Chat service not initialized'}), 500
        
        session_id = chat_service.create_session(title=title)
        session = sessions_store[session_id]
        return jsonify({'session': session})
        
    except Exception as e:
        logger.error(f"创建会话API错误: {e}")
        return jsonify({'error': str(e)}), 500


@chat_bp.route('/history/<session_id>', methods=['GET'])
async def get_session_history(session_id: str):
    """获取会话历史"""
    try:
        if not chat_service:
            return jsonify({'error': 'Chat service not initialized'}), 500
        
        messages = chat_service.get_session_history(session_id)
        return jsonify({'messages': messages})
        
    except Exception as e:
        logger.error(f"获取会话历史API错误: {e}")
        return jsonify({'error': str(e)}), 500


@chat_bp.route('/sessions/<session_id>', methods=['DELETE'])
async def delete_session(session_id: str):
    """删除会话"""
    try:
        if not chat_service:
            return jsonify({'error': 'Chat service not initialized'}), 500
        
        success = chat_service.delete_session(session_id)
        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'error': 'Session not found'}), 404
            
    except Exception as e:
        logger.error(f"删除会话API错误: {e}")
        return jsonify({'error': str(e)}), 500


@chat_bp.route('/history/<session_id>', methods=['DELETE'])
async def clear_session_history(session_id: str):
    """清除会话历史"""
    try:
        if not chat_service:
            return jsonify({'error': 'Chat service not initialized'}), 500
        
        success = chat_service.clear_session_history(session_id)
        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'error': 'Session not found'}), 404
            
    except Exception as e:
        logger.error(f"清除会话历史API错误: {e}")
        return jsonify({'error': str(e)}), 500


@chat_bp.route('/sessions/<session_id>', methods=['PUT'])
async def update_session_title(session_id: str):
    """更新会话标题"""
    try:
        data = await request.get_json()
        title = data.get('title')
        
        if not title:
            return jsonify({'error': 'Title is required'}), 400
        
        if session_id not in sessions_store:
            return jsonify({'error': 'Session not found'}), 404
        
        sessions_store[session_id]['title'] = title
        sessions_store[session_id]['updated_at'] = int(datetime.now().timestamp() * 1000)
        
        return jsonify({'session': sessions_store[session_id]})
        
    except Exception as e:
        logger.error(f"更新会话标题API错误: {e}")
        return jsonify({'error': str(e)}), 500
