"""
请求时间记录中间件
记录每个HTTP请求从开始到结束的处理时间
"""

import time
from typing import Optional
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from contextvars import ContextVar
from kirara_ai.logger import get_logger

# 请求开始时间的上下文变量
_request_start_time: ContextVar[Optional[float]] = ContextVar('request_start_time', default=None)

class RequestTimingMiddleware(BaseHTTPMiddleware):
    """
    请求时间记录中间件
    
    功能：
    1. 记录每个请求的开始和结束时间
    2. 计算请求处理耗时
    3. 在响应头中添加处理时间信息
    4. 记录慢请求日志
    5. 提供请求级别的时间上下文
    """
    
    def __init__(self, app, slow_request_threshold: float = 1.0, log_all_requests: bool = False):
        """
        初始化中间件
        
        Args:
            app: FastAPI应用实例
            slow_request_threshold: 慢请求阈值（秒），超过此时间的请求会被记录为慢请求
            log_all_requests: 是否记录所有请求的时间，默认只记录慢请求
        """
        super().__init__(app)
        self.slow_request_threshold = slow_request_threshold
        self.log_all_requests = log_all_requests
        self.logger = get_logger("RequestTiming")
        
        # 需要忽略的路径（避免日志噪音）
        self.ignored_paths = {
            '/favicon.ico',
            '/health',
            '/metrics',
            '/api/system/status'
        }
    
    async def dispatch(self, request: Request, call_next):
        """
        处理请求的中间件逻辑
        
        Args:
            request: 请求对象
            call_next: 下一个处理函数
            
        Returns:
            Response: 响应对象，包含处理时间信息
        """
        # 记录请求开始时间
        start_time = time.time()
        _request_start_time.set(start_time)
        
        # 获取请求信息
        method = request.method
        url = str(request.url)
        path = request.url.path
        client_ip = self._get_client_ip(request)
        
        try:
            # 调用下一个处理函数
            response = await call_next(request)
            
            # 计算处理时间
            end_time = time.time()
            duration = end_time - start_time
            duration_ms = duration * 1000
            
            # 添加响应头
            response.headers["X-Process-Time"] = f"{duration:.6f}"
            response.headers["X-Process-Time-Ms"] = f"{duration_ms:.2f}"
            
            # 记录日志
            self._log_request(method, path, url, client_ip, duration, response.status_code)
            
            return response
            
        except Exception as e:
            # 处理异常情况
            end_time = time.time()
            duration = end_time - start_time
            
            self.logger.error(
                f"请求处理异常 - {method} {path} - "
                f"耗时: {duration:.3f}s - "
                f"客户端: {client_ip} - "
                f"异常: {str(e)}"
            )
            raise
        finally:
            # 清理上下文变量
            _request_start_time.set(None)
    
    def _get_client_ip(self, request: Request) -> str:
        """
        获取客户端IP地址
        
        Args:
            request: 请求对象
            
        Returns:
            str: 客户端IP地址
        """
        # 尝试从各种头部获取真实IP
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 回退到连接IP
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return "unknown"
    
    def _should_ignore_path(self, path: str) -> bool:
        """
        检查是否应该忽略此路径的日志记录
        
        Args:
            path: 请求路径
            
        Returns:
            bool: 是否应该忽略
        """
        return path in self.ignored_paths
    
    def _log_request(self, method: str, path: str, url: str, client_ip: str,
                    duration: float, status_code: int):
        """
        记录请求日志

        Args:
            method: HTTP方法
            path: 请求路径
            url: 完整URL（当前未使用，保留用于扩展）
            client_ip: 客户端IP
            duration: 处理时间（秒）
            status_code: 响应状态码
        """
        # url参数保留用于未来扩展，当前未使用
        _ = url
        # 检查是否应该忽略此路径
        if self._should_ignore_path(path):
            return
        
        duration_ms = duration * 1000
        
        # 构建日志消息
        log_msg = (
            f"{method} {path} - "
            f"状态: {status_code} - "
            f"耗时: {duration:.3f}s ({duration_ms:.1f}ms) - "
            f"客户端: {client_ip}"
        )
        
        # 根据处理时间和配置决定日志级别
        if duration > self.slow_request_threshold:
            # 慢请求警告
            self.logger.warning(f"慢请求 - {log_msg}")
        elif self.log_all_requests:
            # 记录所有请求
            self.logger.info(log_msg)
        elif status_code >= 400:
            # 错误请求
            self.logger.warning(f"错误请求 - {log_msg}")


def get_request_start_time() -> Optional[float]:
    """
    获取当前请求的开始时间
    
    Returns:
        Optional[float]: 请求开始时间戳，如果不在请求上下文中则返回None
    """
    return _request_start_time.get()


def get_request_duration() -> Optional[float]:
    """
    获取当前请求已经处理的时间
    
    Returns:
        Optional[float]: 请求处理时间（秒），如果不在请求上下文中则返回None
    """
    start_time = _request_start_time.get()
    if start_time is None:
        return None
    
    return time.time() - start_time


def format_duration(duration: float) -> str:
    """
    格式化时间显示
    
    Args:
        duration: 时间（秒）
        
    Returns:
        str: 格式化的时间字符串
    """
    if duration < 0.001:
        return f"{duration * 1000000:.0f}μs"
    elif duration < 1:
        return f"{duration * 1000:.1f}ms"
    else:
        return f"{duration:.3f}s"


class RequestTimingContext:
    """
    请求时间上下文管理器
    用于在代码中手动测量特定操作的耗时
    """
    
    def __init__(self, operation_name: str, logger_name: str = "RequestTiming"):
        self.operation_name = operation_name
        self.logger = get_logger(logger_name)
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # 标记未使用的参数
        _ = exc_val, exc_tb

        self.end_time = time.time()
        duration = self.end_time - self.start_time

        if exc_type is None:
            self.logger.debug(f"{self.operation_name} 完成，耗时: {format_duration(duration)}")
        else:
            self.logger.warning(f"{self.operation_name} 异常，耗时: {format_duration(duration)}")
    
    @property
    def duration(self) -> Optional[float]:
        """获取操作耗时"""
        if self.start_time is None:
            return None
        
        end_time = self.end_time or time.time()
        return end_time - self.start_time
