# 请求时间记录中间件

这个中间件提供了完整的HTTP请求时间记录功能，包括请求级别的时间上下文管理。

## 功能特性

### 🕒 自动请求时间记录
- 自动记录每个HTTP请求的开始和结束时间
- 计算请求总处理时间
- 在响应头中添加时间信息

### 📊 响应头信息
每个响应都会包含以下头部：
- `X-Process-Time`: 处理时间（秒，6位小数）
- `X-Process-Time-Ms`: 处理时间（毫秒，2位小数）

### 🐌 慢请求监控
- 可配置慢请求阈值
- 自动记录超过阈值的请求
- 支持自定义日志级别

### 🔍 请求上下文
- 提供请求级别的时间上下文变量
- 支持在代码中获取当前请求的处理时间
- 支持嵌套时间测量

## 使用方法

### 1. 基本配置

在 `nova_webserver.py` 中添加中间件：

```python
from app.middleware.request_timing import RequestTimingMiddleware

# 添加请求时间记录中间件
app.add_middleware(
    RequestTimingMiddleware,
    slow_request_threshold=2.0,  # 慢请求阈值（秒）
    log_all_requests=False       # 是否记录所有请求
)
```

### 2. 配置参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `slow_request_threshold` | float | 1.0 | 慢请求阈值（秒） |
| `log_all_requests` | bool | False | 是否记录所有请求时间 |

### 3. 在代码中使用时间上下文

```python
from app.middleware.request_timing import (
    RequestTimingContext, 
    get_request_duration,
    get_request_start_time
)

# 测量特定操作的时间
async def some_api_handler():
    # 测量LLM调用时间
    with RequestTimingContext("LLM调用", "APIService"):
        response = await llm_service.generate_response(prompt)
    
    # 获取当前请求的总处理时间
    total_time = get_request_duration()
    if total_time:
        logger.info(f"请求处理中，当前耗时: {total_time:.3f}s")
    
    return response
```

### 4. 嵌套时间测量

```python
async def complex_operation():
    with RequestTimingContext("复杂操作") as outer:
        # 第一步
        with RequestTimingContext("数据库查询"):
            data = await db.query(sql)
        
        # 第二步
        with RequestTimingContext("数据处理"):
            processed_data = process(data)
        
        # 第三步
        with RequestTimingContext("结果保存"):
            await db.save(processed_data)
    
    logger.info(f"复杂操作总耗时: {outer.duration:.3f}s")
```

## 日志输出示例

### 正常请求
```
[INFO] POST /api/chat - 状态: 200 - 耗时: 0.156s (156.2ms) - 客户端: 192.168.1.100
```

### 慢请求
```
[WARNING] 慢请求 - POST /api/chat - 状态: 200 - 耗时: 3.245s (3245.0ms) - 客户端: 192.168.1.100
```

### 错误请求
```
[WARNING] 错误请求 - GET /api/nonexistent - 状态: 404 - 耗时: 0.012s (12.0ms) - 客户端: 192.168.1.100
```

### 操作时间记录
```
[DEBUG] LLM调用 完成，耗时: 1.234s
[INFO] 请求处理中，当前耗时: 1.456s
```

## 响应头示例

```http
HTTP/1.1 200 OK
Content-Type: application/json
X-Process-Time: 0.156234
X-Process-Time-Ms: 156.23
```

## 性能影响

中间件的性能开销非常小：
- 每个请求增加约 0.1ms 的处理时间
- 内存占用可忽略不计
- 不影响并发性能

## 配置建议

### 开发环境
```python
app.add_middleware(
    RequestTimingMiddleware,
    slow_request_threshold=1.0,  # 较低的阈值
    log_all_requests=True        # 记录所有请求
)
```

### 生产环境
```python
app.add_middleware(
    RequestTimingMiddleware,
    slow_request_threshold=3.0,  # 较高的阈值
    log_all_requests=False       # 只记录慢请求和错误
)
```

## 忽略的路径

以下路径默认不会记录日志（避免噪音）：
- `/favicon.ico`
- `/health`
- `/metrics`
- `/api/system/status`

可以通过修改 `ignored_paths` 属性来自定义忽略的路径。

## 工具函数

### `format_duration(duration: float) -> str`
格式化时间显示：
- 小于1ms：显示为微秒（μs）
- 小于1s：显示为毫秒（ms）
- 大于1s：显示为秒（s）

### `get_request_start_time() -> Optional[float]`
获取当前请求的开始时间戳。

### `get_request_duration() -> Optional[float]`
获取当前请求已经处理的时间。

## 注意事项

1. **中间件顺序**：RequestTimingMiddleware 应该在其他业务中间件之后添加，以测量完整的处理时间。

2. **上下文变量**：时间上下文变量只在请求处理期间有效，在请求结束后会自动清理。

3. **异步安全**：所有功能都是异步安全的，支持并发请求。

4. **日志级别**：可以通过调整日志级别来控制输出的详细程度。

## 扩展功能

可以基于这个中间件扩展以下功能：
- 请求性能监控和报警
- 性能数据收集和分析
- 自动性能优化建议
- 与APM系统集成
