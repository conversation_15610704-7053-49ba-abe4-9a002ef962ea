from kirara_ai.im.adapter import IMAdapter
from kirara_ai.im.message import IMMessage
from kirara_ai.ioc.container import DependencyContainer
from kirara_ai.logger import get_logger
from kirara_ai.workflow.core.dispatch.models.dispatch_rules import CombinedDispatchRule, RuleGroup, SimpleDispatchRule
from kirara_ai.workflow.core.dispatch.rules.base import DispatchRule
from kirara_ai.workflow.core.dispatch.registry import DispatchRuleRegistry
from kirara_ai.workflow.core.execution.executor import WorkflowExecutor
from kirara_ai.workflow.core.workflow.base import Workflow
from kirara_ai.workflow.core.workflow.registry import WorkflowRegistry

from app.config.env_settings import settings
from app.utils.waiting_messages import get_waiting_message

from datetime import datetime

# 导入LLM分析参数类型
from app.plugins.llm_dispatch.types import LLMAnalysisParams


class NovaWorkflowDispatcher:
    """nova工作流调度器(支持大模型分类)"""

    def __init__(self, container: DependencyContainer):
        self.container = container
        self.logger = get_logger("NovaWorkflowDispatcher")

        # 从容器获取注册表
        self.workflow_registry = container.resolve(WorkflowRegistry)
        self.dispatch_registry = container.resolve(DispatchRuleRegistry)

    def register_rule(self, rule: CombinedDispatchRule):
        """注册一个调度规则"""
        self.dispatch_registry.register(rule)
        self.logger.info(f"Registered dispatch rule: {rule}")

    async def dispatch(self, source: IMAdapter, message: IMMessage):
        """
        根据消息内容选择第一个匹配的规则进行处理
        """
        # 获取所有已启用的规则，按优先级排序
        active_rules = self.dispatch_registry.get_active_rules()

        llm_rule = None
        for rule in active_rules:
            # LLM 优先级最低， 稍候匹配
            if self._is_llm_dispatch(rule):
                llm_rule = rule
                continue
            if rule.match(message, self.workflow_registry):
                self.logger.debug(f"Matched rule {rule}, executing workflow")
                return await self._exec_workflow(source, message, rule=rule)
        return await self.dispatch_by_llm(llm_rule, source, message)
    
    async def _exec_workflow(self, source: IMAdapter, message: IMMessage, rule: CombinedDispatchRule = None, workflow_id: str = None, workflow_params: LLMAnalysisParams = None):
        try:
            with self.container.scoped() as scoped_container:
                # 注册依赖
                scoped_container.register(IMAdapter, source)
                scoped_container.register(IMMessage, message)

                # 注册工作流参数（如果有的话）
                if workflow_params:
                    scoped_container.register(LLMAnalysisParams, workflow_params)

                workflow = None
                if rule:
                    workflow = rule.get_workflow(scoped_container)
                elif workflow_id:
                    workflow = self.workflow_registry.get_workflow(workflow_id, container=scoped_container)
                if not workflow:
                    raise Exception("没有找到处理的工作流")
                
                if self._is_slow_workflow(workflow):
                    await self._send_wait_notice(source, message)
                
                scoped_container.register(Workflow, workflow)
                executor = WorkflowExecutor(scoped_container)
                scoped_container.register(WorkflowExecutor, executor)
                return await executor.run()
        except Exception as e:
            self.logger.opt(exception=e).error(f"Workflow execution failed: {e}", exc_info=True)
            return None
        
    def _is_slow_workflow(self, workflow:Workflow)->bool:
        return workflow.id in {"rag:chat",}
    
    async def dispatch_by_llm(self, combined_dispatch_rule: CombinedDispatchRule, source: IMAdapter, message: IMMessage):
        """使用大模型进行调度"""
        if not combined_dispatch_rule:
            self.logger.warning("没有可用的LLM分发规则")
            return None
        
        await self._send_wait_notice(source, message)

        rule_group: RuleGroup = combined_dispatch_rule.rule_groups[0]
        rule: SimpleDispatchRule = rule_group.rules[0]

        rule_class = DispatchRule.get_rule_type(rule.type)
        rule_instance = rule_class.from_config(
            rule_class.config_class(**rule.config),
            self.workflow_registry,
            combined_dispatch_rule.workflow_id if hasattr(combined_dispatch_rule, 'workflow_id') else None,
        )

        # 匹配消息并获取推荐的工作流
        if rule_instance.match(message):
            workflow_id = rule_instance.workflow_id
            workflow_params = getattr(rule_instance, 'workflow_params', {})

            self.logger.debug(f"LLM分发匹配成功: workflow_id={workflow_id}, params={workflow_params}")

            return await self._exec_workflow(source, message, workflow_id=workflow_id, workflow_params=workflow_params)
        else:
            self.logger.debug("LLM分发规则未匹配到合适的工作流")
            return None
        
    async def _send_wait_notice(self, source:IMAdapter, message:IMMessage):
        """LLM调度以及后续处理比较慢, 先给用户发等待提示"""
        wait_data = {
            'start_time':datetime.now(),
            'content': get_waiting_message()
        }
        wait_workflow_params = LLMAnalysisParams(data=wait_data)
        await self._exec_workflow(source, message, workflow_id=settings.IM_SEND_MSG_WORKFLOW_ID, workflow_params=wait_workflow_params)
    
    def _is_llm_dispatch(self, combined_dispatch_rule: CombinedDispatchRule) -> bool:
        """检查是否为LLM调度规则"""
        if not combined_dispatch_rule.rule_groups:
            return False
        rule_group: RuleGroup = combined_dispatch_rule.rule_groups[0]
        if not rule_group.rules:
            return False
        rule: SimpleDispatchRule = rule_group.rules[0]
        return rule.type == "llm_dispatch"
