from ryjx.iam.permission_client import permission_client
from kirara_ai.logger import get_logger

logger = get_logger("UserPermissionService")


class FakeRequest:
    headers = {}
    def __init__(self, headers):
        self.headers = headers

class UserPermissionService:
    async def is_admin(self, token, user_id):
        try:
            request = FakeRequest(headers={"Authorization": f"Bearer {token}"})
            has_write_permission = await permission_client.check_write_permission(
                request=request,
                user_id=user_id,
                object_code="all.all"
            )
            return has_write_permission
        except Exception as e:
            # 记录错误但不抛出异常，返回 False 表示没有权限
            logger.error(f"权限检查失败 - 用户ID: {user_id}, 错误: {e}")
            return False