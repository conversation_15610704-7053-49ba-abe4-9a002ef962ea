"""
基于Redis的分布式DispatchRuleRegistry实现
解决多机部署时规则同步问题
"""

import json

from kirara_ai.ioc.container import DependencyContainer
from kirara_ai.logger import get_logger
from kirara_ai.workflow.core.dispatch.models.dispatch_rules import CombinedDispatchRule
from kirara_ai.workflow.core.dispatch.registry import DispatchRuleRegistry
from kirara_ai.database.manager import DatabaseManager

from app.config.dispatch_rules_manager import DispatchRulesManager
from app.utils.kvdb_utils import KvDbDict


class NovaDispatchRuleRegistry(DispatchRuleRegistry):
    """基于Redis Hash的分布式调度规则注册表"""

    # Redis Hash键名常量
    REDIS_RULES_KEY = "dispatch_rules"

    def __init__(self, container: DependencyContainer):
        super().__init__(container)
        self.logger = get_logger("RedisDispatchRuleRegistry")

        # 获取Redis客户端和数据库管理器
        self.db_manager = container.resolve(DatabaseManager)
        self.rules_manager = DispatchRulesManager(self.db_manager)

        # 创建基于Redis Hash的分布式字典
        self.rules = KvDbDict(
            key=self.REDIS_RULES_KEY,
            serializer=self._serialize_rules,
            deserializer=self._deserialize_rules
        )

        # 初始化规则数据
        self._load_initial_rules()

    def _serialize_rules(self, rules_dict):
        """序列化规则字典为JSON"""
        rules_data = []
        for rule in rules_dict.values():
            try:
                if hasattr(rule, 'model_dump'):
                    rules_data.append(rule.model_dump())
                elif hasattr(rule, 'dict'):
                    # 兼容旧版本Pydantic
                    rules_data.append(rule.dict())
            except Exception as e:
                self.logger.error(f"序列化规则失败: {e}")
        return json.dumps(rules_data, ensure_ascii=False)

    def _deserialize_rules(self, data_str):
        """反序列化JSON为规则字典"""
        if not data_str:
            return {}

        try:
            rules_data = json.loads(data_str)
            rules_dict = {}
            for rule_data in rules_data:
                try:
                    rule = CombinedDispatchRule(**rule_data)
                    rules_dict[rule.rule_id] = rule
                except Exception as e:
                    self.logger.error(f"反序列化规则失败: {e}")
            return rules_dict
        except Exception as e:
            self.logger.error(f"反序列化失败: {e}")
            return {}

    def _load_initial_rules(self):
        """初始化加载规则数据"""
        try:
            # 如果Redis中没有数据，从数据库加载
            if len(self.rules) == 0:
                rules_data = self.rules_manager.load_rules()
                if rules_data:
                    for rule_data in rules_data:
                        try:
                            rule = CombinedDispatchRule(**rule_data)
                            # 直接设置到字典，会自动同步到Redis
                            self.rules[rule.rule_id] = rule
                        except Exception as e:
                            self.logger.error(f"加载规则失败: {e}")
        except Exception as e:
            self.logger.error(f"初始化规则失败: {e}")

    def register(self, rule: CombinedDispatchRule):
        """注册规则（会自动同步到Redis和数据库）"""
        if not rule.rule_id:
            raise ValueError("Rule must have an ID")

        # 设置到Redis字典，会自动触发同步
        self.rules[rule.rule_id] = rule

        # 同步到数据库
        self._sync_to_database()

        self.logger.info(f"Registered dispatch rule: {rule}")

    def delete_rule(self, rule_id: str):
        """删除规则（会自动同步到Redis和数据库）"""
        if rule_id not in self.rules:
            raise ValueError(f"Rule {rule_id} not found")

        # 从Redis字典删除，会自动触发同步
        del self.rules[rule_id]

        # 同步到数据库
        self._sync_to_database()

    def _sync_to_database(self):
        """同步当前规则到数据库"""
        try:
            rules_list = list(self.rules.values())
            success = self.rules_manager.save_rules(rules_list)
            if not success:
                self.logger.error("同步规则到数据库失败")
        except Exception as e:
            self.logger.error(f"同步到数据库失败: {e}")

