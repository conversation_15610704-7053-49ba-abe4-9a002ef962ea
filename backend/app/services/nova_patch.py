from datetime import datetime, timedelta
from typing import Dict

from sqlalchemy import case, func,cast,Date

from kirara_ai.database import DatabaseManager

from kirara_ai.tracing.models import LLMRequestTrace
from kirara_ai.tracing.llm_tracer import LLMTracer


class NoMigrationDatabaseManager(DatabaseManager):
    def _run_migrations(self):
        pass

class NovaLLMTracer(LLMTracer):
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        with self.db_manager.get_session() as session:
            # 基础统计
            total_count = session.query(func.count(LLMRequestTrace.id)).scalar() or 0
            success_count = session.query(func.count(LLMRequestTrace.id)).filter_by(status="success").scalar() or 0
            failed_count = session.query(func.count(LLMRequestTrace.id)).filter_by(status="failed").scalar() or 0
            pending_count = session.query(func.count(LLMRequestTrace.id)).filter_by(status="pending").scalar() or 0
            total_tokens = session.query(func.sum(LLMRequestTrace.total_tokens)).scalar() or 0

            # 获取30天内的每日统计
            thirty_days_ago = datetime.now() - timedelta(days=30)

            # 检测数据库类型并使用相应的日期函数
            engine = session.get_bind()
            if 'postgresql' in str(engine.url):
                # PostgreSQL 使用 date_trunc
                date_expr = func.date_trunc('day', LLMRequestTrace.request_time)
            else:
                # SQLite 使用 strftime
                date_expr = func.strftime('%Y-%m-%d', LLMRequestTrace.request_time)

            daily_stats = session.query(
                date_expr.label('date'),
                func.count(LLMRequestTrace.id).label('requests'),
                func.sum(LLMRequestTrace.total_tokens).label('tokens'),
                func.sum(case((LLMRequestTrace.status == 'success', 1), else_=0)).label('success'), # type: ignore
                func.sum(case((LLMRequestTrace.status == 'failed', 1), else_=0)).label('failed') # type: ignore
            ).filter(
                LLMRequestTrace.request_time >= thirty_days_ago # type: ignore
            ).group_by(
                date_expr
            ).order_by(
                date_expr
            ).all()

            # 格式化日期数据
            daily_data = []
            for row in daily_stats:
                if 'postgresql' in str(engine.url):
                    # PostgreSQL 返回 timestamp 对象，需要格式化
                    date_str = row.date.strftime('%Y-%m-%d') if row.date else ''
                else:
                    # SQLite 返回字符串
                    date_str = str(row.date) if row.date else ''

                daily_data.append({
                    'date': date_str,
                    'requests': row.requests,
                    'tokens': row.tokens or 0,
                    'success': row.success,
                    'failed': row.failed
                })

            # 按模型分组统计（最近30天）
            model_stats = []
            model_counts = session.query(
                LLMRequestTrace.model_id, # type: ignore
                func.count(LLMRequestTrace.id).label('count'),
                func.sum(LLMRequestTrace.total_tokens).label('tokens'),
                func.avg(LLMRequestTrace.duration).label('avg_duration')
            ).filter( # type: ignore
                LLMRequestTrace.request_time >= thirty_days_ago # type: ignore
            ).group_by(
                LLMRequestTrace.model_id
            ).all()

            for model_id, count, tokens, avg_duration in model_counts:
                model_stats.append({
                    'model_id': model_id,
                    'count': count,
                    'tokens': tokens or 0,
                    'avg_duration': float(avg_duration) if avg_duration else 0
                })

            # 按后端分组统计（最近30天）
            backend_stats = []
            backend_counts = session.query(
                LLMRequestTrace.backend_name, # type: ignore
                func.count(LLMRequestTrace.id).label('count'),
                func.sum(LLMRequestTrace.total_tokens).label('tokens'),
                func.avg(LLMRequestTrace.duration).label('avg_duration')
            ).filter( # type: ignore
                LLMRequestTrace.request_time >= thirty_days_ago # type: ignore
            ).group_by(
                LLMRequestTrace.backend_name
            ).all()

            for backend_name, count, tokens, avg_duration in backend_counts:
                backend_stats.append({
                    'backend_name': backend_name,
                    'count': count,
                    'tokens': tokens or 0,
                    'avg_duration': float(avg_duration) if avg_duration else 0
                })

            # 获取每小时统计（最近24小时）
            one_day_ago = datetime.now() - timedelta(hours=24)

            # 检测数据库类型并使用相应的日期函数
            engine = session.get_bind()
            if 'postgresql' in str(engine.url):
                # PostgreSQL 使用 date_trunc
                hour_expr = func.date_trunc('hour', LLMRequestTrace.request_time)
            else:
                # SQLite 使用 strftime
                hour_expr = func.strftime('%Y-%m-%d %H:00:00', LLMRequestTrace.request_time)

            hourly_stats = session.query(
                hour_expr.label('hour'),
                func.count(LLMRequestTrace.id).label('requests'),
                func.sum(LLMRequestTrace.total_tokens).label('tokens')
            ).filter(
                LLMRequestTrace.request_time >= one_day_ago # type: ignore
            ).group_by(
                hour_expr
            ).order_by(
                hour_expr
            ).all()

            # 格式化小时数据
            hourly_data = []
            for row in hourly_stats:
                if 'postgresql' in str(engine.url):
                    # PostgreSQL 返回 timestamp 对象，需要格式化
                    hour_str = row.hour.strftime('%Y-%m-%d %H:00:00') if row.hour else ''
                else:
                    # SQLite 返回字符串
                    hour_str = str(row.hour) if row.hour else ''

                hourly_data.append({
                    'hour': hour_str,
                    'requests': row.requests,
                    'tokens': row.tokens or 0
                })

            return {
                'overview': {
                    'total_requests': total_count,
                    'success_requests': success_count,
                    'failed_requests': failed_count,
                    'pending_requests': pending_count,
                    'total_tokens': total_tokens,
                },
                'daily_stats': daily_data,
                'hourly_stats': hourly_data,
                'models': model_stats,
                'backends': backend_stats
            }