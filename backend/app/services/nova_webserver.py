
import asyncio
import os
import socket
from pathlib import Path

from fastapi import <PERSON><PERSON><PERSON>,Request
from hypercorn.asyncio import serve
from hypercorn.config import Config
from quart import Quart
from starlette.middleware.base import BaseHTTPMiddleware

from kirara_ai.config.global_config import GlobalConfig
from kirara_ai.ioc.container import DependencyContainer
from kirara_ai.logger import HypercornLoggerWrapper
from kirara_ai.web.auth.services import AuthService


from kirara_ai.web.app import create_app, create_web_api_app,logger, custom_static_assets
from app.api.web_api_extension import extend_web_api_app
from app.services.nova_auth_service import NovaAuthService
from app.middleware.request_timing import RequestTimingMiddleware

class PathRewriteMiddleware(BaseHTTPMiddleware):

    rewrite_mapping = {
         '/chat-api/api/v1/chat': '/v1/chat',
         '/chat-api/api/v2/chat': '/v2/chat',
         '/chat-api/api/im/webhook/wechat/': '/im/webhook/wechat/',
    }
    async def dispatch(self, request: Request, call_next):
        # 重写路径
        for old_path, new_path in self.rewrite_mapping.items():
            if request.url.path.startswith(old_path):
                request.scope["path"] = new_path + request.url.path[len(old_path):]
                break
        response = await call_next(request)
        return response

class NovaWebServer:
    app: FastAPI
    web_api_app: Quart
    listen_host: str
    listen_port: int
    container: DependencyContainer
    base_url: str

    def __init__(self, container: DependencyContainer):
        self.app = create_app(container)
        self.web_api_app = create_web_api_app(container)

        # 添加中间件（注意顺序：后添加的先执行）
        self.app.add_middleware(PathRewriteMiddleware)

        # 添加请求时间记录中间件
        # slow_request_threshold: 慢请求阈值（秒）
        # log_all_requests: 是否记录所有请求（开发环境可设为True）
        self.app.add_middleware(
            RequestTimingMiddleware,
            slow_request_threshold=0.5,  # 超过2秒的请求记录为慢请求
            log_all_requests=False       # 生产环境建议设为False，只记录慢请求和错误请求
        )

        self.server_task = None
        self.shutdown_event = asyncio.Event()
        self.container = container
        self.base_url = "/chat-api"

        # 这里自定义认证
        container.register(
            AuthService,
            NovaAuthService(),
        )
        self.config = container.resolve(GlobalConfig)

        # 配置 hypercorn
        from hypercorn.logging import Logger

        self.hypercorn_config = Config()
        self.hypercorn_config._log = Logger(self.hypercorn_config)
        base_url = self.base_url

        # 创建自定义的日志包装器，添加 URL 过滤
        class FilteredLoggerWrapper(HypercornLoggerWrapper):
            def info(self, message, *args, **kwargs):
                # 过滤掉不需要记录的URL请求日志
                ignored_paths = [
                    f'{base_url}/api/system/status',  # 添加需要过滤的URL路径
                    '/favicon.ico',
                ]
                for path in ignored_paths:
                    if path in str(args):
                        return
                super().info(message, *args, **kwargs)

        # 使用新的过滤日志包装器
        self.hypercorn_config._log.access_logger = FilteredLoggerWrapper(logger) # type: ignore
        self.hypercorn_config._log.error_logger = HypercornLoggerWrapper(logger) # type: ignore

        # 扩展 Web API 应用，添加聊天接口
        extend_web_api_app(self.web_api_app, container)

        # 挂载 Web API 应用
        self.mount_app(self.base_url, self.web_api_app)

        

    def mount_app(self, prefix: str, app):
        """挂载子应用到指定路径前缀"""
        self.app.mount(prefix, app)

    def _check_port_available(self, host: str, port: int) -> bool:
        """检查端口是否可用"""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                # 设置端口复用选项
                s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                # 在某些系统上还需要设置 SO_REUSEPORT
                if hasattr(socket, 'SO_REUSEPORT'):
                    s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEPORT, 1)
                s.bind((host, port))
                return True
            except socket.error:
                return False

    async def start(self):
        """启动Web服务器"""

        # 确定最终使用的host和port
        if self.container.has("cli_args"):
            cli_args = self.container.resolve("cli_args")
            self.listen_host = cli_args.host or self.config.web.host
            self.listen_port = cli_args.port or self.config.web.port
        else:
            self.listen_host = self.config.web.host
            self.listen_port = self.config.web.port

        self.hypercorn_config.bind = [f"{self.listen_host}:{self.listen_port}"]

        # 检查端口是否被占用
        if not self._check_port_available(self.listen_host, self.listen_port):
            error_msg = f"端口 {self.listen_port} 已被占用，无法启动服务器，请修改端口或关闭其他占用端口的程序。"
            logger.error(error_msg)
            raise RuntimeError(error_msg)

        self.server_task = asyncio.create_task(serve(self.app, self.hypercorn_config, shutdown_trigger=self.shutdown_event.wait)) # type: ignore
        logger.info(f"监听地址：http://{self.listen_host}:{self.listen_port}/")
                
        # 检查WebUI是否存在，如果不存在则尝试自动安装
        # self._check_and_install_webui()
        
    async def stop(self):
        """停止Web服务器"""
        self.shutdown_event.set()

        if self.server_task:
            try:
                await asyncio.wait_for(self.server_task, timeout=3.0)
            except asyncio.TimeoutError:
                logger.warning("Server shutdown timed out after 3 seconds.")
            except asyncio.CancelledError:
                pass
            except Exception as e:
                logger.error(f"Error during server shutdown: {e}")

    def add_static_assets(self, url_path: str, local_path: str):
        """添加自定义静态资源"""
        if not os.path.exists(local_path):
            logger.warning(f"Static asset path does not exist: {local_path}")
            return

        custom_static_assets[url_path] = local_path