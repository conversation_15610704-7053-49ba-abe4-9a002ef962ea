"""
用户向导服务
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
import json

from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from kirara_ai.database.manager import DatabaseManager
from kirara_ai.logger import get_logger
from app.models.user_guide import UserGuideStep, UserGuideSettings

logger = get_logger("UserGuideService")


class UserGuideService:
    """用户向导服务类"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    def get_user_steps(self, user_id: str = 'default') -> Dict[str, bool]:
        """获取用户的向导步骤完成状态"""
        try:
            session = self.db_manager.get_session()
            try:
                steps = session.query(UserGuideStep).filter_by(user_id=user_id).all()
                return {step.step_key: step.completed for step in steps}
            finally:
                session.close()
        except Exception as e:
            logger.error(f"获取用户向导步骤失败: {e}")
            return {}
    
    def update_step(self, step_key: str, completed: bool, user_id: str = 'default', metadata: Optional[Dict] = None) -> bool:
        """更新向导步骤状态"""
        try:
            session = self.db_manager.get_session()
            try:
                # 查找现有步骤
                step = session.query(UserGuideStep).filter_by(
                    user_id=user_id, 
                    step_key=step_key
                ).first()
                
                if step:
                    # 更新现有步骤
                    step.completed = completed
                    step.completed_at = datetime.utcnow() if completed else None
                    step.updated_at = datetime.utcnow()
                    if metadata:
                        step.extra_data = json.dumps(metadata)
                else:
                    # 创建新步骤
                    step = UserGuideStep(
                        user_id=user_id,
                        step_key=step_key,
                        completed=completed,
                        completed_at=datetime.utcnow() if completed else None,
                        extra_data=json.dumps(metadata) if metadata else None
                    )
                    session.add(step)
                
                session.commit()
                logger.info(f"更新用户 {user_id} 的步骤 {step_key} 为 {completed}")
                return True
                
            except IntegrityError as e:
                session.rollback()
                logger.error(f"更新向导步骤时发生完整性错误: {e}")
                return False
            finally:
                session.close()
                
        except Exception as e:
            logger.error(f"更新向导步骤失败: {e}")
            return False
    
    def get_user_settings(self, user_id: str = 'default') -> Dict[str, Any]:
        """获取用户向导设置"""
        try:
            session = self.db_manager.get_session()
            try:
                settings = session.query(UserGuideSettings).filter_by(user_id=user_id).first()
                if settings:
                    return settings.to_dict()
                else:
                    # 返回默认设置
                    return {
                        'user_id': user_id,
                        'hide_guide': False,
                        'settings': None
                    }
            finally:
                session.close()
        except Exception as e:
            logger.error(f"获取用户向导设置失败: {e}")
            return {'user_id': user_id, 'hide_guide': False, 'settings': None}
    
    def update_user_settings(self, user_id: str = 'default', hide_guide: Optional[bool] = None, 
                           settings: Optional[Dict] = None) -> bool:
        """更新用户向导设置"""
        try:
            session = self.db_manager.get_session()
            try:
                # 查找现有设置
                user_settings = session.query(UserGuideSettings).filter_by(user_id=user_id).first()
                
                if user_settings:
                    # 更新现有设置
                    if hide_guide is not None:
                        user_settings.hide_guide = hide_guide
                    if settings is not None:
                        user_settings.settings = json.dumps(settings)
                    user_settings.updated_at = datetime.utcnow()
                else:
                    # 创建新设置
                    user_settings = UserGuideSettings(
                        user_id=user_id,
                        hide_guide=hide_guide if hide_guide is not None else False,
                        settings=json.dumps(settings) if settings else None
                    )
                    session.add(user_settings)
                
                session.commit()
                logger.info(f"更新用户 {user_id} 的向导设置")
                return True
                
            except IntegrityError as e:
                session.rollback()
                logger.error(f"更新用户向导设置时发生完整性错误: {e}")
                return False
            finally:
                session.close()
                
        except Exception as e:
            logger.error(f"更新用户向导设置失败: {e}")
            return False
    
    def get_all_steps(self, user_id: str = 'default') -> List[Dict[str, Any]]:
        """获取用户的所有向导步骤详细信息"""
        try:
            session = self.db_manager.get_session()
            try:
                steps = session.query(UserGuideStep).filter_by(user_id=user_id).all()
                return [step.to_dict() for step in steps]
            finally:
                session.close()
        except Exception as e:
            logger.error(f"获取用户所有向导步骤失败: {e}")
            return []
    
    def reset_user_guide(self, user_id: str = 'default') -> bool:
        """重置用户向导（删除所有步骤和设置）"""
        try:
            session = self.db_manager.get_session()
            try:
                # 删除所有步骤
                session.query(UserGuideStep).filter_by(user_id=user_id).delete()
                # 删除设置
                session.query(UserGuideSettings).filter_by(user_id=user_id).delete()
                
                session.commit()
                logger.info(f"重置用户 {user_id} 的向导")
                return True
                
            except Exception as e:
                session.rollback()
                logger.error(f"重置用户向导时发生错误: {e}")
                return False
            finally:
                session.close()
                
        except Exception as e:
            logger.error(f"重置用户向导失败: {e}")
            return False
