
import asyncio
from datetime import timedel<PERSON>
from pathlib import Path
from typing import Optional

from kirara_ai.web.auth.services import AuthService

from app.utils.oauth_client import oauth2_client


class NovaAuthService(AuthService):

    def is_first_time(self) -> bool:
        return False

    def save_password(self, password: str) -> None:
        pass

    def verify_password(self, password: str) -> bool:
        return False

    def create_access_token(self, expires_delta: Optional[timedelta] = None) -> str:
        return False

    def verify_token(self, token: str) -> bool:
        """
        同步版本的 verify_token，用于中间件调用
        """
        try:
            # 尝试获取当前事件循环
            try:
                asyncio.get_running_loop()
                # 如果已经在事件循环中，使用 ThreadPoolExecutor
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self._verify_token_async(token))
                    return future.result(timeout=10)
            except RuntimeError:
                # 没有运行的事件循环，直接运行
                return asyncio.run(self._verify_token_async(token))
        except Exception:
            return False

    async def _verify_token_async(self, token: str) -> bool:
        """
        异步版本的 verify_token
        """
        try:
            user_info = await oauth2_client.get_userinfo(token)
            return user_info is not None
        except Exception:
            return False
