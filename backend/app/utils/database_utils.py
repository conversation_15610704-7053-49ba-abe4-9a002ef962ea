"""
数据库兼容性工具函数
"""

from sqlalchemy import func
from sqlalchemy.engine import Engine


def get_date_trunc_hour(engine: Engine, column):
    """
    获取按小时截断的日期表达式，兼容 SQLite 和 PostgreSQL
    
    Args:
        engine: SQLAlchemy 引擎对象
        column: 日期时间列
    
    Returns:
        SQLAlchemy 表达式
    """
    if 'postgresql' in str(engine.url):
        # PostgreSQL 使用 date_trunc
        return func.date_trunc('hour', column)
    else:
        # SQLite 使用 strftime
        return func.strftime('%Y-%m-%d %H:00:00', column)


def get_date_trunc_day(engine: Engine, column):
    """
    获取按天截断的日期表达式，兼容 SQLite 和 PostgreSQL
    
    Args:
        engine: SQLAlchemy 引擎对象
        column: 日期时间列
    
    Returns:
        SQLAlchemy 表达式
    """
    if 'postgresql' in str(engine.url):
        # PostgreSQL 使用 date_trunc
        return func.date_trunc('day', column)
    else:
        # SQLite 使用 strftime
        return func.strftime('%Y-%m-%d', column)


def format_datetime_result(engine: Engine, datetime_value, format_type='hour'):
    """
    格式化数据库返回的日期时间结果
    
    Args:
        engine: SQLAlchemy 引擎对象
        datetime_value: 数据库返回的日期时间值
        format_type: 格式类型，'hour' 或 'day'
    
    Returns:
        格式化后的字符串
    """
    if not datetime_value:
        return ''
    
    if 'postgresql' in str(engine.url):
        # PostgreSQL 返回 timestamp 对象，需要格式化
        if format_type == 'hour':
            return datetime_value.strftime('%Y-%m-%d %H:00:00')
        elif format_type == 'day':
            return datetime_value.strftime('%Y-%m-%d')
        else:
            return str(datetime_value)
    else:
        # SQLite 返回字符串
        return str(datetime_value)


def is_postgresql(engine: Engine) -> bool:
    """
    检查是否为 PostgreSQL 数据库
    
    Args:
        engine: SQLAlchemy 引擎对象
    
    Returns:
        bool: 是否为 PostgreSQL
    """
    return 'postgresql' in str(engine.url)


def is_sqlite(engine: Engine) -> bool:
    """
    检查是否为 SQLite 数据库
    
    Args:
        engine: SQLAlchemy 引擎对象
    
    Returns:
        bool: 是否为 SQLite
    """
    return 'sqlite' in str(engine.url)


def get_database_type(engine: Engine) -> str:
    """
    获取数据库类型
    
    Args:
        engine: SQLAlchemy 引擎对象
    
    Returns:
        str: 数据库类型 ('postgresql', 'sqlite', 'mysql', 'unknown')
    """
    url_str = str(engine.url)
    if 'postgresql' in url_str:
        return 'postgresql'
    elif 'sqlite' in url_str:
        return 'sqlite'
    elif 'mysql' in url_str:
        return 'mysql'
    else:
        return 'unknown'
