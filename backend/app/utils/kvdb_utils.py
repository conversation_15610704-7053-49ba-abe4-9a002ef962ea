
import json
import time
import redis
import threading
from collections import UserDict
from kirara_ai.logger import get_logger
from app.config.env_settings import settings


_kv_redis_client =  redis.Redis(
        host=settings.KVDB_REDIS_HOST,
        port=settings.KVDB_REDIS_PORT,
        password=settings.KVDB_REDIS_PASSWORD,
        db=settings.KVDB_REDIS_DB,
        decode_responses=True
    )

def get_kvdb_redis_client()->redis.Redis:
    """获取Redis客户端"""
    return _kv_redis_client


# 移除了复杂的描述符类，使用UserDict简化实现


class KvDbDict(UserDict):
    """基于Redis Hash的分布式字典，继承UserDict简化实现"""

    def __init__(self, key, serializer=None, deserializer=None):
        super().__init__()
        self.redis_client = _kv_redis_client
        self.redis_key = f"kv_dict:{key}"
        self.local_version = 0
        self.logger = get_logger("KvDbDict")

        # 线程安全机制
        self._lock = threading.RLock()  # 可重入锁，支持同一线程多次获取
        self._syncing = threading.local()  # 线程本地存储，防止递归同步

        # 序列化/反序列化函数
        self.serializer = serializer or self._default_serializer
        self.deserializer = deserializer or self._default_deserializer

        # 初始化时从Redis加载数据
        self._load_from_redis()

    def _default_serializer(self, data):
        """默认序列化方法"""
        return json.dumps(data, ensure_ascii=False)

    def _default_deserializer(self, data_str):
        """默认反序列化方法"""
        return json.loads(data_str) if data_str else {}

    def _get_version(self) -> int:
        """获取Redis Hash中的版本号"""
        try:
            version_str = self.redis_client.hget(self.redis_key, "version")
            return int(version_str) if version_str else 0
        except Exception:
            return 0

    def _generate_new_version(self) -> int:
        """生成新版本号"""
        return int(time.time() * 1000)

    def _is_syncing(self) -> bool:
        """检查当前线程是否正在同步"""
        return getattr(self._syncing, 'active', False)

    def _set_syncing(self, value: bool):
        """设置当前线程的同步状态"""
        self._syncing.active = value

    # UserDict核心方法 - 线程安全实现
    def __getitem__(self, key):
        """读取操作：先检查版本，再获取数据"""
        with self._lock:
            self._check_and_sync()
            return self.data[key]

    def __setitem__(self, key, value):
        """写入操作：设置数据后同步到Redis"""
        with self._lock:
            self.data[key] = value
            self._save_to_redis()

    def __delitem__(self, key):
        """删除操作：删除数据后同步到Redis"""
        with self._lock:
            del self.data[key]
            self._save_to_redis()

    def __len__(self):
        """长度操作：先检查版本"""
        with self._lock:
            self._check_and_sync()
            return len(self.data)

    def __iter__(self):
        """迭代操作：先检查版本，返回数据快照的迭代器"""
        with self._lock:
            self._check_and_sync()
            # 返回数据快照的迭代器，避免迭代过程中数据被修改
            return iter(dict(self.data))

    def __contains__(self, key):
        """包含检查：先检查版本"""
        with self._lock:
            self._check_and_sync()
            return key in self.data

    # 辅助方法 - 线程安全实现
    def _load_from_redis(self):
        """从Redis Hash加载数据（线程安全）"""
        if self._is_syncing():
            return

        try:
            self._set_syncing(True)
            data_str = self.redis_client.hget(self.redis_key, "data")
            if data_str:
                data = self.deserializer(data_str)
                # 直接更新data属性，避免触发同步
                self.data.clear()
                self.data.update(data)
                self.local_version = self._get_version()
                self.logger.debug(f"从Redis Hash加载数据成功，版本: {self.local_version}")
        except Exception as e:
            self.logger.error(f"从Redis Hash加载数据失败: {e}")
        finally:
            self._set_syncing(False)

    def _save_to_redis(self):
        """保存数据到Redis Hash（线程安全）"""
        if self._is_syncing():
            return

        try:
            self._set_syncing(True)
            # 创建数据快照，避免序列化过程中数据被修改
            data_snapshot = dict(self.data)
            data_str = self.serializer(data_snapshot)
            new_version = self._generate_new_version()

            # 使用Hash的hset一次性设置data和version
            self.redis_client.hset(self.redis_key, mapping={
                "data": data_str,
                "version": str(new_version)
            })

            self.local_version = new_version
            self.logger.debug(f"保存数据到Redis Hash成功，版本: {new_version}")
        except Exception as e:
            self.logger.error(f"保存数据到Redis Hash失败: {e}")
        finally:
            self._set_syncing(False)

    def _check_and_sync(self):
        """检查版本并按需同步（线程安全）"""
        if self._is_syncing():
            return

        try:
            remote_version = self._get_version()
            if remote_version > self.local_version:
                self.logger.info(f"检测到版本更新: {self.local_version} -> {remote_version}")
                self._load_from_redis()
        except Exception as e:
            self.logger.error(f"版本检查失败: {e}")

    # 高级线程安全方法
    def atomic_update(self, func):
        """原子更新操作：在锁保护下执行更新函数"""
        with self._lock:
            self._check_and_sync()
            func(self.data)
            self._save_to_redis()

    def get_snapshot(self):
        """获取当前数据的线程安全快照"""
        with self._lock:
            self._check_and_sync()
            return dict(self.data)

    def bulk_update(self, updates):
        """批量更新操作，减少Redis同步次数"""
        with self._lock:
            self._check_and_sync()
            if isinstance(updates, dict):
                self.data.update(updates)
            else:
                # 支持可迭代的键值对
                for key, value in updates:
                    self.data[key] = value
            self._save_to_redis()

    def save(self):
        self.logger.info("正在保存")
        with self._lock:
            self._save_to_redis()

    def force_sync(self):
        """强制从Redis同步数据"""
        with self._lock:
            self.local_version = 0  # 重置版本，强制同步
            self._check_and_sync()


class KvDbData:
    """基于Redis Hash的分布式数据存储，专门用于存储已序列化的数据"""

    def __init__(self, key):
        self.redis_client = _kv_redis_client
        self.redis_key = f"kv_data:{key}"
        self.local_version = 0
        self.local_data = ""  # 存储序列化后的数据字符串
        self.logger = get_logger("KvDbData")

        # 读写锁机制
        self._rw_lock = threading.RLock()  # 可重入锁(并没有真正使用读写锁，简化考虑)
        self._syncing = threading.local()  # 线程本地存储，防止递归同步

        # 初始化时从Redis加载数据
        self._load_from_redis()

    def _get_version(self) -> int:
        """获取Redis Hash中的版本号"""
        try:
            version_str = self.redis_client.hget(self.redis_key, "version")
            return int(version_str) if version_str else 0
        except Exception:
            return 0

    def _generate_new_version(self) -> int:
        """生成新版本号"""
        return int(time.time() * 1000)

    def _is_syncing(self) -> bool:
        """检查当前线程是否正在同步"""
        return getattr(self._syncing, 'active', False)

    def _set_syncing(self, value: bool):
        """设置当前线程的同步状态"""
        self._syncing.active = value

    def _load_from_redis(self):
        """从Redis Hash加载数据（内部方法，不加锁）"""
        if self._is_syncing():
            return

        try:
            self._set_syncing(True)
            data_str = self.redis_client.hget(self.redis_key, "data")
            if data_str:
                self.local_data = data_str
                self.local_version = self._get_version()
                self.logger.debug(f"从Redis Hash加载数据成功，版本: {self.local_version}")
            else:
                self.local_data = ""
                self.local_version = 0
        except Exception as e:
            self.logger.error(f"从Redis Hash加载数据失败: {e}")
        finally:
            self._set_syncing(False)

    def _save_to_redis(self, data_str: str):
        """保存数据到Redis Hash（内部方法，不加锁）"""
        if self._is_syncing():
            return

        try:
            self._set_syncing(True)
            new_version = self._generate_new_version()

            # 使用Hash的hset一次性设置data和version
            self.redis_client.hset(self.redis_key, mapping={
                "data": data_str,
                "version": str(new_version)
            })

            self.local_data = data_str
            self.local_version = new_version
            self.logger.debug(f"保存数据到Redis Hash成功，版本: {new_version}")
        except Exception as e:
            self.logger.error(f"保存数据到Redis Hash失败: {e}")
        finally:
            self._set_syncing(False)

    def _check_and_sync(self)->bool:
        """检查版本并按需同步（内部方法，不加锁）"""
        if self._is_syncing():
            return False

        try:
            remote_version = self._get_version()
            if remote_version > self.local_version:
                self.logger.info(f"检测到版本更新: {self.local_version} -> {remote_version}")
                self._load_from_redis()
                return True
        except Exception as e:
            self.logger.error(f"版本检查失败: {e}")
            return False
        return False

    def read_data(self) -> str:
        """读取数据（加读锁）"""
        with self._rw_lock:
            self._check_and_sync()
            return self.local_data

    def write_data(self, data_str: str):
        """写入数据（加写锁）"""
        with self._rw_lock:
            self._save_to_redis(data_str)

    def sync_from_redis(self):
        """从Redis同步数据（加写锁）"""
        with self._rw_lock:
            self._check_and_sync()

    def get_version(self) -> int:
        """获取当前版本号"""
        with self._rw_lock:
            return self.local_version


"""
使用示例：

# KvDbDict - 字典接口
my_dict = KvDbDict("my_data")
my_dict["key1"] = "value1"  # 自动同步到Redis
print(my_dict["key1"])      # 自动检查版本并同步

# KvDbData - 原始数据接口
my_data = KvDbData("my_config")
my_data.write_data('{"key": "value"}')  # 写入已序列化的数据
data_str = my_data.read_data()          # 读取序列化的数据

核心特性：

KvDbDict（基于UserDict + 线程安全）：
1. 继承UserDict，只需重写6个核心方法
2. 所有字典操作自动通过核心方法实现
3. 读取操作自动检查版本并按需同步
4. 写入操作自动保存到Redis Hash
5. 支持自定义序列化/反序列化
6. 使用Redis Hash减少key数量，提高性能
7. 完全线程安全，支持多线程并发访问

KvDbData（原始数据存储）：
1. 专门用于存储已序列化的数据字符串
2. 提供读写锁机制，支持读写分离
3. 版本控制和自动同步
4. 适合需要精确控制序列化的场景
5. 更轻量级，性能更好

线程安全特性：
- 使用RLock（可重入锁）保护所有操作
- 线程本地存储防止递归同步
- 数据快照机制避免迭代和序列化时的竞态条件
- 读写锁支持，提高并发性能
"""
