from ryjx.iam.oauth2_client import OAuth2Client
from ryjx.iam.oidc_client import outer_to_inner
from ryjx.common.service_client import ServiceClient

from app.config import settings


_service_client = ServiceClient(
    base_url=settings.IAM_API_BASE_URL,
    consul_server=settings.SERVICE_DISCOVERY_URL
)

oauth2_client = OAuth2Client(
    client_id=settings.OIDC_CLIENT_ID,
    client_secret=settings.OIDC_CLIENT_SECRET,
    token_endpoint=outer_to_inner(settings.OIDC_TOKEN_URL),
    userinfo_endpoint=outer_to_inner(settings.OIDC_USERINFO_URL),
    service_client=_service_client
)