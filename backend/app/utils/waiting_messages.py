"""
等待提示消息配置
提供友好、专业的等待提示文案
"""

import random


class WaitingMessageConfig:
    """等待提示消息配置类"""

    # 通用等待提示消息
    GENERAL_MESSAGES = [
        "📚 正在查找相关知识，马上为您解答...",
        "🔍 正在搜索知识库，很快就好...",
        "📖 正在翻阅资料，请稍候...",
        # "🎓 正在从知识库中寻找答案...",
        "📋 正在整理相关信息，请等一下...",
        "📚 知识库检索中，请耐心等待...",
    ]


def get_waiting_message() -> str:
    """获取等待提示消息"""
    return random.choice(WaitingMessageConfig.GENERAL_MESSAGES)
