"""
原kirara-ai基于文件读写管理配置，只适用于单机环境。
分布式环境下改用数据库(Pg)管理配置。因为原框架写死了保存配置相关代码，这儿用monkey patch方式修改其行为。
"""
from kirara_ai.config.config_loader import ConfigLoader
from kirara_ai.config.global_config import GlobalConfig
from kirara_ai.logger import get_logger
from pydantic import BaseModel


logger = get_logger("ConfigLoaderPatch")


def apply_config_loader_patch():
    """应用 ConfigLoader 补丁, 把配置写入数据库和Redis"""
    try:
        # 替换 ConfigLoader 的方法

        def save_config_with_backup(config_path: str, config_object: BaseModel):
            """保存配置到Redis和数据库（加写锁）"""

            # 如果是NovaGlobalConfig实例，也支持
            from .nova_global_config import NovaGlobalConfig
            if isinstance(config_object, NovaGlobalConfig):
                # NovaGlobalConfig实例，获取其内部的GlobalConfig并保存
                config_object.save_config()
                logger.info(f"NovaGlobalConfig配置已保存: {config_path}")

                # 立即刷新KvDbData
                config_object.force_refresh()
                logger.info("KvDbData已刷新")
                return
            
            raise Exception(f"配置类型不正确，配置保存失败")

        ConfigLoader.save_config_with_backup = staticmethod(save_config_with_backup)

        logger.info("ConfigLoader 补丁已应用")
        logger.info("- save_config_with_backup: 将配置保存到Redis和数据库，并立即刷新KvDbData")
        logger.info("- 使用写锁确保数据一致性")

    except Exception as e:
        logger.error(f"应用 ConfigLoader 补丁失败: {e}")
