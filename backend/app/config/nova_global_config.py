"""
基于Redis的分布式GlobalConfig实现
解决多机部署时配置同步问题，支持实时读写
"""

import json
import threading
import time
from typing import Optional, ClassVar

from kirara_ai.database.manager import DatabaseManager
from kirara_ai.logger import get_logger
from kirara_ai.ioc.container import DependencyContainer
from kirara_ai.config.global_config import GlobalConfig

from app.config.database_config_manager import DatabaseConfigManager


class NovaGlobalConfig:
    """基于Redis Hash的分布式全局配置管理器"""


    # Redis Hash键名常量
    REDIS_CONFIG_KEY: ClassVar[str] = "global_config"

    # 支持的配置字段
    SUPPORTED_FIELDS: ClassVar[list] = [
        'ims', 'llms', 'defaults', 'memory', 'web',
        'plugins', 'update', 'frpc', 'system', 'tracing'
    ]

    def __init__(self, container: DependencyContainer):
        self.container = container
        self.logger = get_logger("NovaGlobalConfig")

        # 获取数据库管理器
        self.db_manager = container.resolve(DatabaseManager)
        self.config_manager = DatabaseConfigManager(self.db_manager)

        # 创建基于Redis Hash的分布式数据存储（延迟导入避免循环导入）
        from app.utils.kvdb_utils import KvDbData
        self.config_data = KvDbData(self.REDIS_CONFIG_KEY)

        # 读写锁（与KvDbData的锁分离，用于保护GlobalConfig实例）
        self._rw_lock = threading.RLock()

        # 缓存的GlobalConfig实例
        self._cached_config: Optional[GlobalConfig] = None
        self._cache_version = 0

        # 初始化配置数据
        self._load_initial_config()

        # 启动定期刷新线程
        self._start_refresh_daemon()

    def _serialize_config(self, config: GlobalConfig) -> str:
        """序列化GlobalConfig为JSON字符串"""
        try:
            config_data = {}
            for field_name in self.SUPPORTED_FIELDS:
                if hasattr(config, field_name):
                    field_value = getattr(config, field_name)
                    if field_value is not None:
                        config_data[field_name] = self._serialize_field_value(field_value)

            return json.dumps(config_data, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"序列化配置失败: {e}")
            return "{}"

    def _serialize_field_value(self, value):
        """递归序列化字段值"""
        try:
            # 处理Pydantic模型
            if hasattr(value, 'model_dump'):
                return value.model_dump()
            elif hasattr(value, 'dict'):
                return value.dict()
            # 处理字典
            elif isinstance(value, dict):
                return {k: self._serialize_field_value(v) for k, v in value.items()}
            # 处理列表
            elif isinstance(value, (list, tuple)):
                return [self._serialize_field_value(item) for item in value]
            # 处理基本类型
            elif isinstance(value, (str, int, float, bool, type(None))):
                return value
            # 处理其他对象，尝试转换为字典
            else:
                # 如果对象有__dict__属性，尝试序列化其属性
                if hasattr(value, '__dict__'):
                    return {k: self._serialize_field_value(v) for k, v in value.__dict__.items()
                           if not k.startswith('_')}
                # 否则转换为字符串
                else:
                    return str(value)
        except Exception as e:
            self.logger.warning(f"序列化字段值失败: {e}, 使用字符串表示")
            return str(value)

    def _deserialize_config(self, data_str: str) -> GlobalConfig:
        """反序列化JSON字符串为GlobalConfig"""
        try:
            if not data_str:
                return GlobalConfig()

            config_data = json.loads(data_str)
            return GlobalConfig(**config_data)
        except Exception as e:
            self.logger.error(f"反序列化配置失败: {e}")
            return GlobalConfig()

    def _load_initial_config(self):
        """初始化加载配置数据"""
        try:
            # 从Redis读取数据
            data_str = self.config_data.read_data()
            # self.logger.info(f"从redis获取到配置{data_str}")

            # 如果Redis中没有数据，从数据库加载
            if not data_str:
                db_data = self.config_manager.load_config()
                if db_data:
                    # 创建临时GlobalConfig并序列化到Redis
                    temp_config = GlobalConfig(**db_data)
                    serialized_data = self._serialize_config(temp_config)
                    self.config_data.write_data(serialized_data)
                    self.logger.info(f"从数据库初始化配置，共{len(db_data)}个字段")
                else:
                    # 如果数据库也没有数据，使用默认配置
                    default_config = GlobalConfig()
                    serialized_data = self._serialize_config(default_config)
                    self.config_data.write_data(serialized_data)
                    self.logger.info("使用默认配置初始化")
        except Exception as e:
            self.logger.error(f"初始化配置失败: {e}")

    def _get_current_config(self) -> GlobalConfig:
        """获取当前配置（加读锁）"""
        with self._rw_lock:
            # 检查缓存是否有效
            current_version = self.config_data.get_version()
            if self._cached_config is None or self._cache_version != current_version:
                # 从Redis读取最新数据
                data_str = self.config_data.read_data()
                self._cached_config = self._deserialize_config(data_str)
                self._cache_version = current_version
                self.logger.debug(f"刷新配置缓存，版本: {current_version}")

            return self._cached_config

    

    def get_config(self) -> GlobalConfig:
        """获取当前配置"""
        return self._get_current_config()

    def __getattr__(self, name):
        """访问配置属性时从KvDbData获取数据"""
        if name in self.SUPPORTED_FIELDS:
            config = self._get_current_config()
            if name=="ims":
                self.logger.info(f"IMS配置={config.ims}")
            return getattr(config, name)

        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

    def __setattr__(self, name, value):
        """设置属性"""
        # 对于内部属性，直接设置
        if name.startswith('_') or name in ['container', 'logger', 'db_manager', 'config_manager', 'config_data']:
            super().__setattr__(name, value)
            return

        if name in self.SUPPORTED_FIELDS:
            config = self._get_current_config()
            setattr(config, name, value)
            # 这里有问题，应用停止时，会关闭所有的im通道，最后导致数据库里的ims配置被清空
            # self._update_config(config)
        else:
            super().__setattr__(name, value)

    def _start_refresh_daemon(self):
        """启动定期刷新守护线程"""
        def refresh_worker():
            while True:
                try:
                    time.sleep(30)  # 每30秒检查一次
                    self.config_data.sync_from_redis()

                    # 清除缓存，强制下次访问时重新加载
                    with self._rw_lock:
                        self._cache_version = 0

                except Exception as e:
                    self.logger.error(f"定期刷新失败: {e}")

        daemon_thread = threading.Thread(target=refresh_worker, daemon=True)
        daemon_thread.start()
        self.logger.info("定期刷新守护线程已启动")

    def save_config(self):
        """保存完整配置"""
        self._update_config(self._get_current_config())
        self.logger.info("配置保存成功")

    def _update_config(self, config: GlobalConfig):
        """更新配置（加写锁）"""
        with self._rw_lock:
            # 序列化并写入Redis
            serialized_data = self._serialize_config(config)
            self.config_data.write_data(serialized_data)

            # 更新缓存
            self._cached_config = config
            self._cache_version = self.config_data.get_version()

            # 同步到数据库
            try:
                config_data = {}
                for field_name in self.SUPPORTED_FIELDS:
                    if hasattr(config, field_name):
                        field_value = getattr(config, field_name)
                        if field_value is not None:
                            config_data[field_name] = self._serialize_field_value(field_value)

                self.config_manager.save_config(config_data)
                self.logger.debug("配置已同步到数据库")
            except Exception as e:
                self.logger.error(f"同步配置到数据库失败: {e}")

    def force_refresh(self):
        """强制刷新配置"""
        with self._rw_lock:
            self.config_data.sync_from_redis()
            self._cache_version = 0
        self.logger.info("强制刷新配置完成")
