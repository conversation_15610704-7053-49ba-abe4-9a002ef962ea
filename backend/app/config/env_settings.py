"""
简化的环境配置系统
基于 pydantic-settings 和 dotenv 实现
"""

import os
from enum import Enum
from pathlib import Path
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Environment(str, Enum):
    """环境枚举"""
    LOCAL="local" # 本机
    DEV = "dev"   # 办公网开发
    BETA = "beta" # 线上beta
    PROD = "prod" # 线上正式



class AppSettings(BaseSettings):
    """
    应用主配置
    从.env 文件中读取配置，修改配置需要发版生效
    """
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )

    # 环境配置
    environment: Environment = Field(default=Environment.LOCAL, description="运行环境")

    # 数据库配置
    database_url: str = Field(description="数据库URL")

    # 默认知识库ID
    default_rag_repo_id: int = Field(description="默认知识库ID")

    # OAuth/OIDC 配置
    OIDC_CLIENT_ID: str
    OIDC_CLIENT_SECRET: str
    OIDC_USERINFO_URL: str
    OIDC_ISSUER: str
    OIDC_AUTHORIZATION_URL: str
    OIDC_REDIRECT_URL: str
    OIDC_LOGOUT_URL: str
    OIDC_INTROSPECTION_URL: str
    OIDC_JWKS_URL: str
    OIDC_TOKEN_URL: str

    # IAM 配置
    IAM_API_BASE_URL: str = Field(description="IAM API 基础URL")
    IAM_API_INTERNAL_HOST: str = Field(description="IAM API 内部主机")
    SERVICE_DISCOVERY_URL: str = Field(description="服务发现URL")

    # RAG 插件配置
    RAG_API_BASE_URL: str = Field(description="RAG API 基础URL")
    RAG_API_KEY: str = Field(description="RAG API 密钥")
    RAG_DEFAULT_USERKEY: str = Field(description="RAG 默认用户密钥")
    RAG_DEFAULT_USERNAME: str = Field(description="RAG 默认用户姓名")
    RAG_DEFAULT_MODEL_TYPE: str = Field(description="RAG 默认模型类型")
    RAG_REQUEST_TIMEOUT: int = Field(description="RAG 请求超时时间（秒）")
    RAG_MAX_RETRIES: int = Field(description="RAG 最大重试次数")
    RAG_DEBUG: bool = Field(description="RAG 调试模式")
    IM_SEND_MSG_WORKFLOW_ID: str = Field(description="通用消息发送工作流ID")

    # LLM 分发插件配置
    LLM_DISPATCH_ENABLED: bool = Field(description="是否启用LLM分发")
    LLM_DISPATCH_DEFAULT_MODEL_ID: str = Field(description="LLM分发默认模型ID")
    LLM_DISPATCH_CONFIDENCE_THRESHOLD: float = Field(description="LLM分发置信度阈值")
    LLM_DISPATCH_CACHE_ENABLED: bool = Field(description="LLM分发缓存启用")
    LLM_DISPATCH_CACHE_DURATION: int = Field(description="LLM分发缓存时长（秒）")
    LLM_DISPATCH_TIMEOUT: int = Field(description="LLM分发超时时间（秒）")
    LLM_DISPATCH_DEBUG: bool = Field(description="LLM分发调试模式")

    # 天气插件配置
    AMAP_API_KEY: str = Field(description="高德地图API密钥")
    WEATHER_API_BASE_URL: str = Field(description="天气API基础URL")
    WEATHER_TIMEOUT: int = Field(description="天气请求超时时间（秒）")
    WEATHER_EXTENSIONS: str = Field(description="天气信息扩展")

    # Nova企微插件配置
    NOVA_WECOM_ENABLED: bool = Field(description="是否启用Nova企微插件")
    NOVA_WECOM_APP_ID: str = Field(description="企业微信应用ID")
    NOVA_WECOM_SECRET: str = Field(description="企业微信应用Secret")
    NOVA_WECOM_TOKEN: str = Field(description="企业微信Token")
    NOVA_WECOM_ENCODING_AES_KEY: str = Field(description="企业微信EncodingAESKey")
    NOVA_WECOM_CORP_ID: Optional[str] = Field(description="企业ID（企业微信必填）")
    NOVA_WECOM_WEBHOOK_URL: str = Field(description="Webhook回调地址")
    NOVA_WECOM_DEBUG: bool = Field(description="Nova企微调试模式")
    WEATHER_OUTPUT: str = Field(description="天气返回格式")
    WEATHER_CACHE_ENABLED: bool = Field(description="天气缓存启用")
    WEATHER_CACHE_DURATION: int = Field(description="天气缓存时长（秒）")

    # 版本更新配置
    UPDATE_CHECK_ENABLED: bool = Field(description="是否启用版本更新检查")



    # 权限系统配置
    PERMISSION_ROOT: str
    PERMISSION_APP: str
    PERMISSION_ALL: str
    
    # IAM中心接口配置
    IAM_API_BASE_URL: str
    IAM_API_OUTER_HOST: str
    IAM_API_INTERNAL_HOST : str
    SERVICE_DISCOVERY_URL: str

    # REDIS持久化配置
    KVDB_REDIS_HOST:str
    KVDB_REDIS_PORT:int
    KVDB_REDIS_PASSWORD: Optional[str]
    KVDB_REDIS_DB: int

    # REDIS缓存配置
    CACHE_REDIS_HOST:str
    CACHE_REDIS_PORT:int
    CACHE_REDIS_PASSWORD: Optional[str]
    CACHE_REDIS_DB: int



def get_settings(env_file: Optional[str] = None) -> AppSettings:
    """
    获取应用配置
    
    Args:
        env_file: 环境文件路径，如果不指定则自动检测
    
    Returns:
        AppSettings: 应用配置实例
    """
    # 如果没有指定环境文件，则根据环境变量自动检测
    if env_file is None:
        environment = os.getenv("APP_ENV", Environment.LOCAL.value)
        base_path = Path(__file__).parent.parent.parent / "config"
        
        # 尝试加载特定环境的配置文件
        env_files = [
            base_path / f".env.{environment}",
            base_path / ".env"
        ]
        
        for env_path in env_files:
            if env_path.exists():
                env_file = str(env_path)
                break
    
    # 创建配置实例
    if not env_file:
        raise Exception(f"环境文件不存在, 需保证至少存在一项：{env_files}")
    return AppSettings(_env_file=env_file)


# 全局配置实例
settings = get_settings()
