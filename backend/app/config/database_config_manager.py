"""
简化的数据库配置管理器 - 使用 app_configs 表存储 GlobalConfig 配置
每个 config_field 对应一条数据库记录，数据以 JSON 格式存储
"""

from typing import Dict, Any, Optional
import json
from datetime import datetime
from collections import UserDict

from sqlalchemy import Column, Integer, String, DateTime, Text
import sqlalchemy as sa

from kirara_ai.database.manager import DatabaseManager, Base
from kirara_ai.logger import get_logger
from kirara_ai.config.global_config import GlobalConfig

logger = get_logger("DatabaseConfigManager")


class AppConfig(Base):
    """应用配置模型 - 每个配置字段对应一条数据库记录"""
    __tablename__ = 'app_configs'

    id = Column(Integer, primary_key=True, autoincrement=True)
    config_field = Column(String(100), nullable=False, unique=True)  # 配置字段名，如 'ims', 'llms', 'web' 等
    config_data = Column(Text, nullable=True)  # JSON格式存储配置数据
    description = Column(String(500), nullable=True)  # 配置描述
    created_at = Column(DateTime(timezone=True), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP'))
    updated_at = Column(DateTime(timezone=True), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP'), onupdate=datetime.now)

    def __repr__(self):
        return f"<AppConfig(field='{self.config_field}')>"

    def get_data(self) -> Any:
        """获取配置数据"""
        if not self.config_data:
            return None

        try:
            return json.loads(self.config_data)
        except json.JSONDecodeError as e:
            logger.warning(f"解析配置数据失败 {self.config_field}: {e}")
            return None

    def set_data(self, data: Any):
        """设置配置数据"""
        if data is None:
            self.config_data = None
        else:
            self.config_data = json.dumps(data, ensure_ascii=False, indent=2)
        self.updated_at = datetime.now()


class DatabaseConfigManager:
    """简化的数据库配置管理器"""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self._ensure_table_exists()

    def _ensure_table_exists(self):
        """确保配置表存在"""
        try:
            AppConfig.metadata.create_all(self.db_manager.engine)
        except Exception as e:
            logger.error(f"创建配置表失败: {e}")

    def save_config_field(self, field_name: str, field_data: Any, description: str = None) -> bool:
        """保存单个配置字段到数据库"""
        try:
            session = self.db_manager.get_session()
            try:
                # 查找或创建配置记录
                config_record = session.query(AppConfig).filter_by(config_field=field_name).first()
                if not config_record:
                    config_record = AppConfig(config_field=field_name)
                    session.add(config_record)

                # 设置数据和描述
                config_record.set_data(field_data)
                if description:
                    config_record.description = description

                session.commit()
                logger.info(f"配置字段保存成功: {field_name}")
                return True

            except Exception as e:
                session.rollback()
                logger.error(f"保存配置字段失败 {field_name}: {e}")
                return False
            finally:
                session.close()

        except Exception as e:
            logger.error(f"保存配置字段失败 {field_name}: {e}")
            return False

    def save_config(self, config_dict: Dict[str, Any]) -> bool:
        """保存完整配置到数据库"""
        try:
            if not isinstance(config_dict, UserDict|dict):
                logger.error(f"配置必须是字典类型，当前类型: {type(config_dict)}")
                return False

            success_count = 0
            total_count = len(config_dict)

            for field_name, field_data in config_dict.items():
                if self.save_config_field(field_name, field_data):
                    success_count += 1

            logger.info(f"配置保存完成: {success_count}/{total_count} 个字段成功")
            return success_count == total_count

        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            return False

    def load_config_field(self, field_name: str) -> Any:
        """从数据库加载单个配置字段"""
        try:
            session = self.db_manager.get_session()
            try:
                config_record = session.query(AppConfig).filter_by(config_field=field_name).first()
                if config_record:
                    return config_record.get_data()
                return None

            finally:
                session.close()

        except Exception as e:
            logger.error(f"加载配置字段失败 {field_name}: {e}")
            return None

    def load_config(self) -> Dict[str, Any]:
        """从数据库加载完整配置"""
        try:
            session = self.db_manager.get_session()
            try:
                records = session.query(AppConfig).all()
                config = {}

                for record in records:
                    field_data = record.get_data()
                    if field_data is not None:
                        config[record.config_field] = field_data

                logger.info(f"配置加载成功，共 {len(records)} 个字段")
                return config

            finally:
                session.close()

        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            return {}
    