"""
分发规则数据库管理器
将分发规则从 YAML 文件存储改为数据库存储
"""

from typing import List, Dict, Any

from kirara_ai.logger import get_logger
from kirara_ai.database.manager import DatabaseManager
from kirara_ai.workflow.core.dispatch.models.dispatch_rules import CombinedDispatchRule

from .database_config_manager import DatabaseConfigManager

logger = get_logger("DispatchRulesManager")


class DispatchRulesManager:
    """分发规则数据库管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.config_manager = DatabaseConfigManager(db_manager)
        self.config_field = "dispatch_rules"
    
    def save_rules(self, rules: List[CombinedDispatchRule]) -> bool:
        """保存分发规则到数据库"""
        try:
            # 将规则对象转换为字典列表
            rules_data = []
            for rule in rules:
                if hasattr(rule, 'model_dump'):
                    # Pydantic 模型
                    rule_dict = rule.model_dump()
                elif hasattr(rule, 'dict'):
                    # Pydantic v1 模型
                    rule_dict = rule.dict()
                else:
                    # 普通对象，尝试转换为字典
                    rule_dict = self._rule_to_dict(rule)
                
                rules_data.append(rule_dict)
            
            # 保存到数据库
            success = self.config_manager.save_config_field(
                self.config_field, 
                rules_data, 
                "分发规则配置"
            )
            
            if success:
                logger.info(f"分发规则保存成功，共 {len(rules_data)} 条规则")
            else:
                logger.error("分发规则保存失败")
            
            return success
            
        except Exception as e:
            logger.error(f"保存分发规则失败: {e}")
            return False
    
    def load_rules(self) -> List[Dict[str, Any]]:
        """从数据库加载分发规则"""
        try:
            rules_data = self.config_manager.load_config_field(self.config_field)
            
            if not rules_data:
                logger.info("数据库中没有分发规则，返回空列表")
                return []
            
            if not isinstance(rules_data, list):
                logger.warning(f"分发规则数据格式错误，期望列表，实际: {type(rules_data)}")
                return []
            
            logger.info(f"从数据库加载分发规则成功，共 {len(rules_data)} 条规则")
            return rules_data
            
        except Exception as e:
            logger.error(f"加载分发规则失败: {e}")
            return []
    
    
    
    def _rule_to_dict(self, rule: CombinedDispatchRule) -> Dict[str, Any]:
        """将规则对象转换为字典"""
        try:
            # 尝试获取规则的基本属性
            rule_dict = {}
            
            # 基本属性
            for attr in ['rule_id', 'name', 'description', 'workflow_id', 'priority', 'enabled', 'metadata']:
                if hasattr(rule, attr):
                    rule_dict[attr] = getattr(rule, attr)
            
            # 规则组
            if hasattr(rule, 'rule_groups'):
                rule_groups = getattr(rule, 'rule_groups')
                if rule_groups:
                    rule_dict['rule_groups'] = []
                    for group in rule_groups:
                        if hasattr(group, 'model_dump'):
                            rule_dict['rule_groups'].append(group.model_dump())
                        elif hasattr(group, 'dict'):
                            rule_dict['rule_groups'].append(group.dict())
                        else:
                            # 尝试手动转换
                            group_dict = {}
                            for group_attr in ['operator', 'rules']:
                                if hasattr(group, group_attr):
                                    group_dict[group_attr] = getattr(group, group_attr)
                            rule_dict['rule_groups'].append(group_dict)
            
            return rule_dict
            
        except Exception as e:
            logger.error(f"转换规则对象为字典失败: {e}")
            return {}

