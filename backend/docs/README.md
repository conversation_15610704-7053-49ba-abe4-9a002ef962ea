# AI Chat Backend

基于 kirara-ai 的 AI 聊天机器人后端服务

## 功能特性

- 🤖 **复用 kirara-ai 组件**: 直接使用 kirara-ai 的企业微信通道和 DeepSeek 模型
- 💬 **企业微信集成**: 支持企业微信消息接收和发送
- 🧠 **DeepSeek AI**: 集成 DeepSeek 大语言模型
- 📚 **RAG 系统**: 自建知识库和文档检索
- 🔌 **多目录插件支持**: 支持 kirara-ai 内置插件和项目自定义插件
- 🗄️ **数据库支持**: PostgreSQL 数据存储
- 🔄 **会话管理**: 完整的聊天会话历史记录
- 🌐 **RESTful API**: 提供完整的 HTTP API 接口

## 技术架构

- **Web 框架**: FastAPI
- **AI 引擎**: kirara-ai
- **数据库**: PostgreSQL + SQLAlchemy
- **缓存**: Redis
- **大模型**: DeepSeek
- **向量数据库**: ChromaDB
- **企业微信**: wechatpy

## 快速开始

### 1. 安装依赖

**推荐方法：先安装 kirara-ai**
```bash
cd backend
pip install kirara-ai
pip install -r requirements.txt
```

**或者使用自动安装脚本**
```bash
cd backend
python setup.py
```

### 2. 配置环境变量

```bash
cp .env.example .env
# 编辑 .env 文件，填入你的配置
```

### 3. 配置数据库

确保 PostgreSQL 和 Redis 服务正在运行，然后创建数据库：

```sql
CREATE DATABASE ai_chat;
```

### 4. 配置 kirara-ai

编辑 `config/kirara_config.yaml` 文件，配置你的企业微信和 DeepSeek 参数。

### 5. 启动服务

```bash
python run.py
```

或者使用 uvicorn：

```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

## API 文档

启动服务后，访问以下地址查看 API 文档：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 主要 API 接口

### 聊天接口

- `POST /api/v1/chat/send` - 发送聊天消息
- `GET /api/v1/chat/history/{session_id}` - 获取会话历史
- `DELETE /api/v1/chat/session/{session_id}` - 清除会话
- `GET /api/v1/chat/sessions` - 列出所有会话

### 企业微信接口

- `GET /api/v1/wechat/callback` - 企业微信回调验证
- `POST /api/v1/wechat/callback` - 企业微信消息回调
- `POST /api/v1/wechat/send` - 主动发送企业微信消息
- `GET /api/v1/wechat/users` - 获取企业微信用户列表

### 知识库接口

- `POST /api/v1/knowledge/documents` - 添加文档
- `POST /api/v1/knowledge/documents/upload` - 上传文档文件
- `POST /api/v1/knowledge/search` - 搜索知识库
- `GET /api/v1/knowledge/documents` - 列出文档
- `GET /api/v1/knowledge/documents/{doc_id}` - 获取文档详情
- `PUT /api/v1/knowledge/documents/{doc_id}` - 更新文档
- `DELETE /api/v1/knowledge/documents/{doc_id}` - 删除文档

## 配置说明

### 环境变量

主要环境变量说明：

- `WECHAT_CORP_ID`: 企业微信 Corp ID
- `WECHAT_AGENT_ID`: 企业微信应用 Agent ID
- `WECHAT_SECRET`: 企业微信应用密钥
- `WECHAT_TOKEN`: 企业微信回调验证 Token
- `WECHAT_ENCODING_AES_KEY`: 企业微信回调加密密钥
- `DEEPSEEK_API_KEY`: DeepSeek API Key
- `DATABASE_URL`: PostgreSQL 数据库连接 URL
- `REDIS_URL`: Redis 连接 URL

### Kirara AI 配置

`config/kirara_config.yaml` 文件包含了 kirara-ai 的完整配置，包括：

- 模型配置（DeepSeek）
- 平台配置（企业微信）
- 插件配置（RAG）
- 工作流配置
- 安全配置

## 🔌 插件开发

### 插件目录结构

系统支持多个插件目录：
1. `kirara_ai/plugins/` - kirara-ai 内置插件
2. `root_dir/plugins/` - 项目自定义插件
3. `app/services/plugins/` - 服务层插件（兼容）

### 创建自定义插件

在 `plugins/` 目录下创建插件文件：

```python
# plugins/my_plugin.py
from kirara_ai.plugin_manager.plugin import Plugin
from kirara_ai.logger import get_logger

class MyPlugin(Plugin):
    def __init__(self):
        self.logger = get_logger("MyPlugin")

    def on_load(self):
        self.logger.info("插件加载完成")

    def on_start(self):
        self.logger.info("插件启动完成")

    def on_stop(self):
        self.logger.info("插件停止")
```

### 测试插件加载

```bash
python test_plugin_loader.py
```

## 部署

### Docker 部署

```bash
# 构建镜像
docker build -t ai-chat-backend .

# 运行容器
docker run -d \
  --name ai-chat-backend \
  -p 8000:8000 \
  --env-file .env \
  ai-chat-backend
```

### 生产环境

1. 使用 Gunicorn 或 uWSGI 作为 WSGI 服务器
2. 配置 Nginx 作为反向代理
3. 使用 PostgreSQL 和 Redis 的生产环境配置
4. 配置日志轮转和监控

## 开发

### 项目结构

```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI 主应用
│   ├── config.py            # 配置管理
│   ├── database.py          # 数据库模型
│   ├── api/                 # API 路由
│   │   ├── chat.py          # 聊天接口
│   │   ├── wechat.py        # 企业微信接口
│   │   └── knowledge.py     # 知识库接口
│   └── services/            # 业务服务
│       ├── kirara_service.py # Kirara AI 服务封装
│       └── rag_service.py    # RAG 系统服务
├── config/
│   └── kirara_config.yaml   # Kirara AI 配置
├── requirements.txt         # Python 依赖
├── .env.example            # 环境变量示例
├── run.py                  # 启动脚本
└── README.md
```

### 添加新功能

1. 在 `app/services/` 中添加新的服务类
2. 在 `app/api/` 中添加新的 API 路由
3. 在 `app/main.py` 中注册新的路由
4. 更新数据库模型（如需要）

## 故障排除

### 常见问题

1. **kirara-ai 导入失败**
   - 确保已安装 kirara-ai: `pip install kirara-ai`

2. **企业微信回调验证失败**
   - 检查 Token 和 EncodingAESKey 配置
   - 确保回调 URL 可以从外网访问

3. **DeepSeek API 调用失败**
   - 检查 API Key 是否正确
   - 确认网络连接正常

4. **数据库连接失败**
   - 检查 PostgreSQL 服务是否运行
   - 确认数据库连接字符串正确

### 日志

应用日志保存在 `./logs/app.log` 文件中，可以通过以下方式查看：

```bash
tail -f ./logs/app.log
```

## 许可证

本项目基于 MIT 许可证开源。
