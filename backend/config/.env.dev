# 基础配置
ENVIRONMENT=dev

# 数据库配置
DATABASE_URL=*********************************************/ai_chat2


OIDC_CLIENT_ID=a96b9fc719c64cb283d362db7928b5ef
OIDC_CLIENT_SECRET=0zA9sVj9ktv9mTWcg9k9BG44cBwyqeIH
OIDC_TOKEN_URL=http://sso.dev.20382038.xyz/api/v1/oauth/token
OIDC_USERINFO_URL=http://sso.dev.20382038.xyz/api/v1/oauth/userinfo
OIDC_ISSUER=http://sso.dev.20382038.xyz
OIDC_AUTHORIZATION_URL=http://sso.dev.20382038.xyz/oauth/authorize
OIDC_REDIRECT_URL=http://localhost:5173/chat/callback
OIDC_LOGOUT_URL=http://sso.dev.20382038.xyz/oauth/logout
OIDC_INTROSPECTION_URL=http://sso.dev.20382038.xyz/api/v1/oauth/introspect
OIDC_JWKS_URL=http://sso.dev.20382038.xyz/api/v1/oauth/.well-known/jwks.json


# 权限系统配置
PERMISSION_ROOT=ryjx
PERMISSION_APP=chat
PERMISSION_ALL=*

# IAM中心接口配置
IAM_API_BASE_URL=http://sso.dev.20382038.xyz
IAM_API_HOST_URL=http://sso.dev.20382038.xyz
IAM_API_INTERNAL_HOST=http://sso.dev.20382038.xyz
IAM_API_OUTER_HOST=http://sso.dev.20382038.xyz
SERVICE_DISCOVERY_URL=

# RAG 插件配置
RAG_API_BASE_URL=https://dev-rag-nova.jx.ruyi.cn
RAG_API_KEY=pk-7MG5s5oSihnbVfs3AJjmrA:sk-Mi6mY84owF4Hn7HR6yFKS6
RAG_DEFAULT_USERKEY=nova_chat_bot_dev
RAG_DEFAULT_USERNAME=Nova聊天机器人Dev
RAG_DEFAULT_MODEL_TYPE=ccp
RAG_REQUEST_TIMEOUT=30
RAG_MAX_RETRIES=3
RAG_DEBUG=false
# 默认工作流 repo id
DEFAULT_RAG_REPO_ID=1018
# 通用消息发送工作流ID
IM_SEND_MSG_WORKFLOW_ID="im:send_msg"

# 版本更新配置
UPDATE_CHECK_ENABLED=false

# LLM 分发插件配置
LLM_DISPATCH_ENABLED=true
LLM_DISPATCH_DEFAULT_MODEL_ID=deepseek-chat
LLM_DISPATCH_CONFIDENCE_THRESHOLD=0.7
LLM_DISPATCH_CACHE_ENABLED=false
LLM_DISPATCH_CACHE_DURATION=300
LLM_DISPATCH_TIMEOUT=10
LLM_DISPATCH_DEBUG=false

# 天气插件配置
AMAP_API_KEY=48b136f258e235923e6ea97556fa77b7
WEATHER_API_BASE_URL=https://restapi.amap.com/v3
WEATHER_TIMEOUT=10
WEATHER_EXTENSIONS=all
WEATHER_OUTPUT=json
WEATHER_CACHE_ENABLED=false
WEATHER_CACHE_DURATION=600

# Nova企微插件配置
NOVA_WECOM_ENABLED=false
NOVA_WECOM_APP_ID=""
NOVA_WECOM_SECRET=""
NOVA_WECOM_TOKEN=""
NOVA_WECOM_ENCODING_AES_KEY=""
NOVA_WECOM_CORP_ID=""
NOVA_WECOM_WEBHOOK_URL="/im/webhook/nova_wecom"
NOVA_WECOM_DEBUG=false

# REDIS持久化
KVDB_REDIS_HOST=*************
KVDB_REDIS_PORT=6379
KVDB_REDIS_PASSWORD=
KVDB_REDIS_DB=0

# REDIS缓存
CACHE_REDIS_HOST=*************
CACHE_REDIS_PORT=6379
CACHE_REDIS_PASSWORD=
CACHE_REDIS_DB=0