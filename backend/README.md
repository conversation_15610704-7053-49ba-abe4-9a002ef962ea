# AI Chat Backend

基于 kirara-ai 的 AI 聊天后端服务。

## 🚀 快速开始

```bash
# 1. 激活 conda 环境
conda activate .conda

# 2. 安装依赖
pip install -r requirements.txt

# 3. 启动服务
python run.py
```

## 📚 文档

详细文档请查看 [`docs/`](docs/) 目录：

- [**快速开始指南**](docs/QUICKSTART.md) - 详细的安装和配置指南
- [**API 接口文档**](docs/API_DOCUMENTATION.md) - 完整的 API 接口说明
- [**项目结构文档**](docs/PROJECT_STRUCTURE.md) - 项目架构和代码组织
- [**配置管理指南**](docs/CONFIG_MANAGEMENT.md) - 多环境配置管理
- [**Monkey Patch 指南**](docs/MONKEY_PATCH_GUIDE.md) - 动态配置技术详解

## 🧪 测试

```bash
# 运行所有测试
python run_tests.py

# 运行指定测试
python run_tests.py --test chat_api

# 列出所有测试
python run_tests.py --list
```

## 🔧 主要功能

- ✅ **AI 聊天接口** - 基于 DeepSeek 模型的智能对话
- ✅ **会话管理** - 完整的聊天会话生命周期管理
- ✅ **RESTful API** - 标准的 HTTP API 接口
- ✅ **配置灵活** - 支持多环境动态配置
- ✅ **插件系统** - 可扩展的插件架构
- ✅ **测试完整** - 全面的测试覆盖

## 📁 项目结构

```
backend/
├── app/                    # 应用代码
│   ├── api/               # API 接口
│   ├── services/          # 服务层
│   ├── utils/             # 工具模块
│   └── plugins/           # 插件目录
├── config/                # 配置文件
├── docs/                  # 文档目录
├── tests/                 # 测试文件
├── run.py                 # 启动脚本
└── run_tests.py          # 测试运行器
```

## 🌐 服务地址

- **后端 API**: http://localhost:8080/chat-api
- **管理界面**: http://localhost:8080
- **健康检查**: http://localhost:8080/chat-api/health

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
