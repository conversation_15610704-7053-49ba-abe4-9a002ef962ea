#!/bin/bash

# 检查 .gitignore 配置是否正确工作

echo "🔍 检查 .gitignore 配置..."

# 检查是否存在 .gitignore 文件
if [ ! -f ".gitignore" ]; then
    echo "❌ .gitignore 文件不存在"
    exit 1
fi

echo "✅ .gitignore 文件存在"

# 检查一些应该被忽略的文件/目录
echo ""
echo "📋 检查忽略规则..."

# 定义应该被忽略的路径
ignored_paths=(
    "data/config.yaml"
    "logs/log_2025-07-10.log"
    "backend/data"
    "backend/logs"
    ".conda"
    "node_modules"
    "__pycache__"
    ".env"
    "*.pyc"
    "*.log"
)

# 检查每个路径
for path in "${ignored_paths[@]}"; do
    if git check-ignore "$path" >/dev/null 2>&1; then
        echo "✅ $path - 已忽略"
    else
        # 检查文件是否存在
        if [ -e "$path" ] || [ -d "$path" ]; then
            echo "⚠️  $path - 存在但未被忽略"
        else
            echo "ℹ️  $path - 不存在"
        fi
    fi
done

echo ""
echo "📊 Git 状态摘要:"
echo "未跟踪文件数量: $(git status --porcelain | grep '^??' | wc -l)"
echo "已忽略文件数量: $(git status --ignored --porcelain | grep '^!!' | wc -l)"

echo ""
echo "🎯 建议:"
echo "1. 确保敏感配置文件被正确忽略"
echo "2. 定期检查是否有新的文件类型需要忽略"
echo "3. 避免提交大型数据文件或构建产物"

echo ""
echo "✅ .gitignore 检查完成"
