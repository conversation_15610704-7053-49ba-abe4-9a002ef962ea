#!/bin/bash
# 前端构建脚本 - 在同步前执行

# 严格模式：遇到错误立即退出
set -e

ENV=${APP_ENV:-"dev"}
# 定义变量
FRONTEND_DIR="frontend"

LOG_FILE="/tmp/ai-chat-frontend-build.log"
START_TIME=$(date +%s)
ORIGINAL_DIR=$(pwd)

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 执行命令并检查结果
run_cmd() {
    local cmd="$1"
    local desc="$2"
    
    log "开始: $desc"
    if eval "$cmd"; then
        log "完成: $desc"
        return 0
    else
        log "错误: $desc 失败，退出码: $?"
        return 1
    fi
}

# 构建前端项目
build_frontend() {
    local dir="$1"
    local name="$2"
    
    log "=== 开始构建 $name ==="
    
    # 保存当前目录
    local current_dir=$(pwd)
    
    # 检查目录是否存在
    if [ ! -d "$dir" ]; then
        log "错误: 目录 '$dir' 不存在"
        return 1
    fi
    
    # 进入目录
    log "进入目录: $dir"
    cd "$dir" || {
        log "错误: 无法进入目录 $dir"
        return 1
    }
    
    # 安装依赖
    if ! run_cmd "APP_ENV=$ENV npm install" "安装 $name 依赖"; then
        log "错误: 安装 $name 依赖失败"
        cd "$current_dir"  # 确保返回原目录
        return 1
    fi
    
    # 构建项目
    if ! run_cmd "APP_ENV=$ENV npm run build" "构建 $name 项目"; then
        log "错误: 构建 $name 项目失败"
        cd "$current_dir"  # 确保返回原目录
        return 1
    fi
    
    # 返回原目录
    cd "$current_dir"
    log "=== $name 构建完成 ==="
    return 0
}

# 初始化
log "=== 开始前端构建过程 ==="

# 构建用户前端
if ! build_frontend "$FRONTEND_DIR" "用户前端"; then
    log "错误: 用户前端构建失败"
    exit 1
fi


# 计算执行时间
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))
log "=== 所有前端构建完成，耗时: ${DURATION}秒 ==="

exit 0