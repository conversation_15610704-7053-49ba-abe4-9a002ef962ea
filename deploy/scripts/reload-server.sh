#!/bin/bash
# 部署脚本 - 用于设置和部署项目

# 严格模式：遇到错误立即退出
set -e

# 定义变量
PROJECT_NAME="chat"
ENV=${APP_ENV:-"beta"}
PYTHON_VERSION=3.11.13
BASE_PROJECT_NAME="chat"
PIP_MIRROR="https://mirrors.cloud.tencent.com/pypi/simple"

# 输出环境信息
echo "当前部署环境: ${ENV}"

# 目录结构
VENV_DIR="/opt/venv/${BASE_PROJECT_NAME}"
PROJECT_DIR="/opt/works/${BASE_PROJECT_NAME}"
BACKEND_DIR="${PROJECT_DIR}/backend"
RUN_DIR="/opt/run/${BASE_PROJECT_NAME}"
LOG_DIR="/opt/logs/${BASE_PROJECT_NAME}"
SUPERVISOR_DIR="${PROJECT_DIR}/deploy/supervisor"

export UV_PYTHON_INSTALL_MIRROR="https://gh-proxy.com/github.com/indygreg/python-build-standalone/releases/download"

# 日志函数
log() {
    local log_file=$1
    shift
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*" | tee -a "$log_file"
}

# 检查目录是否存在，不存在则创建
check_dir() {
    if [ ! -d "$1" ]; then
        mkdir -p "$1"
        log "$2" "创建目录: $1"
    fi
}

# 执行命令并检查结果
run_cmd() {
    local cmd="$1"
    local error_msg="$2"
    local log_file="$3"
    
    if ! eval "$cmd"; then
        log "$log_file" "错误: $error_msg"
        exit 1
    fi
}

# 创建虚拟环境并安装依赖
setup_environment() {
    # 确保日志目录存在
    check_dir "${LOG_DIR}" "${LOG_DIR}/deploy.log"
    local main_log_file="${LOG_DIR}/deploy.log"
    
    # 记录部署开始
    log "$main_log_file" "开始部署 ${BASE_PROJECT_NAME} 项目"
    log "$main_log_file" "当前环境: ${ENV}"
    
    # 确保虚拟环境目录存在
    check_dir "$VENV_DIR" "$main_log_file"
    
    # 安装Python和uvicorn
    log "$main_log_file" "安装Python和uvicorn"
    run_cmd "sudo yum install -y python3-pip" "安装Python pip失败" "$main_log_file"
    run_cmd "sudo pip3 install -i \"$PIP_MIRROR\" uvicorn" "安装uvicorn失败" "$main_log_file"
    
    # 进入虚拟环境目录
    run_cmd "cd \"$VENV_DIR\"" "无法进入目录 $VENV_DIR" "$main_log_file"
    
    # 检查uv命令是否可用
    log "$main_log_file" "创建虚拟环境"
    command -v uv >/dev/null 2>&1 || {
        log "$main_log_file" "错误: 未找到uv命令，请先安装uv"
        exit 1
    }
    
    # 创建虚拟环境
    if [ ! -d "${VENV_DIR}/bin" ]; then
        run_cmd "uv venv \"$VENV_DIR\" --python \"$PYTHON_VERSION\"" "创建虚拟环境失败" "$main_log_file"
    else
        log "$main_log_file" "虚拟环境已存在，跳过创建"
    fi
    
    # 激活虚拟环境
    log "$main_log_file" "激活虚拟环境"
    # shellcheck source=/dev/null
    source "${VENV_DIR}/bin/activate" || {
        log "$main_log_file" "错误: 激活虚拟环境失败"
        exit 1
    }

    # 安装pip
    run_cmd "python3 -m ensurepip" "安装pip失败" "$main_log_file"
    
    # 安装项目依赖
    log "$main_log_file" "虚拟环境安装项目依赖"
    run_cmd "cd \"$BACKEND_DIR\"" "无法进入项目目录 $BACKEND_DIR" "$main_log_file"
    run_cmd "pip3 install -i \"$PIP_MIRROR\" -r requirements.txt" "安装项目依赖失败" "$main_log_file"
}

# 配置和重启Nginx
setup_nginx() {
    local main_log_file="${LOG_DIR}/deploy.log"
    
    # 根据环境选择合适的nginx配置文件
    local nginx_config=""
    case "$ENV" in
        "prod")
            nginx_config="prod-chat-nova.conf"
            log "$main_log_file" "使用生产环境Nginx配置文件: ${nginx_config}"
            ;;
        "beta")
            nginx_config="beta-chat-nova.conf"
            log "$main_log_file" "使用测试环境Nginx配置文件: ${nginx_config}"
            ;;
        *)
            nginx_config="beta-chat-nova.conf"
            log "$main_log_file" "使用默认测试环境Nginx配置文件: ${nginx_config}"
            ;;
    esac
    
    # 拷贝nginx配置文件
    log "$main_log_file" "拷贝 ${BASE_PROJECT_NAME} nginx配置文件 (${nginx_config})"
    run_cmd "/bin/cp -f \"${PROJECT_DIR}/deploy/nginx/${nginx_config}\" \"/etc/nginx/conf.d/${BASE_PROJECT_NAME}-nova.conf\"" "${BASE_PROJECT_NAME} nginx配置文件拷贝失败" "$main_log_file"
    
    # 检测nginx配置
    log "$main_log_file" "检测nginx配置"
    run_cmd "sudo nginx -t" "nginx配置检测失败" "$main_log_file"
    
    # 重启nginx
    log "$main_log_file" "重启nginx"
    run_cmd "sudo nginx -s reload" "重启nginx失败" "$main_log_file"
}

# 配置并更新Supervisor
setup_supervisor() {
    local main_log_file="${LOG_DIR}/deploy.log"
    
    log "$main_log_file" "开始配置Supervisor (环境: ${ENV})"
    
    # 检测操作系统类型
    if [ -f /etc/debian_version ]; then
        OS_TYPE="debian"
        SUPERVISOR_CONF_DIR="/etc/supervisor/conf.d"
        SUPERVISOR_SERVICE="supervisor"
    elif [ -f /etc/redhat-release ]; then
        OS_TYPE="redhat"
        SUPERVISOR_CONF_DIR="/etc/supervisord.d"
        SUPERVISOR_SERVICE="supervisord"
    else
        log "$main_log_file" "不支持的操作系统类型"
        return 1
    fi
    
    # 检查Supervisor是否已安装
    if ! command -v supervisorctl &> /dev/null; then
        log "$main_log_file" "Supervisor未安装，正在安装..."
        if [ "$OS_TYPE" = "debian" ]; then
            sudo apt-get update
            sudo apt-get install -y supervisor
        elif [ "$OS_TYPE" = "redhat" ]; then
            sudo yum install -y epel-release
            sudo yum install -y supervisor
        fi
        
        # 启动Supervisor服务
        sudo systemctl enable ${SUPERVISOR_SERVICE}
        sudo systemctl start ${SUPERVISOR_SERVICE}
        log "$main_log_file" "Supervisor安装完成"
    else
        log "$main_log_file" "Supervisor已安装"
    fi
    
    # 设置脚本执行权限
    log "$main_log_file" "设置脚本执行权限"
    chmod +x ${SUPERVISOR_DIR}/scripts/*.sh
    
    # 根据环境变量选择要部署的配置文件
    local config_pattern=""
    case "$ENV" in
        "prod")
            log "$main_log_file" "部署生产环境配置"
            config_pattern="${PROJECT_NAME}-prod-*.ini"  # 匹配生产环境配置
            ;;
        "beta")
            log "$main_log_file" "部署测试环境配置"
            config_pattern="${PROJECT_NAME}-beta-*.ini"  # 匹配测试环境配置
            ;;
        *)
            log "$main_log_file" "环境值 '$ENV' 无效，默认使用测试环境配置"
            config_pattern="${PROJECT_NAME}-beta-*.ini"
            ;;
    esac
    
    # 删除所有现有的项目相关配置
    log "$main_log_file" "删除现有配置文件链接"
    sudo rm -f ${SUPERVISOR_CONF_DIR}/${PROJECT_NAME}-*.ini
    
    # 创建符号链接
    for conf_file in $(find ${SUPERVISOR_DIR}/conf -name "${config_pattern}" 2>/dev/null); do
        filename=$(basename "$conf_file")
        target="${SUPERVISOR_CONF_DIR}/${filename}"
        
        # 创建新链接
        sudo ln -sf "$conf_file" "$target"
        log "$main_log_file" "创建配置链接: $conf_file -> $target"
    done
    
    # 检查是否有配置文件被复制
    local conf_count=$(ls -1 ${SUPERVISOR_CONF_DIR}/${PROJECT_NAME}-*.ini 2>/dev/null | wc -l)
    if [ "$conf_count" -eq 0 ]; then
        log "$main_log_file" "警告: 没有配置文件被复制，请检查配置目录和匹配模式"
    else
        log "$main_log_file" "成功复制 ${conf_count} 个配置文件"
    fi
    
    # 重新加载Supervisor配置
    log "$main_log_file" "重新加载Supervisor配置"
    sudo supervisorctl reread
    sudo supervisorctl update
    sudo supervisorctl restart ${PROJECT_NAME}-${ENV}-user
    
    # 显示服务状态
    log "$main_log_file" "当前服务状态:"
    sudo supervisorctl status
    
    log "$main_log_file" "Supervisor配置完成"
}

# 主流程
# 确保必要的目录存在
check_dir "${RUN_DIR}" "/tmp/deploy.log"
check_dir "${LOG_DIR}" "/tmp/deploy.log"

# 设置环境
setup_environment

# 配置Supervisor
setup_supervisor

# 配置和重启Nginx
# setup_nginx

log "${LOG_DIR}/deploy.log" "部署完成"
