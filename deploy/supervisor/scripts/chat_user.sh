#!/bin/bash
# 图书馆用户端服务启动脚本 (Unix Socket版本)

# 设置环境变量和目录
PROJECT_NAME="chat"
ENV=${APP_ENV:-"prod"}
BACKEND_DIR="/opt/works/${PROJECT_NAME}/backend"
LOG_DIR="/opt/logs/${PROJECT_NAME}"
RUN_DIR="/opt/run/${PROJECT_NAME}"
SOCKET_FILE="${RUN_DIR}/uvicorn_user.sock"

# 确保日志目录和运行目录存在
mkdir -p ${LOG_DIR}
mkdir -p ${RUN_DIR}

# 删除可能存在的旧Socket文件
if [ -S "${SOCKET_FILE}" ]; then
  rm -f "${SOCKET_FILE}"
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 删除旧的Socket文件: ${SOCKET_FILE}"
fi

# 输出启动信息
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 启动AI聊天服务(Socket)，环境: ${ENV}"

# 激活虚拟环境
source /opt/venv/${PROJECT_NAME}/bin/activate

# 进入后端目录
cd ${BACKEND_DIR}

# 使用环境变量启动应用
# 这里不使用 nohup 和 &，因为 supervisor 会管理进程
APP_ENV="${ENV}" exec python run.py \
  -H 127.0.0.1 \
  -p 9084