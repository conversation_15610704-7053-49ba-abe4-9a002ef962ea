[program:chat-beta-user]
; 图书馆测试环境用户端服务
command=/opt/works/chat/deploy/supervisor/scripts/chat_user.sh
directory=/opt/works/chat/backend
autostart=true
autorestart=true
startsecs=5
startretries=3
stopwaitsecs=10
user=user00
environment=APP_ENV="beta"
redirect_stderr=true
stdout_logfile=/opt/logs/chat/chat-user.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
stdout_capture_maxbytes=1MB
stdout_events_enabled=false
stderr_logfile=/opt/logs/chat/chat-user-error.log
stderr_logfile_maxbytes=50MB
stderr_logfile_backups=10
stderr_capture_maxbytes=1MB
stderr_events_enabled=false 