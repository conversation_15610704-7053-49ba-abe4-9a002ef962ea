# Supervisor 部署配置

本目录包含使用 Supervisor 管理图书馆服务的配置文件和启动脚本。

## 目录结构

- `conf/` - Supervisor 配置文件
- `scripts/` - 启动脚本

## 配置文件说明

- **生产环境**
  - `lib-prod-user.conf` - 用户端服务配置
  - `lib-prod-admin.conf` - 管理端服务配置

- **测试环境**
  - `lib-beta-user.conf` - 测试环境用户端服务配置
  - `lib-beta-admin.conf` - 测试环境管理端服务配置

## 启动脚本说明

- `lib_user.sh` - 用户端 Unix Socket 服务启动脚本
- `lib_admin.sh` - 管理端 Unix Socket 服务启动脚本

## 使用方法

### 安装 Supervisor

```bash
# CentOS/RHEL
sudo yum install supervisor

# Ubuntu/Debian
sudo apt-get install supervisor
```

### 配置 Supervisor

根据不同环境部署配置文件：

```bash
# 生产环境
APP_ENV=prod ./deploy/scripts/update-supervisor.sh

# 测试环境
APP_ENV=beta ./deploy/scripts/update-supervisor.sh
# 或直接使用（默认为beta环境）
./deploy/scripts/update-supervisor.sh
```

也可以直接在命令行参数指定环境：

```bash
# 生产环境
./deploy/scripts/update-supervisor.sh prod

# 测试环境
./deploy/scripts/update-supervisor.sh beta
```

### 启动和管理服务

```bash
# 启动所有服务
# 生产环境
sudo supervisorctl start lib:*
# 测试环境
sudo supervisorctl start lib-beta:*

# 重启服务
sudo supervisorctl restart lib:*   # 生产环境
sudo supervisorctl restart lib-beta:* # 测试环境

# 查看服务状态
sudo supervisorctl status
```

## 环境变量

服务启动时会使用 `APP_ENV` 环境变量来指定运行环境：

- `prod` - 生产环境
- `beta` - 测试环境

配置文件部署时会根据 `APP_ENV` 变量选择部署不同的配置文件：
- 当 `APP_ENV=prod` 时，会部署生产环境配置（lib-prod开头的配置文件）
- 当 `APP_ENV=beta` 时，会部署测试环境配置（lib-beta开头的配置文件）

每个服务的配置文件中已经设置了对应的环境变量，启动脚本会自动读取这些环境变量。 